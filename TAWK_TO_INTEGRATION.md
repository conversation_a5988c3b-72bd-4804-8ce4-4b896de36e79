# Tawk.to 实时聊天集成文档

## 概述

本项目已成功集成 Tawk.to 实时聊天功能，为用户提供即时客户支持服务。

## 集成组件

### 1. TawkToChat 组件
位置：`components/TawkToChat/TawkToChat.tsx`

这是一个专门的 React 组件，负责：
- 动态加载 Tawk.to 脚本
- 处理 SSR（服务器端渲染）兼容性
- 提供配置选项和自定义功能
- 确保脚本只加载一次

### 2. 配置文件
位置：`graphcommerce.config.js`

添加了 `tawkTo` 配置节，包含：
```javascript
tawkTo: {
  enabled: true,
  propertyId: '686fc14861b9c5190dd25f8c',
  widgetId: '1ivq8m1u7',
  customConfig: {
    // hideOnPages: ['/checkout', '/account/signin'], // 隐藏聊天的页面
    // position: 'bottom-right', // 小部件位置
    // customStyle: {
    //   visibility: {
    //     desktop: true,
    //     mobile: true
    //   }
    // }
  }
}
```

### 3. 布局集成
已集成到以下布局组件：
- `components/Layout/LayoutNavigation.tsx` - 主要导航布局
- `components/Layout/LayoutMinimal.tsx` - 最小化布局

## 功能特性

### ✅ 已实现功能
- [x] 动态脚本加载
- [x] SSR 兼容性
- [x] 配置化启用/禁用
- [x] 自定义 Property ID 和 Widget ID
- [x] 防止重复加载
- [x] 跨页面持久化
- [x] 移动端和桌面端支持

### 🔧 可配置选项
- **enabled**: 启用/禁用聊天功能
- **propertyId**: Tawk.to 属性 ID
- **widgetId**: Tawk.to 小部件 ID
- **hideOnPages**: 指定隐藏聊天的页面路径
- **position**: 聊天小部件位置（需要 Tawk.to Pro）
- **visibility**: 桌面端/移动端显示控制

## 使用方法

### 基本使用
聊天功能已自动集成到所有页面，无需额外配置。

### 自定义配置
修改 `graphcommerce.config.js` 中的 `tawkTo` 配置：

```javascript
// 禁用聊天功能
tawkTo: {
  enabled: false,
  // ...其他配置
}

// 在特定页面隐藏聊天
tawkTo: {
  enabled: true,
  customConfig: {
    hideOnPages: ['/checkout', '/account/signin', '/admin']
  }
}

// 仅在桌面端显示
tawkTo: {
  enabled: true,
  customConfig: {
    customStyle: {
      visibility: {
        desktop: true,
        mobile: false
      }
    }
  }
}
```

### 手动集成到新布局
如果创建新的布局组件，需要手动添加 TawkToChat：

```tsx
import { TawkToChat } from '../TawkToChat'
import graphcommerceConfig from '../../graphcommerce.config'

// 在布局组件的返回部分添加
<TawkToChat
  enabled={graphcommerceConfig.tawkTo?.enabled}
  propertyId={graphcommerceConfig.tawkTo?.propertyId}
  widgetId={graphcommerceConfig.tawkTo?.widgetId}
  customConfig={graphcommerceConfig.tawkTo?.customConfig}
/>
```

## 测试验证

### 开发环境测试
1. 启动开发服务器：`npm run dev`
2. 访问 http://localhost:3001
3. 检查页面右下角是否显示 Tawk.to 聊天小部件
4. 点击聊天小部件验证功能正常

### 网络请求验证
正常工作时应该看到以下网络请求：
- `https://embed.tawk.to/686fc14861b9c5190dd25f8c/1ivq8m1u7` - 主脚本
- `https://va.tawk.to/v1/widget-settings` - 小部件设置
- `https://va.tawk.to/v1/session/start` - 会话启动

### 浏览器控制台
检查浏览器控制台，确保没有 Tawk.to 相关的错误信息。

## 故障排除

### 聊天小部件不显示
1. 检查 `graphcommerce.config.js` 中 `tawkTo.enabled` 是否为 `true`
2. 验证 `propertyId` 和 `widgetId` 是否正确
3. 检查浏览器控制台是否有 JavaScript 错误
4. 确认网络请求是否成功加载 Tawk.to 脚本

### TypeError: t.$_Tawk.i18next is not a function
**已修复** - 这个错误通常由脚本加载时机问题引起。修复方案：
1. 改进了脚本加载逻辑，添加了错误处理
2. 使用 `useRef` 防止重复加载
3. 添加了延迟配置应用，确保 Tawk.to 完全初始化
4. 增强了错误边界处理

如果仍然遇到此错误：
1. 清除浏览器缓存并重新加载页面
2. 检查网络连接是否稳定
3. 确认 Tawk.to 服务是否正常运行

### 页面性能影响
Tawk.to 脚本采用异步加载，不会阻塞页面渲染。如果遇到性能问题：
1. 检查网络连接
2. 考虑在特定页面禁用聊天功能
3. 联系 Tawk.to 支持团队

### SSR 相关问题
组件已处理 SSR 兼容性，如果遇到问题：
1. 确保组件只在客户端执行脚本加载
2. 检查 `typeof window === 'undefined'` 条件
3. 验证 `window.tawkToLoaded` 标志是否正确设置

## 维护说明

### 更新 Tawk.to 配置
1. 登录 Tawk.to 管理面板
2. 获取新的 Property ID 和 Widget ID
3. 更新 `graphcommerce.config.js` 配置
4. 重启开发服务器

### 版本升级
Tawk.to 脚本会自动使用最新版本，无需手动更新。

## 支持联系

如有技术问题，请联系：
- Tawk.to 官方支持：https://www.tawk.to/support/
- 项目开发团队：通过项目 Issue 系统

## 更新日志

### v1.1.0 (2025-01-10)
- 🐛 **修复**: 解决了 "TypeError: t.$_Tawk.i18next is not a function" 错误
- 🔧 **改进**: 增强了脚本加载逻辑和错误处理
- 🛡️ **安全**: 添加了防重复加载机制
- ⚡ **性能**: 优化了初始化时机和配置应用

### v1.0.0 (2025-01-10)
- 🎉 **初始版本**: 完成 Tawk.to 实时聊天集成
- ✨ **功能**: 支持 SSR、配置化管理、跨页面持久化
- 📱 **兼容**: 支持桌面端和移动端

---

**集成完成日期**: 2025-01-10
**当前版本**: v1.1.0
**测试状态**: ✅ 通过（包括错误修复验证）
