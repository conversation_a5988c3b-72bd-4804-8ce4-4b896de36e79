import { GetStaticProps } from 'next'
import { Box, Typography, List, ListItem, ListItemText } from '@mui/material'
import { LayoutNavigation, LayoutNavigationProps } from '../../components'
import { LayoutDocument } from '../../components/Layout/Layout.gql'
import { graphqlSsrClient } from '../../lib/graphql/graphqlSsrClient'

type MenuSimpleProps = LayoutNavigationProps

export default function MenuSimplePage(props: MenuSimpleProps) {
  const { menu } = props
  
  return (
    <LayoutNavigation {...props}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Menu Position Test
        </Typography>
        
        <Typography variant="body1" paragraph>
          This page tests the menu position functionality. Check the navigation menu above to see if items are sorted by position.
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Menu Items (should be sorted by position):
          </Typography>
          
          {menu?.items?.[0]?.children ? (
            <List>
              {menu.items[0].children
                .filter((item: any) => item.include_in_menu)
                .sort((a: any, b: any) => {
                  const positionA = a.position || 0
                  const positionB = b.position || 0
                  return positionA - positionB
                })
                .map((item: any) => (
                  <ListItem key={item.uid}>
                    <ListItemText
                      primary={item.name}
                      secondary={`Position: ${item.position || 'N/A'} | URL: /${item.url_path}`}
                    />
                  </ListItem>
                ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No menu items found
            </Typography>
          )}
        </Box>
        
        <Box sx={{ mt: 4, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="h6" gutterBottom>
            Test Instructions:
          </Typography>
          <Typography variant="body2" paragraph>
            1. Check the main navigation menu at the top of the page
          </Typography>
          <Typography variant="body2" paragraph>
            2. Menu items should be sorted by their position value (ascending)
          </Typography>
          <Typography variant="body2" paragraph>
            3. Items with the same position should be sorted alphabetically
          </Typography>
          <Typography variant="body2" paragraph>
            4. If position is not available, items should default to position 0
          </Typography>
        </Box>
      </Box>
    </LayoutNavigation>
  )
}

export const getStaticProps: GetStaticProps = async (context) => {
  const client = graphqlSsrClient(context)
  
  const layout = await client.query({
    query: LayoutDocument,
  })
  
  return {
    props: {
      ...layout.data,
    },
    revalidate: 60 * 20, // 20 minutes
  }
} 