import { GetStaticProps } from 'next'
import { Box, Typography, Card, CardContent, Accordion, AccordionSummary, AccordionDetails } from '@mui/material'
import { ExpandMore } from '@mui/icons-material'
import { LayoutNavigation, LayoutNavigationProps } from '../../components'
import { LayoutDocument } from '../../components/Layout/Layout.gql'
import { graphqlSsrClient } from '../../lib/graphql/graphqlSsrClient'

type MenuDebugProps = LayoutNavigationProps

// Recursive component to display menu structure
function MenuItemDebug({ item, level = 0 }: { item: any; level?: number }) {
  const hasChildren = item.children && item.children.length > 0
  
  return (
    <Card sx={{ mb: 1, ml: level * 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" component="div">
            {item.name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Position: {item.position || 'N/A'}
          </Typography>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          UID: {item.uid}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          URL: /{item.url_path}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Level: {item.level || 'N/A'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Include in Menu: {item.include_in_menu ? 'Yes' : 'No'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Children Count11: {item.childrenCount || 0}
        </Typography>
        
        {/* Display menu image if available */}
        {item.menu_image && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Menu Image:
            </Typography>
            <Box
              component="img"
              src={item.menu_image}
              alt={`${item.name} menu icon`}
              sx={{
                width: 64,
                height: 64,
                objectFit: 'cover',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'grey.100',
              }}
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
            <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
              Path: {item.menu_image}
            </Typography>
          </Box>
        )}
        
        {!item.menu_image && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            No menu image available
          </Typography>
        )}
        
        {hasChildren && (
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>Children ({item.children.length})</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {item.children.map((child: any) => (
                <MenuItemDebug key={child.uid} item={child} level={level + 1} />
              ))}
            </AccordionDetails>
          </Accordion>
        )}
      </CardContent>
    </Card>
  )
}

export default function MenuDebugPage(props: MenuDebugProps) {
  const { menu } = props
  
  // Process menu data similar to LayoutNavigation
  const processMenuStructure = (items: any[]): any[] => {
    if (!items || items.length === 0) return []
    
    const filterMenuItems = (items: any[], currentLevel: number = 1): any[] => {
      if (!items) return []
     
      return items
        .filter(item => {
          const includeInMenu = item?.include_in_menu
          return includeInMenu === true || includeInMenu === 1 || includeInMenu === '1'
        })
        .map(item => {
          const children = item.children ? filterMenuItems(item.children, currentLevel + 1) : undefined
          
          const calculateChildrenCount = (item: any): number => {
            if (!item.children || item.children.length === 0) return 0
            let count = item.children.length
            for (const child of item.children) {
              count += calculateChildrenCount(child)
            }
            return count
          }
          
          const menuItem = {
            uid: item.uid,
            name: item.name,
            url_path: item.url_path,
            include_in_menu: item.include_in_menu,
            position: item.position,
            menu_image: item.menu_image,
            children,
            level: currentLevel,
            childrenCount: calculateChildrenCount({ children }),
          }
          
          return menuItem
        })
    }
    
    let menuItems = items.length === 1 && items[0].children 
      ? filterMenuItems(items[0].children)
      : filterMenuItems(items)
    
    // Sort by position
    const sortMenuItems = (items: any[]) => {
      items.sort((a, b) => {
        const positionA = a.position || 0
        const positionB = b.position || 0
        
        if (positionA !== positionB) {
          return positionA - positionB
        }
        
        return (a.name || '').localeCompare(b.name || '')
      })
      
      items.forEach(item => {
        if (item.children && item.children.length > 0) {
          sortMenuItems(item.children)
        }
      })
      
      return items
    }
    
    return sortMenuItems(menuItems)
  }
  
  const processedMenu = processMenuStructure(menu?.items || [])
  
  return (
    <LayoutNavigation {...props}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Menu Structure Debug
        </Typography>
        
        <Typography variant="body1" paragraph>
          This page displays the complete menu structure with position information for debugging purposes.
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Raw Menu Data Summary:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total root items: {menu?.items?.length || 0}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            First level children: {menu?.items?.[0]?.children?.length || 0}
          </Typography>
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Processed Menu Items (sorted by position):
          </Typography>
          
          {/* Summary of images */}
          <Box sx={{ mb: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
            <Typography variant="body2" gutterBottom>
              <strong>Menu Images Summary:</strong>
            </Typography>
            <Typography variant="body2">
              Items with images: {processedMenu.filter(item => item.menu_image).length}
            </Typography>
            <Typography variant="body2">
              Items without images: {processedMenu.filter(item => !item.menu_image).length}
            </Typography>
          </Box>
          
          {processedMenu.length > 0 ? (
            processedMenu.map((item: any) => (
              <MenuItemDebug key={item.uid} item={item} />
            ))
          ) : (
            <Typography variant="body2" color="text.secondary">
              No menu items found or processed
            </Typography>
          )}
        </Box>
        
        <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
          <Typography variant="h6" gutterBottom>
            Raw Menu JSON (for debugging):
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', overflow: 'auto' }}>
            {JSON.stringify(menu, null, 2)}
          </Typography>
        </Box>
      </Box>
    </LayoutNavigation>
  )
}

export const getStaticProps: GetStaticProps = async (context) => {
  const client = graphqlSsrClient(context)
  
  const layout = await client.query({
    query: LayoutDocument,
  })
  
  return {
    props: {
      ...layout.data,
    },
    revalidate: 60 * 20, // 20 minutes
  }
} 