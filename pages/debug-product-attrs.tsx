import React, { useState } from 'react'
import { GetStaticProps } from 'next'
import { Container, Typography, Box, Button, TextField, Paper } from '@mui/material'
import { graphqlSsrClient } from '../lib/graphql/graphqlSsrClient'
import { gql } from '@apollo/client'

const DEBUG_QUERY = gql`
  query DebugProductAttrs($urlKey: String!, $useCustomAttributes: Boolean = true) {
    products(filter: { url_key: { eq: $urlKey } }) {
      items {
        __typename
        uid
        name
        sku
        url_key
        mpn
        colour
        shape
        overflow_cap
        waste_outlet_diameter
        tap_hole_diameter
        tap_hole
        excludes
        includes
        brand
        ean
        glass_thickness
        coating
        model
        certification
        pan_material
        Length
        # Add more comprehensive product attributes
        id
        attribute_set_id
        canonical_url
        categories {
          uid
          name
          url_path
        }
        country_of_manufacture
        created_at
        gift_message_available
        manufacturer
        meta_description
        meta_keyword
        meta_title
        new_from_date
        new_to_date
        only_x_left_in_stock
        options_container
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
            discount {
              amount_off
              percent_off
            }
          }
          maximum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
            discount {
              amount_off
              percent_off
            }
          }
        }
        product_links {
          position
          link_type
          linked_product_sku
          linked_product_type
        }
        rating_summary
        review_count
        short_description {
          html
        }
        description {
          html
        }
        special_from_date
        special_to_date
        stock_status
        swatch_image
        thumbnail {
          url
          label
        }
        tier_prices {
          customer_group_id
          percentage_value
          qty
          value
          website_id
        }
        type_id
        updated_at
        url_suffix
        websites {
          id
          name
          code
        }
        ... on ConfigurableProduct {
          configurable_options {
            attribute_code
            attribute_id
            id
            label
            position
            product_id
            use_default
            values {
              default_label
              label
              store_label
              use_default_value
              value_index
            }
          }
          variants {
            attributes {
              code
              value_index
            }
            product {
              uid
              sku
              name
              price_range {
                minimum_price {
                  final_price {
                    value
                    currency
                  }
                }
              }
            }
          }
        }
        custom_attributesV2(filters: { is_visible_on_front: true }) @include(if: $useCustomAttributes) {
          items {
            code
            __typename
            ... on AttributeValue {
              value
            }
            ... on AttributeSelectedOptions {
              selected_options {
                label
                value
              }
            }
          }
        }
        ... on SimpleProduct {
          weight
        }
        ... on BundleProduct {
          dynamic_price
          dynamic_sku
          dynamic_weight
          price_view
          ship_bundle_items
        }
        ... on DownloadableProduct {
          downloadable_product_links {
            id
            title
            sort_order
            is_shareable
            price
            number_of_downloads
            link_type
            sample_type
          }
          downloadable_product_samples {
            id
            title
            sort_order
            sample_type
          }
          links_purchased_separately
          links_title
        }
        ... on GroupedProduct {
          items {
            position
            qty
            product {
              uid
              name
              sku
              price_range {
                minimum_price {
                  final_price {
                    value
                    currency
                  }
                }
              }
            }
          }
        }
        ... on VirtualProduct {
          # Virtual products don't have additional specific fields
          gift_message_available
        }
      }
    }
  }
`

interface Props {
  initialData?: any
}

export default function DebugProductAttrs({ initialData }: Props) {
  const [urlKey, setUrlKey] = useState('frameless-sliding-shower-door-bracket-mount')
  const [productData, setProductData] = useState(initialData)
  const [loading, setLoading] = useState(false)

  const fetchProductData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: DEBUG_QUERY,
          variables: { urlKey, useCustomAttributes: true },
        }),
      })
      
      const result = await response.json()
      setProductData(result.data)
    } catch (error) {
      console.error('Error fetching product data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Product Attributes Debug Tool
      </Typography>
      
      <Box sx={{ mb: 4, display: 'flex', gap: 2, alignItems: 'center' }}>
        <TextField
          label="Product URL Key"
          value={urlKey}
          onChange={(e) => setUrlKey(e.target.value)}
          sx={{ flexGrow: 1 }}
        />
        <Button 
          variant="contained" 
          onClick={fetchProductData}
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Fetch Data'}
        </Button>
      </Box>

      {productData?.products?.items?.[0] && (
        <Box sx={{ display: 'grid', gap: 3 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom color="primary">
              Basic Product Information
            </Typography>
            <Box component="pre" sx={{ 
              fontSize: '0.875rem', 
              overflow: 'auto',
              bgcolor: 'grey.50',
              p: 2,
              borderRadius: 1
            }}>
              {JSON.stringify({
                __typename: productData.products.items[0].__typename,
                uid: productData.products.items[0].uid,
                name: productData.products.items[0].name,
                sku: productData.products.items[0].sku,
                url_key: productData.products.items[0].url_key,
                mpn: productData.products.items[0].mpn,
                brand: productData.products.items[0].brand,
                ean: productData.products.items[0].ean,
                colour: productData.products.items[0].colour,
                shape: productData.products.items[0].shape,
                model: productData.products.items[0].model,
                certification: productData.products.items[0].certification,
                pan_material: productData.products.items[0].pan_material,
                Length: productData.products.items[0].Length,
                glass_thickness: productData.products.items[0].glass_thickness,
                coating: productData.products.items[0].coating,
                overflow_cap: productData.products.items[0].overflow_cap,
                waste_outlet_diameter: productData.products.items[0].waste_outlet_diameter,
                tap_hole_diameter: productData.products.items[0].tap_hole_diameter,
                tap_hole: productData.products.items[0].tap_hole,
                includes: productData.products.items[0].includes,
                excludes: productData.products.items[0].excludes,
                weight: productData.products.items[0].weight,
              }, null, 2)}
            </Box>
          </Paper>

          {productData.products.items[0].configurable_options && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom color="secondary">
                Configurable Options
              </Typography>
              <Box component="pre" sx={{ 
                fontSize: '0.875rem', 
                overflow: 'auto',
                bgcolor: 'grey.50',
                p: 2,
                borderRadius: 1
              }}>
                {JSON.stringify(productData.products.items[0].configurable_options, null, 2)}
              </Box>
            </Paper>
          )}

          {productData.products.items[0].custom_attributesV2 && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom color="success.main">
                Custom Attributes V2
              </Typography>
              <Box component="pre" sx={{ 
                fontSize: '0.875rem', 
                overflow: 'auto',
                bgcolor: 'grey.50',
                p: 2,
                borderRadius: 1
              }}>
                {JSON.stringify(productData.products.items[0].custom_attributesV2, null, 2)}
              </Box>
            </Paper>
          )}

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom color="error.main">
              Full Product Data
            </Typography>
            <Box component="pre" sx={{ 
              fontSize: '0.75rem', 
              overflow: 'auto',
              bgcolor: 'grey.50',
              p: 2,
              borderRadius: 1,
              maxHeight: '400px'
            }}>
              {JSON.stringify(productData.products.items[0], null, 2)}
            </Box>
          </Paper>
        </Box>
      )}

      {productData && !productData.products?.items?.length && (
        <Typography color="error">
          No product found with URL key: {urlKey}
        </Typography>
      )}
    </Container>
  )
}

export const getStaticProps: GetStaticProps = async () => {
  try {
    const client = graphqlSsrClient({ locale: 'en' })
    
    console.log('=== DEBUGGING PRODUCT ATTRIBUTES ===')
    console.log('URL Key: frameless-sliding-shower-door-bracket-mount')
    
    const { data } = await client.query({
      query: DEBUG_QUERY,
      variables: { urlKey: 'frameless-sliding-shower-door-bracket-mount', useCustomAttributes: true },
    })
    
    const product = data?.products?.items?.[0]
    if (product) {
      console.log('\n=== BASIC PRODUCT INFO ===')
      console.log('Product Name:', product.name)
      console.log('SKU:', product.sku)
      console.log('Type ID:', product.type_id)
      console.log('Product ID:', product.id)
      console.log('UID:', product.uid)
      console.log('Brand:', product.brand)
      console.log('EAN:', product.ean)
      console.log('Model:', product.model)
      console.log('Manufacturer:', product.manufacturer)
      console.log('Country of Manufacture:', product.country_of_manufacture)
      console.log('Colour:', product.colour)
      console.log('Shape:', product.shape)
      console.log('Certification:', product.certification)
      console.log('Glass Thickness:', product.glass_thickness)
      console.log('Coating:', product.coating)
      console.log('Material:', product.pan_material)
      console.log('Length:', product.Length)
      console.log('Weight:', product.weight)
      console.log('Stock Status:', product.stock_status)
      console.log('Only X Left in Stock:', product.only_x_left_in_stock)
      console.log('Created At:', product.created_at)
      console.log('Updated At:', product.updated_at)
      
      console.log('\n=== CATEGORIES ===')
      if (product.categories?.length) {
        product.categories.forEach((cat: any, index: number) => {
          console.log(`Category ${index + 1}:`, cat.name, `(${cat.url_path})`)
        })
      } else {
        console.log('No categories')
      }
      
      console.log('\n=== PRICE INFO ===')
      if (product.price_range) {
        console.log('Min Price:', product.price_range.minimum_price?.final_price?.value, product.price_range.minimum_price?.final_price?.currency)
        console.log('Max Price:', product.price_range.maximum_price?.final_price?.value, product.price_range.maximum_price?.final_price?.currency)
        console.log('Regular Price:', product.price_range.minimum_price?.regular_price?.value, product.price_range.minimum_price?.regular_price?.currency)
      }
      
      console.log('\n=== CONFIGURABLE OPTIONS ===')
      if (product.configurable_options) {
        product.configurable_options.forEach((option: any, index: number) => {
          console.log(`Option ${index + 1}:`, option.label || option.attribute_code)
          console.log('  Attribute Code:', option.attribute_code)
          console.log('  Attribute ID:', option.attribute_id)
          console.log('  Position:', option.position)
          if (option.values) {
            console.log('  Values:')
            option.values.forEach((v: any, vIndex: number) => {
              console.log(`    ${vIndex + 1}. ${v.store_label} (index: ${v.value_index})`)
            })
          }
        })
        
        if (product.variants) {
          console.log('\n=== PRODUCT VARIANTS ===')
          product.variants.forEach((variant: any, index: number) => {
            console.log(`Variant ${index + 1}:`)
            console.log('  SKU:', variant.product?.sku)
            console.log('  Name:', variant.product?.name)
            console.log('  Price:', variant.product?.price_range?.minimum_price?.final_price?.value)
            if (variant.attributes) {
              console.log('  Attributes:')
              variant.attributes.forEach((attr: any) => {
                console.log(`    ${attr.code}: ${attr.value_index}`)
              })
            }
          })
        }
      } else {
        console.log('No configurable options')
      }
      
      console.log('\n=== CUSTOM ATTRIBUTES V2 ===')
      if (product.custom_attributesV2?.items) {
        product.custom_attributesV2.items.forEach((attr: any, index: number) => {
          console.log(`Attribute ${index + 1}:`)
          console.log('  Code:', attr.code)
          console.log('  Type:', attr.__typename)
          if (attr.value) {
            console.log('  Value:', attr.value)
          }
          if (attr.selected_options) {
            console.log('  Selected Options:', attr.selected_options.map((opt: any) => opt.label).join(', '))
          }
        })
      } else {
        console.log('No custom attributes')
      }
      
      console.log('\n=== FULL PRODUCT DATA ===')
      console.log(JSON.stringify(product, null, 2))
    } else {
      console.log('No product found!')
    }
    
    return {
      props: {
        initialData: data,
      },
      revalidate: 60,
    }
  } catch (error) {
    console.error('Error fetching initial data:', error)
    console.error('Error details:', error)
    return {
      props: {
        initialData: null,
        error: error.message,
      },
      revalidate: 60,
    }
  }
} 