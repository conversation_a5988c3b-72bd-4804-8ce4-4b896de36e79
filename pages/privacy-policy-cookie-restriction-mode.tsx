import { PageOptions } from '@graphcommerce/framer-next-pages'
import { HygraphPagesQuery } from '@graphcommerce/hygraph-ui'
import { StoreConfigDocument } from '@graphcommerce/magento-store'
import {
  Container,
  PageMeta,
  GetStaticProps,
  LayoutTitle,
  LayoutHeader,
  Breadcrumbs,
} from '@graphcommerce/next-ui'
import { 
  Typography, 
  Box,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  List,
  ListItem,
  Alert
} from '@mui/material'
import { LayoutDocument, LayoutNavigation, LayoutNavigationProps } from '../components'
import { graphqlSsrClient, graphqlSharedClient } from '../lib/graphql/graphqlSsrClient'

type Props = HygraphPagesQuery

const cookieData = [
  {
    name: 'FORM_KEY',
    description: 'Stores randomly generated key used to prevent forged requests.'
  },
  {
    name: 'PHPSE<PERSON><PERSON>',
    description: 'Your session ID on the server.'
  },
  {
    name: 'GUEST-VIEW',
    description: 'Allows guests to view and edit their orders.'
  },
  {
    name: 'PERSISTENT_SHOPPING_CART',
    description: 'A link to information about your cart and viewing history, if you have asked for this.'
  },
  {
    name: 'STF',
    description: 'Information on products you have emailed to friends.'
  },
  {
    name: 'STORE',
    description: 'The store view or language you have selected.'
  },
  {
    name: 'USER_ALLOWED_SAVE_COOKIE',
    description: 'Indicates whether a customer allowed to use cookies.'
  },
  {
    name: 'MAGE-CACHE-SESSID',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'MAGE-CACHE-STORAGE',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'MAGE-CACHE-STORAGE-SECTION-INVALIDATION',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'MAGE-CACHE-TIMEOUT',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'SECTION-DATA-IDS',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'PRIVATE_CONTENT_VERSION',
    description: 'Facilitates caching of content on the browser to make pages load faster.'
  },
  {
    name: 'X-MAGENTO-VARY',
    description: 'Facilitates caching of content on the server to make pages load faster.'
  },
  {
    name: 'MAGE-TRANSLATION-FILE-VERSION',
    description: 'Facilitates translation of content to other languages.'
  },
  {
    name: 'MAGE-TRANSLATION-STORAGE',
    description: 'Facilitates translation of content to other languages.'
  }
]

function PrivacyPolicyPage(props: Props) {
  return (
    <>
      <PageMeta
        title="Privacy Policy & Cookie Restriction Mode"
        metaDescription="Learn about our privacy policy, cookie usage, and how we protect your personal information when shopping with us."
        metaRobots={['noindex']}
        canonical="/privacy-policy-cookie-restriction-mode"
      />

      <LayoutHeader floatingMd hideMd={import.meta.graphCommerce.breadcrumbs}>
        <LayoutTitle size="small" component="span">
          Privacy Policy & Cookie Restriction Mode
        </LayoutTitle>
      </LayoutHeader>

      <Container maxWidth="lg">
        {import.meta.graphCommerce.breadcrumbs && (
          <Breadcrumbs breadcrumbs={[{ href: '/privacy-policy-cookie-restriction-mode', name: 'Privacy Policy & Cookie Restriction Mode' }]} />
        )}

        <Box sx={{ py: 4 }}>
          <Alert severity="info" sx={{ mb: 4 }}>
            <Typography variant="body1">
              <strong>We use cookies.</strong> For more information, please check our cookie policy below. 
              The store will not work correctly when cookies are disabled.
            </Typography>
          </Alert>

          <Typography variant="h4" component="h1" gutterBottom>
            Privacy and Cookie Policy
          </Typography>
          
          <Typography variant="body1" paragraph>
            This privacy policy sets out how this website (hereafter "the Store") uses and protects any information that you give the Store while using this website. The Store is committed to ensuring that your privacy is protected. Should we ask you to provide certain information by which you can be identified when using this website, then you can be assured that it will only be used in accordance with this privacy statement. The Store may change this policy from time to time by updating this page. You should check this page from time to time to ensure that you are happy with any changes.
          </Typography>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            What we collect
          </Typography>
          <Typography variant="body1" paragraph>
            We may collect the following information:
          </Typography>
          <List sx={{ pl: 2 }}>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">name</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">contact information including email address</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">demographic information such as postcode, preferences and interests</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">other information relevant to customer surveys and/or offers</Typography>
            </ListItem>
          </List>
          <Typography variant="body1" paragraph>
            For the exhaustive list of cookies we collect see the List of cookies we collect section.
          </Typography>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            What we do with the information we gather
          </Typography>
          <Typography variant="body1" paragraph>
            We require this information to understand your needs and provide you with a better service, and in particular for the following reasons:
          </Typography>
          <List sx={{ pl: 2 }}>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">Internal record keeping.</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">We may use the information to improve our products and services.</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">We may periodically send promotional emails about new products, special offers or other information which we think you may find interesting using the email address which you have provided.</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">From time to time, we may also use your information to contact you for market research purposes. We may contact you by email, phone, fax or mail. We may use the information to customise the website according to your interests.</Typography>
            </ListItem>
          </List>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            Security
          </Typography>
          <Typography variant="body1" paragraph>
            We are committed to ensuring that your information is secure. In order to prevent unauthorised access or disclosure, we have put in place suitable physical, electronic and managerial procedures to safeguard and secure the information we collect online.
          </Typography>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            How we use cookies
          </Typography>
          <Typography variant="body1" paragraph>
            A cookie is a small file which asks permission to be placed on your computer's hard drive. Once you agree, the file is added and the cookie helps analyse web traffic or lets you know when you visit a particular site. Cookies allow web applications to respond to you as an individual. The web application can tailor its operations to your needs, likes and dislikes by gathering and remembering information about your preferences.
          </Typography>
          <Typography variant="body1" paragraph>
            We use traffic log cookies to identify which pages are being used. This helps us analyse data about web page traffic and improve our website in order to tailor it to customer needs. We only use this information for statistical analysis purposes and then the data is removed from the system.
          </Typography>
          <Typography variant="body1" paragraph>
            Overall, cookies help us provide you with a better website, by enabling us to monitor which pages you find useful and which you do not. A cookie in no way gives us access to your computer or any information about you, other than the data you choose to share with us. You can choose to accept or decline cookies. Most web browsers automatically accept cookies, but you can usually modify your browser setting to decline cookies if you prefer. This may prevent you from taking full advantage of the website.
          </Typography>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            Links to other websites
          </Typography>
          <Typography variant="body1" paragraph>
            Our website may contain links to other websites of interest. However, once you have used these links to leave our site, you should note that we do not have any control over that other website. Therefore, we cannot be responsible for the protection and privacy of any information which you provide whilst visiting such sites and such sites are not governed by this privacy statement. You should exercise caution and look at the privacy statement applicable to the website in question.
          </Typography>

          <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4 }}>
            Controlling your personal information
          </Typography>
          <Typography variant="body1" paragraph>
            You may choose to restrict the collection or use of your personal information in the following ways:
          </Typography>
          <List sx={{ pl: 2 }}>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">whenever you are asked to fill in a form on the website, look for the box that you can click to indicate that you do not want the information to be used by anybody for direct marketing purposes</Typography>
            </ListItem>
            <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 0 }}>
              <Typography variant="body1">if you have previously agreed to us using your personal information for direct marketing purposes, you may change your mind at any time by letting us know using our Contact Us information</Typography>
            </ListItem>
          </List>
          <Typography variant="body1" paragraph>
            We will not sell, distribute or lease your personal information to third parties unless we have your permission or are required by law to do so. We may use your personal information to send you promotional information about third parties which we think you may find interesting if you tell us that you wish this to happen.
          </Typography>
          <Typography variant="body1" paragraph>
            You may request details of personal information which we hold about you under the Data Protection Act 1998. A small fee will be payable. If you would like a copy of the information held on you please email us this request using our Contact Us information.
          </Typography>
          <Typography variant="body1" paragraph>
            If you believe that any information we are holding on you is incorrect or incomplete, please write to or email us as soon as possible, at the above address. We will promptly correct any information found to be incorrect.
          </Typography>

            {/* List of Cookies */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h5" component="h2" gutterBottom id="list-of-cookies">
                  List of cookies we collect
                </Typography>
                <Typography variant="body1" paragraph>
                  The table below lists the cookies we collect and what information they store:
                </Typography>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Cookie Name</strong></TableCell>
                        <TableCell><strong>Cookie Description</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {cookieData.map((cookie, index) => (
                        <TableRow key={index}>
                          <TableCell component="th" scope="row">
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {cookie.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {cookie.description}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {/* Contact Information */}
        </Box>
      </Container>
    </>
  )
}

PrivacyPolicyPage.pageOptions = {
  Layout: LayoutNavigation,
} as PageOptions

export default PrivacyPolicyPage

export const getStaticProps: GetStaticProps<LayoutNavigationProps> = async (context) => {
  const client = graphqlSharedClient(context)
  const staticClient = graphqlSsrClient(context)

  const conf = client.query({ query: StoreConfigDocument })
  const layout = staticClient.query({ query: LayoutDocument })

  return {
    props: {
      ...(await layout).data,
      apolloState: await conf.then(() => client.cache.extract()),
      variantMd: 'bottom',
      up: { href: '/', title: 'Home' },
    },
  }
} 