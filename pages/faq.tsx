import { PageOptions } from '@graphcommerce/framer-next-pages'
import { hygraphPageContent, HygraphPagesQuery } from '@graphcommerce/hygraph-ui'
import { StoreConfigDocument } from '@graphcommerce/magento-store'
import {
  Container,
  PageMeta,
  GetStaticProps,
  LayoutTitle,
  LayoutHeader,
  Breadcrumbs,
} from '@graphcommerce/next-ui'
import { 
  Typography, 
  Box,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Card,
  CardContent,
  Collapse,
  IconButton,
  Stack,
  alpha
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import SearchIcon from '@mui/icons-material/Search'
import ClearIcon from '@mui/icons-material/Clear'
import { useState, useMemo } from 'react'
import { LayoutDocument, LayoutNavigation, LayoutNavigationProps } from '../components'
import { graphqlSsrClient, graphqlSharedClient } from '../lib/graphql/graphqlSsrClient'

type Props = HygraphPagesQuery

const faqData = [
  {
    category: 'General',
    icon: '🏢',
    color: '#2563eb',
    questions: [
      {
        question: 'How can I contact you?',
        answer: 'We offer dedicated customer support between 9:00am-5:00pm Monday to Friday. Our team are contactable via our onsite live chat, via telephone (01922 627773) or via email: <EMAIL>'
      },
      {
        question: 'Do you have a showroom?',
        answer: 'We currently do not have a full showroom, however we are able to showcase the smaller products upon a visit to our site which can be seen along with opening times given.'
      },
      {
        question: 'Do you have an item in stock?',
        answer: 'We do show up to date stock information for each product on our website. If an item is out of stock please contact our team who are on hand to help with all your product enquiry needs.'
      },
      {
        question: 'Where can I find dimensions and technical information for a product?',
        answer: 'All product specification can be found in the technical specification tab on each product. We also offer a downloads section which will offer all technical drawings and instructions for installation of all products we offer.'
      },
      {
        question: 'Do your prices include VAT?',
        answer: 'All our prices are VAT Inclusive.'
      },
      {
        question: 'Can I get a VAT receipt?',
        answer: 'Not a problem, simply contact our team and request this, we will simply need your order number and postcode.'
      },
      {
        question: 'What methods of payment do you accept?',
        answer: 'We currently accept all major credit and debit cards (found at the bottom of the page).'
      },
      {
        question: 'Does anyone else sell your products?',
        answer: 'We are proud to say our range is totally unique to ourselves, all items are selected by our design team to compliment the range and offer something for each and every bathroom.'
      },
      {
        question: 'Where can I download the instructions?',
        answer: 'The instructions can be downloaded from the website listing or the instructions guide page.'
      }
    ]
  },
  {
    category: 'Order Information',
    icon: '📋',
    color: '#059669',
    questions: [
      {
        question: 'Can I get a copy of my order?',
        answer: 'Yes, you will receive an email confirmation of your order, this can also be requested from us directly via our contact us page.'
      },
      {
        question: 'Where is my order?',
        answer: 'We offer tracking with all our orders, please check your order details for the link to the tracking for your particular item, if you are having any issues our team are on hand to help.'
      },
      {
        question: 'How do I cancel an order?',
        answer: 'To cancel an order please contact our team directly. If the items have not left for dispatch the cancellation can be processed immediately; however, if the goods have left, the cancellation will be processed but cannot be completed until the goods have been returned to our depot. Cancellation charge may apply for order after dispatch.'
      },
      {
        question: 'Do you offer discounts for businesses?',
        answer: 'Please check out discounted products on our offer page.'
      },
      {
        question: 'Can I collect my order?',
        answer: 'If you are looking to collect the order, please leave a note on the order during the checkout process, call our landline or email us to ensure the goods are not prepared for dispatch, as we ship all goods on daily collections it is important to ensure this is stated when the order is placed to prevent any issues.'
      },
      {
        question: 'Can I order an item that is not currently in stock?',
        answer: 'We currently do not accept pre-orders for products, however all customers who sign up for our personal or trade accounts are notified of all returning goods as they are restocked into the range. We can order for you or contact you when the item back in stock.'
      },
      {
        question: 'Can I modify an existing order?',
        answer: 'We are able to modify any order before dispatch, providing we are notified before the 2pm daily delivery cut off. This includes but is not limited to address alterations.'
      }
    ]
  },
  {
    category: 'Delivery Information',
    icon: '🚚',
    color: '#dc2626',
    questions: [
      {
        question: 'Do you deliver internationally?',
        answer: 'We currently do not deliver outside of the UK Mainlands, for further details please see our shipping information. We do accept orders for our international customers however the carriage would need to be arranged by the customer.'
      },
      {
        question: 'How much does delivery cost?',
        answer: 'We offer free delivery for all customers in England, Wales and the Scotland Lowlands on single delivery items less than 20kg. For 2man delivery, shipping surcharges apply based on postcode areas.'
      },
      {
        question: 'Do you deliver on a weekend?',
        answer: 'We currently offer only a business day delivery service at the moment.'
      },
      {
        question: 'Can I request a specific delivery date?',
        answer: 'As all our single man items are sent on the next business day service this would be something we are able to hold if informed before dispatch to have a specific day delivery. With our 2 man items, we don\'t allow for any specified delivery.'
      },
      {
        question: 'My order is delayed, what do I do?',
        answer: 'Please call or email us, we will contact the courier to retrieve any further updates.'
      },
      {
        question: 'Will I be contacted before delivery?',
        answer: 'We aim to have all of the courier services we utilise contact you on the day of the delivery at the very least. We will of course require an up to date contact number as this would be the optimal point of contact.'
      },
      {
        question: 'Do you unload any items or help with installation?',
        answer: 'Our logistics teams will assist with delivering the item into your home but we currently do not offer a room of choice delivery option. With installation, we do offer full installation guides upon request if not enclosed within the packaging but do not have any installation teams at the present moment.'
      },
      {
        question: 'If I am not home when the delivery arrives, what happens?',
        answer: 'Our items are categorised into two separate sizes. With the Small and Medium items these can often be left in a safe place, with a neighbour or returned to the delivery depot for re delivery or collection. With the large (2 Man) items if a time frame has been agreed and no one is available to accept the delivery this would be considered a chargeable failure, and before re delivery can be attempted a surcharge would be applied to the order and would need to be cleared.'
      },
      {
        question: 'Can you leave my delivery with a neighbour if necessary?',
        answer: 'Leaving items with your neighbour or in a safe place is not an issue, many of our delivery companies as standard will look to deliver the item to a neighbour if you are not available, however for the larger items we would always recommend confirming if this is possible or not before ordering due to the size and weight of the products.'
      }
    ]
  },
  {
    category: 'Returns Information',
    icon: '↩️',
    color: '#7c3aed',
    questions: [
      {
        question: 'My order is damaged, what now?',
        answer: 'We aim to ensure there would never be an issue in regards to damage, however in the rare instance this may occur please contact our customer service via email: <EMAIL>. In this scenario we would request some images to confirm the issue and be able to evaluate what options we can offer to be able to resolve your issue as soon as possible. We will need images of the defect, the whole product and the labels from the packaging.'
      },
      {
        question: 'An item is missing from my order, what do I do?',
        answer: 'Please contact our customer service via email: <EMAIL>. In this scenario we would request some images to confirm the issue and advise accordingly.'
      },
      {
        question: 'What is your returns address?',
        answer: 'Our returns address is: Durovin Bathrooms, Unit 15, Bentley Lane Industrial Estate, Bentley Lane, Walsall, West Midlands, WS2 8TL, United Kingdom'
      },
      {
        question: 'Do you offer a collections service?',
        answer: 'We currently do not offer a collections service, however should an item require a return please contact our team for recommendations for suitable carriers.'
      },
      {
        question: 'I need to return an item, what is the process?',
        answer: 'We hope you are happy with all the items ordered however we understand sometimes an item just won\'t suit your needs, this is not an issue. Providing the order is placed within the past 30 days, is in original condition, can be repackaged in original packaging and secured for transit the item can be returned to us. To begin a returns process please contact our team directly and they can begin the process internally for the item.'
      },
      {
        question: 'How long does the returns process take?',
        answer: 'With a returns process this would usually depend on the speed of the returning shipment. From the arrival of the goods the item will be processed by our returns department within 24 hours and the refund processed the same day. Due to certain payment providers this can take up to 72 hours to clear but is usually the same day as the product is cleared by our team.'
      },
      {
        question: 'I have more queries regarding returning an item, who should I ask?',
        answer: 'Our friendly team are on hand to assist with any additional query and can be contacted via telephone: 01922 627773, via email: <EMAIL> or via our live chat.'
      }
    ]
  }
]

function FAQPage(props: Props) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const filteredFAQs = useMemo(() => {
    let filtered = faqData

    if (selectedCategory) {
      filtered = filtered.filter(category => category.category === selectedCategory)
    }

    if (searchTerm) {
      filtered = filtered.map(category => ({
        ...category,
        questions: category.questions.filter(
          faq =>
            faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })).filter(category => category.questions.length > 0)
    }

    return filtered
  }, [searchTerm, selectedCategory])

  const totalQuestions = faqData.reduce((sum, category) => sum + category.questions.length, 0)
  const filteredQuestions = filteredFAQs.reduce((sum, category) => sum + category.questions.length, 0)

  const handleToggleExpand = (categoryIndex: number, questionIndex: number) => {
    const key = `${categoryIndex}-${questionIndex}`
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(key)) {
      newExpanded.delete(key)
    } else {
      newExpanded.add(key)
    }
    setExpandedItems(newExpanded)
  }

  const clearSearch = () => {
    setSearchTerm('')
    setSelectedCategory(null)
  }

  return (
    <>
      <PageMeta 
        title="Frequently Asked Questions - Durovin Bathrooms" 
        metaDescription="Find answers to common questions about orders, delivery, returns and more at Durovin Bathrooms. Get help with your bathroom products and services."
        canonical="/faq" 
      />

      <LayoutHeader floatingMd hideMd={import.meta.graphCommerce.breadcrumbs}>
        <LayoutTitle size='small' component='span'>
          FAQ
        </LayoutTitle>
      </LayoutHeader>

      <Container maxWidth="lg">
        {import.meta.graphCommerce.breadcrumbs && (
          <Breadcrumbs breadcrumbs={[{ href: '/faq', name: 'FAQ' }]} />
        )}
        
        {/* Header Section */}
        <Box >
          <LayoutTitle 
            variant='h1' 
            sx={{ 
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
              fontWeight: 600,
              color: 'text.primary'
            }}
          >
            Frequently Asked Questions
          </LayoutTitle>
          {/* <Typography 
            variant="body1" 
            sx={{ 
              color: 'text.secondary', 
              maxWidth: 560, 
              mx: 'auto',
              fontSize: '1.1rem',
              lineHeight: 1.6
            }}
          >
            Find answers to common questions about our products, orders, delivery, and returns.
          </Typography> */}
        </Box>

        {/* Search Section */}
        {/* <Box sx={{ maxWidth: 480, mx: 'auto', mb: 5 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'text.disabled', fontSize: 20 }} />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position="end">
                  <IconButton onClick={clearSearch} size="small" sx={{ color: 'text.disabled' }}>
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                borderRadius: 3,
                backgroundColor: 'grey.50',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                  borderWidth: 1
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                  borderWidth: 1
                }
              }
            }}
            sx={{
              '& .MuiInputBase-input': {
                py: 1.5,
                fontSize: '0.95rem'
              }
            }}
          />
        </Box> */}

        {/* Category Filter */}
        <Box sx={{ mb: 5 }}>
          <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
            <Chip
              label="All"
              onClick={() => setSelectedCategory(null)}
              variant={selectedCategory === null ? 'filled' : 'outlined'}
              sx={{
                backgroundColor: selectedCategory === null ? 'primary.main' : 'transparent',
                color: selectedCategory === null ? 'white' : 'text.secondary',
                borderColor: selectedCategory === null ? 'primary.main' : 'divider',
                fontSize: '0.85rem',
                height: 32,
                                 '&:hover': {
                   backgroundColor: selectedCategory === null ? 'primary.dark' : 'rgba(37, 99, 235, 0.08)'
                 }
              }}
            />
            {faqData.map((category) => (
              <Chip
                key={category.category}
                label={category.category}
                onClick={() => setSelectedCategory(category.category)}
                variant={selectedCategory === category.category ? 'filled' : 'outlined'}
                sx={{
                  backgroundColor: selectedCategory === category.category ? category.color : 'transparent',
                  color: selectedCategory === category.category ? 'white' : 'text.secondary',
                  borderColor: selectedCategory === category.category ? category.color : 'divider',
                  fontSize: '0.85rem',
                  height: 32,
                                     '&:hover': {
                     backgroundColor: selectedCategory === category.category ? category.color : 'rgba(0, 0, 0, 0.04)'
                   }
                }}
              />
            ))}
          </Stack>
          
          {/* Results Count */}
          <Typography 
            variant="body2" 
            sx={{ 
              textAlign: 'center', 
              color: 'text.disabled', 
              mt: 2,
              fontSize: '0.85rem'
            }}
          >
            Showing {filteredQuestions} of {totalQuestions} questions
          </Typography>
        </Box>

        {/* FAQ Content */}
        <Box sx={{ maxWidth: 900, mx: 'auto', mb: 6 }}>
          {filteredFAQs.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 6 }}>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                No questions found
              </Typography>
              <Typography variant="body2" color="text.disabled" sx={{ mb: 3 }}>
                Try adjusting your search or browse all categories
              </Typography>
              <Button 
                variant="outlined" 
                onClick={clearSearch}
                sx={{ textTransform: 'none' }}
              >
                Clear filters
              </Button>
            </Box>
          ) : (
            <Stack spacing={3}>
              {filteredFAQs.map((category, categoryIndex) => (
                <Box key={category.category}>
                  {/* Category Header */}
                  <Box sx={{ mb: 2 }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: category.color
                        }}
                      />
                      <Typography 
                        variant="h6" 
                        sx={{ 
                          fontWeight: 600,
                          color: 'text.primary',
                          fontSize: '1.1rem'
                        }}
                      >
                        {category.category}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        sx={{ 
                          color: 'text.disabled',
                          fontSize: '0.75rem'
                        }}
                      >
                        {category.questions.length} questions
                      </Typography>
                    </Stack>
                  </Box>
                  
                  {/* Questions */}
                  <Stack spacing={1}>
                    {category.questions.map((faq, questionIndex) => {
                      const isExpanded = expandedItems.has(`${categoryIndex}-${questionIndex}`)
                      return (
                        <Card 
                          key={questionIndex}
                          variant="outlined"
                          sx={{ 
                            border: '1px solid',
                            borderColor: isExpanded ? category.color : 'divider',
                            borderRadius: 2,
                            overflow: 'hidden',
                            transition: 'all 0.2s ease',
                                                         '&:hover': {
                               borderColor: category.color,
                               boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                             }
                          }}
                        >
                                                      <Box
                              onClick={() => handleToggleExpand(categoryIndex, questionIndex)}
                              sx={{
                                px: 3,
                                py: 2.5,
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                gap: 2,
                                '&:hover': {
                                  backgroundColor: 'rgba(0, 0, 0, 0.02)'
                                }
                              }}
                            >
                            <Typography 
                              variant="subtitle1" 
                              sx={{ 
                                flex: 1,
                                fontWeight: 500,
                                fontSize: '0.95rem',
                                lineHeight: 1.5
                              }}
                            >
                              {faq.question}
                            </Typography>
                            <ExpandMoreIcon 
                              sx={{ 
                                color: category.color,
                                transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                                transition: 'transform 0.2s ease',
                                fontSize: 20
                              }} 
                            />
                          </Box>
                          <Collapse in={isExpanded}>
                            <Box 
                              sx={{ 
                                px: 3,
                                pb: 3,
                                borderTop: '1px solid',
                                borderColor: 'divider',
                                                                 backgroundColor: 'rgba(0, 0, 0, 0.02)'
                              }}
                            >
                              <Typography 
                                variant="body2" 
                                sx={{ 
                                  color: 'text.secondary',
                                  lineHeight: 1.6,
                                  fontSize: '0.9rem',
                                  pt: 2
                                }}
                              >
                                {faq.answer}
                              </Typography>
                            </Box>
                          </Collapse>
                        </Card>
                      )
                    })}
                  </Stack>
                </Box>
              ))}
            </Stack>
          )}
        </Box>

        {/* Contact Section */}
        <Card 
          sx={{ 
            backgroundColor: '#fff',
            color: '#000',
            borderRadius: 3,
            mb: 4
          }}
        >
          <CardContent sx={{ p: 5, textAlign: 'center' }}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
              Still need help?
            </Typography>
            <Typography variant="body1" sx={{ mb: 4, opacity: 0.9, maxWidth: 480, mx: 'auto' }}>
              Our support team is available Monday to Friday, 9:00am - 5:00pm to help with any questions.
            </Typography>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ justifyContent: 'center' }}>
              <Button 
                variant="contained" 
                href="/service/contact-us"
                sx={{ 
                  backgroundColor: 'white',
                  color: 'primary.main',
                  textTransform: 'none',
                  px: 3,
                  py: 1,
                  fontWeight: 500,
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: 'grey.100'
                  }
                }}
              >
                Contact Us
              </Button>
              <Button 
                variant="contained" 
                href="tel:01922627773"
                sx={{ 
                  backgroundColor: 'white',
                  color: 'primary.main',
                  textTransform: 'none',
                  px: 3,
                  py: 1,
                  fontWeight: 500,
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: 'grey.100'
                  }
                }}
              >
                Call: 01922 627 773
              </Button>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    </>
  )
}

const pageOptions: PageOptions<LayoutNavigationProps> = {
  Layout: LayoutNavigation,
}
FAQPage.pageOptions = pageOptions

export default FAQPage

export const getStaticProps: GetStaticProps<LayoutNavigationProps> = async (context) => {
  const client = graphqlSharedClient(context)
  const staticClient = graphqlSsrClient(context)

  const conf = client.query({ query: StoreConfigDocument })
  const layout = staticClient.query({ query: LayoutDocument, errorPolicy: 'all' })

  return {
    props: {
      ...(await layout).data,
      apolloState: await conf.then(() => client.cache.extract()),
      variantMd: 'bottom',
      size: 'max',
      up: { href: '/', title: 'Home' },
    },
  }
} 