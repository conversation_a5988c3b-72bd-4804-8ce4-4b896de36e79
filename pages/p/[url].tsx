import { PageOptions } from '@graphcommerce/framer-next-pages'
import {
  cacheFirst,
  PrivateQueryMaskProvider,
  mergeDeep,
  usePrivateQuery,
} from '@graphcommerce/graphql'
import { hygraphPageContent, HygraphPagesQuery } from '@graphcommerce/hygraph-ui'
import {
  AddProductsToCartForm,
  AddProductsToCartFormProps,
  getProductStaticPaths,
  jsonLdProduct,
  jsonLdProductOffer,
  ProductPageName,
  ProductPageAddToCartActionsRow,
  ProductPageBreadcrumbs,
  productPageCategory,
  ProductPageGallery,
  ProductPageJsonLd,
  ProductPageMeta,
  ProductShortDescription,
  AddProductsToCartButton,
} from '@graphcommerce/magento-product'
import { defaultConfigurableOptionsSelection } from '@graphcommerce/magento-product-configurable'
import { RecentlyViewedProducts } from '@graphcommerce/magento-recently-viewed-products'
import { jsonLdProductReview, ProductReviewChip } from '@graphcommerce/magento-review'
import { redirectOrNotFound, Money, StoreConfigDocument } from '@graphcommerce/magento-store'
import { ProductWishlistChipDetail } from '@graphcommerce/magento-wishlist'
import { GetStaticProps, LayoutHeader, LayoutTitle, isTypename } from '@graphcommerce/next-ui'
import { i18n } from '@lingui/core'
import { Trans } from '@lingui/react'
import { Typography } from '@mui/material'
import { GetStaticPaths } from 'next'
import {
  LayoutDocument,
  LayoutNavigation,
  LayoutNavigationProps,
  productListRenderer,
  RowProduct,
  RowRenderer,
  Usps,
  CustomProductPageGallery,
} from '../../components'
import { ProductPageTabs } from '../../components/ProductPageDescription'
import { AddProductsToCartView } from '../../components/ProductView/AddProductsToCartView'
import { UspsDocument, UspsQuery } from '../../components/Usps/Usps.gql'
import { ProductPage2Document, ProductPage2Query } from '../../graphql/ProductPage2.gql'
import { graphqlSharedClient, graphqlSsrClient } from '../../lib/graphql/graphqlSsrClient'
import { Container } from '@graphcommerce/next-ui'

export type Props = HygraphPagesQuery &
  UspsQuery &
  ProductPage2Query &
  Pick<AddProductsToCartFormProps, 'defaultValues'> & { urlKey: string }

type RouteProps = { url: string }
type GetPageStaticPaths = GetStaticPaths<RouteProps>
type GetPageStaticProps = GetStaticProps<LayoutNavigationProps, Props, RouteProps>

function ProductPage(props: Props) {
  const { usps, sidebarUsps, pages, defaultValues, urlKey } = props

  const scopedQuery = usePrivateQuery(
    ProductPage2Document,
    { variables: { urlKey, useCustomAttributes: import.meta.graphCommerce.magentoVersion >= 247 } },
    props,
  )
  const { products, relatedUpsells } = scopedQuery.data

  const product = mergeDeep(
    products?.items?.[0],
    relatedUpsells?.items?.find((item) => item?.uid === products?.items?.[0]?.uid),
  )

  if (!product?.sku || !product.url_key) return null

  return (
    <PrivateQueryMaskProvider mask={scopedQuery.mask}>
      <AddProductsToCartForm 
        key={product.uid} 
        defaultValues={defaultValues}
        onComplete={(result) => {
          // Debug: Log the addProductsToCart response
          // console.log('AddProductsToCart Response:', result)
          // console.log('Full Response Data:', JSON.stringify(result?.data, null, 2))
          // console.log('Added items count:', result?.data?.addProductsToCart?.cart?.items?.length)
          // console.log('Cart total quantity:', result?.data?.addProductsToCart?.cart?.total_quantity)
          // console.log('User errors:', result?.data?.addProductsToCart?.user_errors)
        }}>
        <LayoutHeader floatingMd hideMd={import.meta.graphCommerce.breadcrumbs}>
          <LayoutTitle size='small' component='span'>
            <ProductPageName product={product} />
          </LayoutTitle>
        </LayoutHeader>

        <ProductPageJsonLd
          product={product}
          render={(p) => ({
            '@context': 'https://schema.org',
            ...jsonLdProduct(p),
            ...jsonLdProductOffer(p),
            ...jsonLdProductReview(p),
          })}
        />

        <ProductPageMeta product={product} />

        {import.meta.graphCommerce.breadcrumbs && (
          <Container
            maxWidth={false}
            sx={(theme) => ({ py: `calc(${theme.spacings.xxs} / 2)`, bgcolor: 'background.paper' })}
            breakoutRight
          >
            <ProductPageBreadcrumbs
              product={product}
              sx={(theme) => ({
                [theme.breakpoints.down('md')]: {
                  '& .MuiBreadcrumbs-ol': { justifyContent: 'center' },
                },
              })}
            />
          </Container>
        )}

        <CustomProductPageGallery
          product={product}
          sx={(theme) => ({
            '& .CustomSidebarGallery-sidebar': { display: 'grid', rowGap: theme.spacings.sm },
          })}
          disableSticky
        >
          <div>
            {/* 
              Temporarily hidden: Price range indicator for configurable/bundle products
              Original function: Shows "As low as £XXX" for products with multiple price options
              Displays the minimum price from the price range for configurable and bundle products
            */}
            {isTypename(product, ['ConfigurableProduct', 'BundleProduct']) && (
              <Typography component='div' variant='body1' color='text.disabled' sx={{ display: 'none' }}>
                <Trans
                  id='As low as <0/>'
                  components={{ 0: <Money {...product.price_range.minimum_price.final_price} /> }}
                />
              </Typography>
            )}
            <Typography variant='h4' component='div' gutterBottom>
              <ProductPageName product={product} />
            </Typography>
          </div>
          
          <AddProductsToCartView product={product} />

          <ProductPageAddToCartActionsRow product={product}>
            <AddProductsToCartButton fullWidth product={product} />
            <ProductWishlistChipDetail {...product} />
          </ProductPageAddToCartActionsRow>
          
          <div>
            <ProductShortDescription
              sx={(theme) => ({ 
                mb: theme.spacings.xs,
                fontSize: {
                  xs: '0.8125rem',  // 进一步缩小：13px on mobile
                  sm: '0.875rem',   // 进一步缩小：14px on small screens
                  md: '0.9375rem',  // 进一步缩小：15px on medium screens and up
                },
                lineHeight: {
                  xs: 1.4,         // Tighter line height on mobile
                  sm: 1.5,         // Standard line height on larger screens
                },
                '& ul, & ol': {
                  marginTop: 0,    // 设置ul和ol的margin-top为0
                  marginBottom: 1,
                  paddingLeft: 2,
                },
                '& li': {
                  marginBottom: 0.25, // 列表项之间紧凑间距
                },
                '& p': {
                  marginBottom: 1,    // 段落间距
                },
              })}
              product={product}
            />
            <ProductReviewChip rating={product.rating_summary} reviewSectionId='reviews' />
          </div>
          

          

          {/* <Usps usps={sidebarUsps} size='small' /> */}
        </CustomProductPageGallery>

        <ProductPageTabs
          product={product}
          fontSize='responsive'
        />
      </AddProductsToCartForm>

      {pages?.[0] && (
        <RowRenderer
          loadingEager={0}
          content={pages?.[0].content}
          renderer={{
            RowProduct: (rowProps) => (
              <RowProduct
                {...rowProps}
                {...product}
                items={products?.items}
                aggregations={products?.aggregations}
              />
            ),
          }}
        />
      )}

      <RecentlyViewedProducts
        title={<Trans id='Recently viewed products' />}
        exclude={[product.sku]}
        productListRenderer={productListRenderer}
        sx={(theme) => ({ mb: theme.spacings.xxl })}
      />
    </PrivateQueryMaskProvider>
  )
}

ProductPage.pageOptions = {
  Layout: LayoutNavigation,
} as PageOptions

export default ProductPage

export const getStaticPaths: GetPageStaticPaths = async ({ locales = [] }) => {
  if (process.env.NODE_ENV === 'development') return { paths: [], fallback: 'blocking' }

  const path = (locale: string) => getProductStaticPaths(graphqlSsrClient({ locale }), locale)
  const paths = (await Promise.all(locales.map(path))).flat(1)

  return { paths, fallback: 'blocking' }
}

export const getStaticProps: GetPageStaticProps = async (context) => {
  const { locale, params } = context
  const client = graphqlSharedClient(context)
  const staticClient = graphqlSsrClient(context)

  const urlKey = params?.url ?? '??'

  const conf = client.query({ query: StoreConfigDocument })
  const productPage = staticClient.query({
    query: ProductPage2Document,
    variables: { urlKey, useCustomAttributes: import.meta.graphCommerce.magentoVersion >= 247 },
  })
  const layout = staticClient.query({
    query: LayoutDocument,
    fetchPolicy: cacheFirst(staticClient),
  })

  const product = productPage.then((pp) =>
    pp.data.products?.items?.find((p) => p?.url_key === urlKey),
  )

  const pages = hygraphPageContent(staticClient, 'product/global', product, true)
  if (!(await product)) return redirectOrNotFound(staticClient, conf, params, locale)

  const category = productPageCategory(await product)
  const up =
    category?.url_path && category?.name
      ? { href: `/${category.url_path}`, title: category.name }
      : { href: '/', title: i18n._(/* i18n */ 'Home') }
  const usps = staticClient.query({ query: UspsDocument, fetchPolicy: cacheFirst(staticClient) })

  return {
    props: {
      urlKey,
      ...defaultConfigurableOptionsSelection(urlKey, client, (await productPage).data),
      ...(await layout).data,
      ...(await pages).data,
      ...(await usps).data,
      apolloState: await conf.then(() => client.cache.extract()),
      up,
    },
    revalidate: 60 * 20,
  }
}
