import { PageOptions } from '@graphcommerce/framer-next-pages'
import { PageMeta, StoreConfigDocument } from '@graphcommerce/magento-store'
import { GetStaticProps, LayoutHeader } from '@graphcommerce/next-ui'
import { i18n } from '@lingui/core'
import { Trans } from '@lingui/react'
import { 
  Container, 
  Typography, 
  Link as MuiLink,
  styled,
} from '@mui/material'
import Link from 'next/link'
import {
  LayoutDocument,
  LayoutNavigation,
  LayoutNavigationProps,
} from '../components'
import { graphqlSharedClient, graphqlSsrClient } from '../lib/graphql/graphqlSsrClient'

// 热门搜索词数据
const popularSearchTerms = [
  'basin', '"basin"', '8mm', '8mm n', '110', '400', '400*400', '600', '600*', '600*.', 
  '800', '800 x', '800 x 800 sh', '800 x 800 shower', '800x', '800x800', '800x 800', 
  '800x800 sho', '900', '900mm', '900mm+basin', '900x', '1400x900 show', 'b80', 'basi', 
  'basin', 'basin+600#', 'Basin+counter', 'Basin+sh', 'Basin+she', 'Basin+top', 
  'Basin+white\'', 'Basin counter', 'basins', 'Basin sh', 'Basin she', 'basin taps', 
  'Basin top', 'bathroo', 'bathroom', 'Bathroom+mi', 'Bathroom+she', 'Bathroom b', 
  'bathroom basin', 'Bathroom ca', 'Bathroom mi', 'bathrooms', 'Bathroom she', 
  'ceramic', 'ceramic+basin', 'ceramic basin', 'Colos', 'coloss', 'colossu', 
  'Colossum', 'colossum basin', 'COLOU', 'coun', 'Counter', 'Counter+basin', 
  'Counter basin', 'countertop', 'Counter Top', 'Counter top basin', 'length', 
  'Resi', 'Resin', 'Round', 'she', 'Shower', 'shower enclosure', 'Shower mi', 
  'showers', 'Sink', 'Sliding door', 'square', 'stone', 'stone ba', 'Stone resin', 
  'Stone Resin Bathroom Basin', 'Tap', 'Tap mi', 'taps', 'Taps basin', 'test', 
  'top', 'top basin', 'top mi', 'Wall', 'Wall.', 'wall hung', 'Wall hung basin', 
  'Wall sh', 'Was', 'wast', 'waste', 'WHITE', 'white+basin', 'white+tap', 'white basin'
]

const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(4),
  paddingBottom: theme.spacing(8),
}))

const StyledTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  fontWeight: 600,
  color: theme.palette.text.primary,
}))

const SearchTermsList = styled('div')(({ theme }) => ({
  lineHeight: 1.6,
  fontSize: '0.875rem',
}))

const SearchTermLink = styled(MuiLink)(({ theme }) => ({
  color: theme.palette.text.primary,
  textDecoration: 'none',
  '&:hover': {
    color: theme.palette.primary.main,
    textDecoration: 'underline',
  },
}))

type SearchTermsPageProps = {}

function SearchTermsPage(props: SearchTermsPageProps) {
  return (
    <>
      <PageMeta
        title={i18n._(/* i18n */ 'Popular Search Terms')}
        metaDescription={i18n._(/* i18n */ 'Discover popular search terms and find what you are looking for quickly')}
        canonical='/search-terms'
      />

      <StyledContainer maxWidth="lg">
        <StyledTitle variant="h1">
          <Trans id="Popular Search Terms">Popular Search Terms</Trans>
        </StyledTitle>
        
        <SearchTermsList>
          {popularSearchTerms.map((term, index) => (
            <span key={index}>
              <Link href={`/search/${encodeURIComponent(term)}`} passHref legacyBehavior>
                <SearchTermLink>
                  {term}
                </SearchTermLink>
              </Link>
              {index < popularSearchTerms.length - 1 && ' | '}
            </span>
          ))}
        </SearchTermsList>
      </StyledContainer>
    </>
  )
}

const pageOptions: PageOptions<LayoutNavigationProps> = {
  Layout: LayoutNavigation,
}

SearchTermsPage.pageOptions = pageOptions

export default SearchTermsPage

export const getStaticProps: GetStaticProps<LayoutNavigationProps, SearchTermsPageProps> = async (
  context,
) => {
  const client = graphqlSharedClient(context)
  const staticClient = graphqlSsrClient(context)

  const conf = client.query({ query: StoreConfigDocument })
  const layout = staticClient.query({ query: LayoutDocument })

  return {
    props: {
      ...(await layout).data,
      apolloState: await conf.then(() => client.cache.extract()),
      up: { href: '/', title: i18n._(/* i18n */ 'Home') },
    },
  }
} 