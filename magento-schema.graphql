""""""
type Query {
  """
  Retrieve EAV attributes associated to a frontend form. Use countries query provided by DirectoryGraphQl module to retrieve region_id and country_id attribute options.
  """
  attributesForm(
    """Form code."""
    formCode: String!
  ): AttributesFormOutput!

  """Returns a list of attributes metadata for a given entity type."""
  attributesList(
    """Entity type."""
    entityType: AttributeEntityTypeEnum!

    """Identifies which filter inputs to search for and return."""
    filters: AttributeFilterInput
  ): AttributesMetadataOutput

  """Get a list of available store views and their config information."""
  availableStores(
    """Filter store views by the current store group."""
    useCurrentGroup: Boolean
  ): [StoreConfig]

  """Return information about the specified shopping cart."""
  cart(
    """The unique ID of the cart to query."""
    cart_id: String!
  ): Cart

  """Return a list of categories that match the specified filter."""
  categories(
    """Identifies which Category filter inputs to search for and return."""
    filters: CategoryFilterInput

    """
    Specifies the maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """Specifies which page of results to return. The default value is 1."""
    currentPage: Int = 1
  ): CategoryResult

  """
  Search for categories that match the criteria specified in the `search` and `filter` attributes.
  """
  category(
    """The category ID to use as the root of the search."""
    id: Int
  ): CategoryTree @deprecated(reason: "Use `categories` instead.")

  """Return an array of categories based on the specified filters."""
  categoryList(
    """Identifies which Category filter inputs to search for and return."""
    filters: CategoryFilterInput

    """
    Specifies the maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """Specifies which page of results to return. The default value is 1."""
    currentPage: Int = 1
  ): [CategoryTree] @deprecated(reason: "Use `categories` instead.")

  """Return Terms and Conditions configuration information."""
  checkoutAgreements: [CheckoutAgreement]

  """Return information about CMS blocks."""
  cmsBlocks(
    """An array of CMS block IDs."""
    identifiers: [String]
  ): CmsBlocks

  """Return details about a CMS page."""
  cmsPage(
    """The ID of the CMS page."""
    id: Int

    """The identifier of the CMS page."""
    identifier: String
  ): CmsPage

  """Return products that have been added to the specified compare list."""
  compareList(
    """The unique ID of the compare list to be queried."""
    uid: ID!
  ): CompareList

  """The countries query provides information for all countries."""
  countries: [Country]

  """The countries query provides information for a single country."""
  country(id: String): Country

  """Return information about the store's currency."""
  currency: Currency

  """Return the attribute type, given an attribute code and entity type."""
  customAttributeMetadata(
    """
    An input object that specifies the attribute code and entity type to search.
    """
    attributes: [AttributeInput!]!
  ): CustomAttributeMetadata @deprecated(reason: "Use `customAttributeMetadataV2` query instead.")

  """Retrieve EAV attributes metadata."""
  customAttributeMetadataV2(attributes: [AttributeInput!]): AttributesMetadataOutput!

  """Return detailed information about a customer account."""
  customer: Customer

  """Return information about the customer's shopping cart."""
  customerCart: Cart!

  """Return a list of downloadable products the customer has purchased."""
  customerDownloadableProducts: CustomerDownloadableProducts
  customerOrders: CustomerOrders @deprecated(reason: "Use the `customer` query instead.")

  """Return a list of customer payment tokens stored in the vault."""
  customerPaymentTokens: CustomerPaymentTokens

  """
  Retrieve the secure PayPal URL for a Payments Pro Hosted Solution transaction.
  """
  getHostedProUrl(
    """An input object that specifies the cart ID."""
    input: HostedProUrlInput!
  ): HostedProUrl

  """
  Retrieve payment credentials for a transaction. Use this query for Payflow Link and Payments Advanced payment methods.
  """
  getPayflowLinkToken(
    """
    An input object that defines the requirements to receive a payment token.
    """
    input: PayflowLinkTokenInput!
  ): PayflowLinkToken

  """Retrieves the payment configuration for a given location"""
  getPaymentConfig(
    """Defines the origin location for that payment request"""
    location: PaymentLocation!
  ): PaymentConfigOutput

  """Retrieves the payment details for the order"""
  getPaymentOrder(
    """The customer cart ID"""
    cartId: String!

    """PayPal order ID"""
    id: String!
  ): PaymentOrderOutput

  """Gets the payment SDK urls and values"""
  getPaymentSDK(
    """Defines the origin location for that payment request"""
    location: PaymentLocation!
  ): GetPaymentSDKOutput

  """Retrieves the vault configuration"""
  getVaultConfig: VaultConfigOutput

  """Retrieve guest order details based on number, email and postcode."""
  guestOrder(input: OrderInformationInput!): CustomerOrder!

  """Retrieve guest order details based on token."""
  guestOrderByToken(input: OrderTokenInput!): CustomerOrder!

  """
  Check whether the specified email has already been used to create a customer account.
  """
  isEmailAvailable(
    """The email address to check."""
    email: String!
  ): IsEmailAvailableOutput

  """
  The pickup locations query searches for locations that match the search request requirements.
  """
  pickupLocations(
    """Perform search by location using radius and search term."""
    area: AreaInput

    """Apply filters by attributes."""
    filters: PickupLocationFilterInput

    """
    Specifies which attribute to sort on, and whether to return the results in ascending or descending order.
    """
    sort: PickupLocationSortInput

    """
    The maximum number of pickup locations to return at once. The attribute is optional.
    """
    pageSize: Int = 20

    """Specifies which page of results to return. The default value is 1."""
    currentPage: Int = 1

    """Information about products which should be delivered."""
    productsInfo: [ProductInfoInput]
  ): PickupLocations

  """
  Return the active ratings attributes and the values each rating can have.
  """
  productReviewRatingsMetadata: ProductReviewRatingsMetadata!

  """
  Search for products that match the criteria specified in the `search` and `filter` attributes.
  """
  products(
    """One or more keywords to use in a full-text search."""
    search: String

    """The product attributes to search for and return."""
    filter: ProductAttributeFilterInput

    """
    The maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """The page of results to return. The default value is 1."""
    currentPage: Int = 1

    """
    Specifies which attributes to sort on, and whether to return the results in ascending or descending order.
    """
    sort: ProductAttributeSortInput
  ): Products

  """Returns details about Google reCAPTCHA V3-Invisible configuration."""
  recaptchaV3Config: ReCaptchaConfigurationV3

  """
  Return the full details for a specified product, category, or CMS page.
  """
  route(
    """A `url_key` appended by the `url_suffix, if one exists."""
    url: String!
  ): RoutableInterface

  """Return details about the store's configuration."""
  storeConfig: StoreConfig

  """Return the relative URL for a specified product, category or CMS page."""
  urlResolver(
    """A `url_key` appended by the `url_suffix, if one exists."""
    url: String!
  ): EntityUrl @deprecated(reason: "Use the `route` query instead.")

  """Return the contents of a customer's wish list."""
  wishlist: WishlistOutput @deprecated(reason: "Moved under `Customer.wishlist`.")
}

""""""
type Mutation {
  """
  Add one or more bundle products to the specified cart. We recommend using `addProductsToCart` instead.
  """
  addBundleProductsToCart(
    """An input object that defines which bundle products to add to the cart."""
    input: AddBundleProductsToCartInput
  ): AddBundleProductsToCartOutput

  """
  Add one or more configurable products to the specified cart. We recommend using `addProductsToCart` instead.
  """
  addConfigurableProductsToCart(
    """
    An input object that defines which configurable products to add to the cart.
    """
    input: AddConfigurableProductsToCartInput
  ): AddConfigurableProductsToCartOutput

  """
  Add one or more downloadable products to the specified cart. We recommend using `addProductsToCart` instead.
  """
  addDownloadableProductsToCart(
    """
    An input object that defines which downloadable products to add to the cart.
    """
    input: AddDownloadableProductsToCartInput
  ): AddDownloadableProductsToCartOutput

  """Add any type of product to the cart."""
  addProductsToCart(
    """The cart ID of the shopper."""
    cartId: String!

    """An array that defines the products to add to the cart."""
    cartItems: [CartItemInput!]!
  ): AddProductsToCartOutput

  """Add products to the specified compare list."""
  addProductsToCompareList(
    """
    An input object that defines which products to add to an existing compare list.
    """
    input: AddProductsToCompareListInput
  ): CompareList

  """
  Add one or more products to the specified wish list. This mutation supports all product types.
  """
  addProductsToWishlist(
    """The ID of a wish list."""
    wishlistId: ID!

    """An array of products to add to the wish list."""
    wishlistItems: [WishlistItemInput!]!
  ): AddProductsToWishlistOutput

  """
  Add one or more simple products to the specified cart. We recommend using `addProductsToCart` instead.
  """
  addSimpleProductsToCart(
    """An input object that defines which simple products to add to the cart."""
    input: AddSimpleProductsToCartInput
  ): AddSimpleProductsToCartOutput

  """
  Add one or more virtual products to the specified cart. We recommend using `addProductsToCart` instead.
  """
  addVirtualProductsToCart(
    """
    An input object that defines which virtual products to add to the cart.
    """
    input: AddVirtualProductsToCartInput
  ): AddVirtualProductsToCartOutput

  """Add items in the specified wishlist to the customer's cart."""
  addWishlistItemsToCart(
    """The unique ID of the wish list"""
    wishlistId: ID!

    """
    An array of IDs representing products to be added to the cart. If no IDs are specified, all items in the wishlist will be added to the cart
    """
    wishlistItemIds: [ID!]
  ): AddWishlistItemsToCartOutput

  """Apply a pre-defined coupon code to the specified cart."""
  applyCouponToCart(
    """An input object that defines the coupon code to apply to the cart."""
    input: ApplyCouponToCartInput
  ): ApplyCouponToCartOutput

  """Assign the specified compare list to the logged in customer."""
  assignCompareListToCustomer(
    """The unique ID of the compare list to be assigned."""
    uid: ID!
  ): AssignCompareListToCustomerOutput

  """Assign a logged-in customer to the specified guest shopping cart."""
  assignCustomerToGuestCart(cart_id: String!): Cart!

  """Cancel the specified customer order."""
  cancelOrder(input: CancelOrderInput!): CancelOrderOutput

  """Change the password for the logged-in customer."""
  changeCustomerPassword(
    """The customer's original password."""
    currentPassword: String!

    """The customer's updated password."""
    newPassword: String!
  ): Customer

  """Confirms the email address for a customer."""
  confirmEmail(
    """An input object to identify the customer to confirm the email."""
    input: ConfirmEmailInput!
  ): CustomerOutput

  """Send a 'Contact Us' email to the merchant."""
  contactUs(
    """An input object that defines shopper information."""
    input: ContactUsInput!
  ): ContactUsOutput

  """Creates Client Token for Braintree Javascript SDK initialization."""
  createBraintreeClientToken: String!

  """
  Creates Client Token for Braintree PayPal Javascript SDK initialization.
  """
  createBraintreePayPalClientToken: String!

  """
  Creates Client Token for Braintree PayPal Vault Javascript SDK initialization.
  """
  createBraintreePayPalVaultClientToken(input: BraintreeVaultInput): String!

  """
  Create a new compare list. The compare list is saved for logged in customers.
  """
  createCompareList(input: CreateCompareListInput): CompareList

  """Use `createCustomerV2` instead."""
  createCustomer(
    """An input object that defines the customer to be created."""
    input: CustomerInput!
  ): CustomerOutput

  """Create a billing or shipping address for a customer or guest."""
  createCustomerAddress(input: CustomerAddressInput!): CustomerAddress

  """Create a customer account."""
  createCustomerV2(
    """An input object that defines the customer to be created."""
    input: CustomerCreateInput!
  ): CustomerOutput

  """Create an empty shopping cart for a guest or logged in user"""
  createEmptyCart(
    """An optional input object that assigns the specified ID to the cart."""
    input: createEmptyCartInput
  ): String @deprecated(reason: "Use `Mutation.createGuestCart` or `Query.customerCart` for logged in customer")

  """Create a new shopping cart"""
  createGuestCart(input: CreateGuestCartInput): CreateGuestCartOutput

  """
  Initiate a transaction and receive a token. Use this mutation for Payflow Pro and Payments Pro payment methods
  """
  createPayflowProToken(
    """
    An input object that defines the requirements to fetch payment token information.
    """
    input: PayflowProTokenInput!
  ): CreatePayflowProTokenOutput

  """Creates a payment order for further payment processing"""
  createPaymentOrder(
    """
    Contains payment order details that are used while processing the payment order
    """
    input: CreatePaymentOrderInput!
  ): CreatePaymentOrderOutput

  """
  Initiate an Express Checkout transaction and receive a token. Use this mutation for Express Checkout and Payments Standard payment methods.
  """
  createPaypalExpressToken(
    """
    An input object that defines the requirements to receive a payment token.
    """
    input: PaypalExpressTokenInput!
  ): PaypalExpressTokenOutput

  """Create a product review for the specified product."""
  createProductReview(
    """
    An input object that contains the details necessary to create a product review.
    """
    input: CreateProductReviewInput!
  ): CreateProductReviewOutput!

  """Creates a vault payment token"""
  createVaultCardPaymentToken(
    """Describe the variables needed to create a vault card payment token"""
    input: CreateVaultCardPaymentTokenInput!
  ): CreateVaultCardPaymentTokenOutput

  """Creates a vault card setup token"""
  createVaultCardSetupToken(
    """Describe the variables needed to create a vault card setup token"""
    input: CreateVaultCardSetupTokenInput!
  ): CreateVaultCardSetupTokenOutput

  """Delete the specified compare list."""
  deleteCompareList(
    """The unique ID of the compare list to be deleted."""
    uid: ID!
  ): DeleteCompareListOutput

  """Delete customer account"""
  deleteCustomer: Boolean

  """Delete the billing or shipping address of a customer."""
  deleteCustomerAddress(
    """The ID of the customer address to be deleted."""
    id: Int!
  ): Boolean

  """Delete a customer's payment token."""
  deletePaymentToken(
    """The reusable payment token securely stored in the vault."""
    public_hash: String!
  ): DeletePaymentTokenOutput

  """Estimate shipping method(s) for cart based on address"""
  estimateShippingMethods(
    """
    An input object that specifies details for estimation of available shipping methods
    """
    input: EstimateTotalsInput!
  ): [AvailableShippingMethod]

  """Estimate totals for cart based on the address"""
  estimateTotals(
    """An input object that specifies details for cart totals estimation"""
    input: EstimateTotalsInput!
  ): EstimateTotalsOutput!

  """Generate a token for specified customer."""
  generateCustomerToken(
    """The customer's email address."""
    email: String!

    """The customer's password."""
    password: String!
  ): CustomerToken

  """
  Request a customer token so that an administrator can perform remote shopping assistance.
  """
  generateCustomerTokenAsAdmin(
    """An input object that defines the customer email address."""
    input: GenerateCustomerTokenAsAdminInput!
  ): GenerateCustomerTokenAsAdminOutput

  """
  Handle a payment response and save the payment in Quote. Use this mutation for Payflow Pro and Payments Pro payment methods.
  """
  handlePayflowProResponse(
    """
    An input object that includes the payload returned by PayPal and the cart ID.
    """
    input: PayflowProResponseInput!
  ): PayflowProResponseOutput

  """
  Transfer the contents of a guest cart into the cart of a logged-in customer.
  """
  mergeCarts(
    """The guest's cart ID before they login."""
    source_cart_id: String!

    """The cart ID after the guest logs in."""
    destination_cart_id: String
  ): Cart!

  """Convert the quote into an order."""
  placeOrder(
    """An input object that defines the shopper's cart ID."""
    input: PlaceOrderInput
  ): PlaceOrderOutput

  """
  Remove a previously-applied coupon from the cart. The cart must contain at least one item in order to remove the coupon.
  """
  removeCouponFromCart(
    """
    An input object that defines which coupon code to remove from the cart.
    """
    input: RemoveCouponFromCartInput
  ): RemoveCouponFromCartOutput

  """
  Delete the entire quantity of a specified item from the cart. If you remove all items from the cart, the cart continues to exist.
  """
  removeItemFromCart(
    """An input object that defines which products to remove from the cart."""
    input: RemoveItemFromCartInput
  ): RemoveItemFromCartOutput

  """Remove products from the specified compare list."""
  removeProductsFromCompareList(
    """
    An input object that defines which products to remove from a compare list.
    """
    input: RemoveProductsFromCompareListInput
  ): CompareList

  """Remove one or more products from the specified wish list."""
  removeProductsFromWishlist(
    """The ID of a wish list."""
    wishlistId: ID!

    """An array of item IDs representing products to be removed."""
    wishlistItemsIds: [ID!]!
  ): RemoveProductsFromWishlistOutput

  """Add all products from a customer's previous order to the cart."""
  reorderItems(orderNumber: String!): ReorderItemsOutput

  """
  Request an email with a reset password token for the registered customer identified by the specified email.
  """
  requestPasswordResetEmail(
    """The customer's email address."""
    email: String!
  ): Boolean

  """
  Reset a customer's password using the reset password token that the customer received in an email after requesting it using `requestPasswordResetEmail`.
  """
  resetPassword(
    """The customer's email address."""
    email: String!

    """A runtime token generated by the `requestPasswordResetEmail` mutation."""
    resetPasswordToken: String!

    """The customer's new password."""
    newPassword: String!
  ): Boolean

  """Revoke the customer token."""
  revokeCustomerToken: RevokeCustomerTokenOutput

  """
  Send a message on behalf of a customer to the specified email addresses.
  """
  sendEmailToFriend(
    """An input object that defines sender, recipients, and product."""
    input: SendEmailToFriendInput
  ): SendEmailToFriendOutput

  """Set the billing address on a specific cart."""
  setBillingAddressOnCart(
    """
    An input object that defines the billing address to be assigned to the cart.
    """
    input: SetBillingAddressOnCartInput
  ): SetBillingAddressOnCartOutput

  """Assign the email address of a guest to the cart."""
  setGuestEmailOnCart(
    """An input object that defines a guest email address."""
    input: SetGuestEmailOnCartInput
  ): SetGuestEmailOnCartOutput

  """Set the cart payment method and convert the cart into an order."""
  setPaymentMethodAndPlaceOrder(input: SetPaymentMethodAndPlaceOrderInput): PlaceOrderOutput @deprecated(reason: "Should use setPaymentMethodOnCart and placeOrder mutations in single request.")

  """Apply a payment method to the cart."""
  setPaymentMethodOnCart(
    """
    An input object that defines which payment method to apply to the cart.
    """
    input: SetPaymentMethodOnCartInput
  ): SetPaymentMethodOnCartOutput

  """Set one or more shipping addresses on a specific cart."""
  setShippingAddressesOnCart(
    """
    An input object that defines one or more shipping addresses to be assigned to the cart.
    """
    input: SetShippingAddressesOnCartInput
  ): SetShippingAddressesOnCartOutput

  """Set one or more delivery methods on a cart."""
  setShippingMethodsOnCart(
    """An input object that applies one or more shipping methods to the cart."""
    input: SetShippingMethodsOnCartInput
  ): SetShippingMethodsOnCartOutput

  """Subscribe the specified email to the store's newsletter."""
  subscribeEmailToNewsletter(
    """The email address that will receive the store's newsletter."""
    email: String!
  ): SubscribeEmailToNewsletterOutput

  """Synchronizes the payment order details for further payment processing"""
  syncPaymentOrder(
    """
    Describes the variables needed to synchronize the payment order details
    """
    input: SyncPaymentOrderInput
  ): Boolean

  """Modify items in the cart."""
  updateCartItems(
    """An input object that defines products to be updated."""
    input: UpdateCartItemsInput
  ): UpdateCartItemsOutput

  """Use `updateCustomerV2` instead."""
  updateCustomer(
    """An input object that defines the customer characteristics to update."""
    input: CustomerInput!
  ): CustomerOutput

  """Update the billing or shipping address of a customer or guest."""
  updateCustomerAddress(
    """The ID assigned to the customer address."""
    id: Int!

    """An input object that contains changes to the customer address."""
    input: CustomerAddressInput
  ): CustomerAddress

  """Change the email address for the logged-in customer."""
  updateCustomerEmail(
    """The customer's email address."""
    email: String!

    """The customer's password."""
    password: String!
  ): CustomerOutput

  """Update the customer's personal information."""
  updateCustomerV2(
    """An input object that defines the customer characteristics to update."""
    input: CustomerUpdateInput!
  ): CustomerOutput

  """Update one or more products in the specified wish list."""
  updateProductsInWishlist(
    """The ID of a wish list."""
    wishlistId: ID!

    """An array of items to be updated."""
    wishlistItems: [WishlistItemUpdateInput!]!
  ): UpdateProductsInWishlistOutput
}

"""Defines the comparison operators that can be used in a filter."""
input FilterTypeInput {
  """Equals."""
  eq: String

  """"""
  finset: [String]

  """From. Must be used with the `to` field."""
  from: String

  """Greater than."""
  gt: String

  """Greater than or equal to."""
  gteq: String

  """In. The value can contain a set of comma-separated values."""
  in: [String]

  """
  Like. The specified value can contain % (percent signs) to allow matching of 0 or more characters.
  """
  like: String

  """Less than."""
  lt: String

  """Less than or equal to."""
  lteq: String

  """More than or equal to."""
  moreq: String

  """Not equal to."""
  neq: String

  """Not in. The value can contain a set of comma-separated values."""
  nin: [String]

  """Not null."""
  notnull: String

  """Is null."""
  null: String

  """To. Must be used with the `from` field."""
  to: String
}

"""Defines a filter that matches the input exactly."""
input FilterEqualTypeInput {
  """
  Use this attribute to exactly match the specified string. For example, to filter on a specific category ID, specify a value such as `5`.
  """
  eq: String

  """
  Use this attribute to filter on an array of values. For example, to filter on category IDs 4, 5, and 6, specify a value of `["4", "5", "6"]`.
  """
  in: [String]
}

"""
Defines a filter that matches a range of values, such as prices or dates.
"""
input FilterRangeTypeInput {
  """Use this attribute to specify the lowest possible value in the range."""
  from: String

  """Use this attribute to specify the highest possible value in the range."""
  to: String
}

"""Defines a filter that performs a fuzzy search."""
input FilterMatchTypeInput {
  """
  Use this attribute to fuzzy match the specified string. For example, to filter on a specific SKU, specify a value such as `24-MB01`.
  """
  match: String

  """
  Filter match type for fine-tuned results. Possible values FULL or PARTIAL. If match_type is not provided, returned results will default to FULL match.
  """
  match_type: FilterMatchTypeEnum
}

""""""
enum FilterMatchTypeEnum {
  """"""
  FULL

  """"""
  PARTIAL
}

"""Defines a filter for an input string."""
input FilterStringTypeInput {
  """Filters items that are exactly the same as the specified string."""
  eq: String

  """
  Filters items that are exactly the same as entries specified in an array of strings.
  """
  in: [String]

  """
  Defines a filter that performs a fuzzy search using the specified string.
  """
  match: String
}

"""Provides navigation for the query response."""
type SearchResultPageInfo {
  """The specific page to return."""
  current_page: Int

  """The maximum number of items to return per page of results."""
  page_size: Int

  """The total number of pages in the response."""
  total_pages: Int
}

"""Indicates whether to return results in ascending or descending order."""
enum SortEnum {
  """"""
  ASC

  """"""
  DESC
}

""""""
type ComplexTextValue {
  """Text that can contain HTML tags."""
  html: String!
}

"""
Defines a monetary value, including a numeric value and a currency code.
"""
type Money {
  """A three-letter currency code, such as USD or EUR."""
  currency: CurrencyEnum

  """A number expressing a monetary value."""
  value: Float
}

"""The list of available currency codes."""
enum CurrencyEnum {
  """"""
  AFN

  """"""
  ALL

  """"""
  AZN

  """"""
  DZD

  """"""
  AOA

  """"""
  ARS

  """"""
  AMD

  """"""
  AWG

  """"""
  AUD

  """"""
  BSD

  """"""
  BHD

  """"""
  BDT

  """"""
  BBD

  """"""
  BYN

  """"""
  BZD

  """"""
  BMD

  """"""
  BTN

  """"""
  BOB

  """"""
  BAM

  """"""
  BWP

  """"""
  BRL

  """"""
  GBP

  """"""
  BND

  """"""
  BGN

  """"""
  BUK

  """"""
  BIF

  """"""
  KHR

  """"""
  CAD

  """"""
  CVE

  """"""
  CZK

  """"""
  KYD

  """"""
  GQE

  """"""
  CLP

  """"""
  CNY

  """"""
  COP

  """"""
  KMF

  """"""
  CDF

  """"""
  CRC

  """"""
  HRK

  """"""
  CUP

  """"""
  DKK

  """"""
  DJF

  """"""
  DOP

  """"""
  XCD

  """"""
  EGP

  """"""
  SVC

  """"""
  ERN

  """"""
  EEK

  """"""
  ETB

  """"""
  EUR

  """"""
  FKP

  """"""
  FJD

  """"""
  GMD

  """"""
  GEK

  """"""
  GEL

  """"""
  GHS

  """"""
  GIP

  """"""
  GTQ

  """"""
  GNF

  """"""
  GYD

  """"""
  HTG

  """"""
  HNL

  """"""
  HKD

  """"""
  HUF

  """"""
  ISK

  """"""
  INR

  """"""
  IDR

  """"""
  IRR

  """"""
  IQD

  """"""
  ILS

  """"""
  JMD

  """"""
  JPY

  """"""
  JOD

  """"""
  KZT

  """"""
  KES

  """"""
  KWD

  """"""
  KGS

  """"""
  LAK

  """"""
  LVL

  """"""
  LBP

  """"""
  LSL

  """"""
  LRD

  """"""
  LYD

  """"""
  LTL

  """"""
  MOP

  """"""
  MKD

  """"""
  MGA

  """"""
  MWK

  """"""
  MYR

  """"""
  MVR

  """"""
  LSM

  """"""
  MRO

  """"""
  MUR

  """"""
  MXN

  """"""
  MDL

  """"""
  MNT

  """"""
  MAD

  """"""
  MZN

  """"""
  MMK

  """"""
  NAD

  """"""
  NPR

  """"""
  ANG

  """"""
  YTL

  """"""
  NZD

  """"""
  NIC

  """"""
  NGN

  """"""
  KPW

  """"""
  NOK

  """"""
  OMR

  """"""
  PKR

  """"""
  PAB

  """"""
  PGK

  """"""
  PYG

  """"""
  PEN

  """"""
  PHP

  """"""
  PLN

  """"""
  QAR

  """"""
  RHD

  """"""
  RON

  """"""
  RUB

  """"""
  RWF

  """"""
  SHP

  """"""
  STD

  """"""
  SAR

  """"""
  RSD

  """"""
  SCR

  """"""
  SLL

  """"""
  SGD

  """"""
  SKK

  """"""
  SBD

  """"""
  SOS

  """"""
  ZAR

  """"""
  KRW

  """"""
  LKR

  """"""
  SDG

  """"""
  SRD

  """"""
  SZL

  """"""
  SEK

  """"""
  CHF

  """"""
  SYP

  """"""
  TWD

  """"""
  TJS

  """"""
  TZS

  """"""
  THB

  """"""
  TOP

  """"""
  TTD

  """"""
  TND

  """"""
  TMM

  """"""
  USD

  """"""
  UGX

  """"""
  UAH

  """"""
  AED

  """"""
  UYU

  """"""
  UZS

  """"""
  VUV

  """"""
  VEB

  """"""
  VEF

  """"""
  VND

  """"""
  CHE

  """"""
  CHW

  """"""
  XOF

  """"""
  WST

  """"""
  YER

  """"""
  ZMK

  """"""
  ZWD

  """"""
  TRY

  """"""
  AZM

  """"""
  ROL

  """"""
  TRL

  """"""
  XPF
}

"""Defines a customer-entered option."""
input EnteredOptionInput {
  """
  The unique ID for a `CustomizableOptionInterface` object, such as a `CustomizableFieldOption`, `CustomizableFileOption`, or `CustomizableAreaOption` object.
  """
  uid: ID!

  """Text the customer entered."""
  value: String!
}

""""""
enum BatchMutationStatus {
  """"""
  SUCCESS

  """"""
  FAILURE

  """"""
  MIXED_RESULTS
}

""""""
interface ErrorInterface {
  """The returned error message."""
  message: String!
}

"""Contains an error message when an invalid UID was specified."""
type NoSuchEntityUidError implements ErrorInterface {
  """The returned error message."""
  message: String!

  """The specified invalid unique ID of an object."""
  uid: ID!
}

"""Contains an error message when an internal error occurred."""
type InternalError implements ErrorInterface {
  """The returned error message."""
  message: String!
}

"""Defines an array of custom attributes."""
type CustomAttributeMetadata {
  """An array of attributes."""
  items: [Attribute]
}

"""Contains details about the attribute, including the code and type."""
type Attribute {
  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  attribute_code: String

  """Attribute options list."""
  attribute_options: [AttributeOption]

  """The data type of the attribute."""
  attribute_type: String

  """The type of entity that defines the attribute."""
  entity_type: String

  """The frontend input type of the attribute."""
  input_type: String

  """Details about the storefront properties configured for the attribute."""
  storefront_properties: StorefrontProperties
}

"""Indicates where an attribute can be displayed."""
type StorefrontProperties {
  """
  The relative position of the attribute in the layered navigation block.
  """
  position: Int

  """
  Indicates whether the attribute is filterable with results, without results, or not at all.
  """
  use_in_layered_navigation: UseInLayeredNavigationOptions

  """Indicates whether the attribute is displayed in product listings."""
  use_in_product_listing: Boolean

  """
  Indicates whether the attribute can be used in layered navigation on search results pages.
  """
  use_in_search_results_layered_navigation: Boolean

  """Indicates whether the attribute is displayed on product pages."""
  visible_on_catalog_pages: Boolean
}

"""Defines whether the attribute is filterable in layered navigation."""
enum UseInLayeredNavigationOptions {
  """"""
  NO

  """"""
  FILTERABLE_WITH_RESULTS

  """"""
  FILTERABLE_NO_RESULT
}

"""Defines an attribute option."""
type AttributeOption {
  """The label assigned to the attribute option."""
  label: String

  """The attribute option value."""
  value: String
}

"""
Defines the attribute characteristics to search for the `attribute_code` and `entity_type` to search.
"""
input AttributeInput {
  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  attribute_code: String

  """The type of entity that defines the attribute."""
  entity_type: String
}

"""Metadata of EAV attributes."""
type AttributesMetadataOutput {
  """Errors of retrieving certain attributes metadata."""
  errors: [AttributeMetadataError]!

  """Requested attributes metadata."""
  items: [CustomAttributeMetadataInterface]!
}

"""Attribute metadata retrieval error."""
type AttributeMetadataError {
  """Attribute metadata retrieval error message."""
  message: String!

  """Attribute metadata retrieval error type."""
  type: AttributeMetadataErrorType!
}

"""Attribute metadata retrieval error types."""
enum AttributeMetadataErrorType {
  """The requested entity was not found."""
  ENTITY_NOT_FOUND

  """The requested attribute was not found."""
  ATTRIBUTE_NOT_FOUND

  """The filter cannot be applied as it does not belong to the entity"""
  FILTER_NOT_FOUND

  """Not categorized error, see the error message."""
  UNDEFINED
}

"""An interface containing fields that define the EAV attribute."""
interface CustomAttributeMetadataInterface {
  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  code: ID!

  """Default attribute value."""
  default_value: String

  """The type of entity that defines the attribute."""
  entity_type: AttributeEntityTypeEnum!

  """The frontend class of the attribute."""
  frontend_class: String

  """The frontend input type of the attribute."""
  frontend_input: AttributeFrontendInputEnum

  """Whether the attribute value is required."""
  is_required: Boolean!

  """Whether the attribute value must be unique."""
  is_unique: Boolean!

  """The label assigned to the attribute."""
  label: String

  """Attribute options."""
  options: [CustomAttributeOptionInterface]!
}

""""""
interface CustomAttributeOptionInterface {
  """Is the option value default."""
  is_default: Boolean!

  """The label assigned to the attribute option."""
  label: String!

  """The attribute option value."""
  value: String!
}

"""Base EAV implementation of CustomAttributeOptionInterface."""
type AttributeOptionMetadata implements CustomAttributeOptionInterface {
  """Is the option value default."""
  is_default: Boolean!

  """The label assigned to the attribute option."""
  label: String!

  """The attribute option value."""
  value: String!
}

"""Base EAV implementation of CustomAttributeMetadataInterface."""
type AttributeMetadata implements CustomAttributeMetadataInterface {
  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  code: ID!

  """Default attribute value."""
  default_value: String

  """The type of entity that defines the attribute."""
  entity_type: AttributeEntityTypeEnum!

  """The frontend class of the attribute."""
  frontend_class: String

  """The frontend input type of the attribute."""
  frontend_input: AttributeFrontendInputEnum

  """Whether the attribute value is required."""
  is_required: Boolean!

  """Whether the attribute value must be unique."""
  is_unique: Boolean!

  """The label assigned to the attribute."""
  label: String

  """Attribute options."""
  options: [CustomAttributeOptionInterface]!
}

"""
List of all entity types. Populated by the modules introducing EAV entities.
"""
enum AttributeEntityTypeEnum {
  """"""
  CATALOG_PRODUCT

  """"""
  CATALOG_CATEGORY

  """"""
  CUSTOMER

  """"""
  CUSTOMER_ADDRESS
}

"""EAV attribute frontend input types."""
enum AttributeFrontendInputEnum {
  """"""
  BOOLEAN

  """"""
  DATE

  """"""
  DATETIME

  """"""
  FILE

  """"""
  GALLERY

  """"""
  HIDDEN

  """"""
  IMAGE

  """"""
  MEDIA_IMAGE

  """"""
  MULTILINE

  """"""
  MULTISELECT

  """"""
  PRICE

  """"""
  SELECT

  """"""
  TEXT

  """"""
  TEXTAREA

  """"""
  WEIGHT

  """"""
  UNDEFINED
}

"""Metadata of EAV attributes associated to form"""
type AttributesFormOutput {
  """Errors of retrieving certain attributes metadata."""
  errors: [AttributeMetadataError]!

  """Requested attributes metadata."""
  items: [CustomAttributeMetadataInterface]!
}

""""""
interface AttributeValueInterface {
  """The attribute code."""
  code: ID!
}

""""""
type AttributeValue implements AttributeValueInterface {
  """The attribute code."""
  code: ID!

  """The attribute value."""
  value: String!
}

""""""
type AttributeSelectedOptions implements AttributeValueInterface {
  """The attribute code."""
  code: ID!
  selected_options: [AttributeSelectedOptionInterface]!
}

""""""
interface AttributeSelectedOptionInterface {
  """The attribute selected option label."""
  label: String!

  """The attribute selected option value."""
  value: String!
}

""""""
type AttributeSelectedOption implements AttributeSelectedOptionInterface {
  """The attribute selected option label."""
  label: String!

  """The attribute selected option value."""
  value: String!
}

"""Specifies the value for attribute."""
input AttributeValueInput {
  """The code of the attribute."""
  attribute_code: String!

  """
  An array containing selected options for a select or multiselect attribute.
  """
  selected_options: [AttributeInputSelectedOption]

  """The value assigned to the attribute."""
  value: String
}

"""Specifies selected option for a select or multiselect attribute value."""
input AttributeInputSelectedOption {
  """The attribute option value."""
  value: String!
}

"""An input object that specifies the filters used for attributes."""
input AttributeFilterInput {
  """
  Whether a product or category attribute can be compared against another or not.
  """
  is_comparable: Boolean

  """Whether a product or category attribute can be filtered or not."""
  is_filterable: Boolean

  """
  Whether a product or category attribute can be filtered in search or not.
  """
  is_filterable_in_search: Boolean

  """Whether a product or category attribute can use HTML on front or not."""
  is_html_allowed_on_front: Boolean

  """Whether a product or category attribute can be searched or not."""
  is_searchable: Boolean

  """
  Whether a product or category attribute can be used for price rules or not.
  """
  is_used_for_price_rules: Boolean

  """
  Whether a product or category attribute is used for promo rules or not.
  """
  is_used_for_promo_rules: Boolean

  """
  Whether a product or category attribute is visible in advanced search or not.
  """
  is_visible_in_advanced_search: Boolean

  """Whether a product or category attribute is visible on front or not."""
  is_visible_on_front: Boolean

  """Whether a product or category attribute has WYSIWYG enabled or not."""
  is_wysiwyg_enabled: Boolean

  """
  Whether a product or category attribute is used in product listing or not.
  """
  used_in_product_listing: Boolean
}

"""
Deprecated. It should not be used on the storefront. Contains information about a website.
"""
type Website {
  """A code assigned to the website to identify it."""
  code: String @deprecated(reason: "The field should not be used on the storefront.")

  """The default group ID of the website."""
  default_group_id: String @deprecated(reason: "The field should not be used on the storefront.")

  """The ID number assigned to the website."""
  id: Int @deprecated(reason: "The field should not be used on the storefront.")

  """Indicates whether this is the default website."""
  is_default: Boolean @deprecated(reason: "The field should not be used on the storefront.")

  """The website name. Websites use this name to identify it easier."""
  name: String @deprecated(reason: "The field should not be used on the storefront.")

  """The attribute to use for sorting websites."""
  sort_order: Int @deprecated(reason: "The field should not be used on the storefront.")
}

"""Contains information about a store's configuration."""
type StoreConfig {
  """
  Contains scripts that must be included in the HTML before the closing `<body>` tag.
  """
  absolute_footer: String

  """
  Indicates whether guest users can write product reviews. Possible values: 1 (Yes) and 0 (No).
  """
  allow_guests_to_write_product_reviews: String

  """The value of the Allow Gift Messages for Order Items option"""
  allow_items: String

  """The value of the Allow Gift Messages on Order Level option"""
  allow_order: String

  """
  Indicates whether to enable autocomplete on login and forgot password forms.
  """
  autocomplete_on_storefront: Boolean

  """The base currency code."""
  base_currency_code: String

  """
  A fully-qualified URL that is used to create relative links to the `base_url`.
  """
  base_link_url: String

  """The fully-qualified URL that specifies the location of media files."""
  base_media_url: String

  """
  The fully-qualified URL that specifies the location of static view files.
  """
  base_static_url: String

  """The store’s fully-qualified base URL."""
  base_url: String

  """Braintree 3D Secure, should 3D Secure be used for specific countries."""
  braintree_3dsecure_allowspecific: Boolean

  """Braintree 3D Secure, always request 3D Secure flag."""
  braintree_3dsecure_always_request_3ds: Boolean

  """
  Braintree 3D Secure, the specific countries to use 3D Secure in, to be used if allow specific is status is enabled.
  """
  braintree_3dsecure_specificcountry: String

  """
  Braintree 3D Secure, threshold above which 3D Secure should be requested.
  """
  braintree_3dsecure_threshold_amount: String

  """Braintree 3D Secure enabled/active status."""
  braintree_3dsecure_verify_3dsecure: Boolean

  """Braintree ACH vault status."""
  braintree_ach_direct_debit_vault_active: Boolean

  """Braintree Apple Pay merchant name."""
  braintree_applepay_merchant_name: String

  """Braintree Apple Pay vault status."""
  braintree_applepay_vault_active: Boolean

  """Braintree cc vault status."""
  braintree_cc_vault_active: String

  """Braintree cc vault CVV re-verification enabled status."""
  braintree_cc_vault_cvv: Boolean

  """Braintree environment."""
  braintree_environment: String

  """Braintree Google Pay button color."""
  braintree_googlepay_btn_color: String

  """Braintree Google Pay Card types supported."""
  braintree_googlepay_cctypes: String

  """Braintree Google Pay merchant ID."""
  braintree_googlepay_merchant_id: String

  """Braintree Google Pay vault status."""
  braintree_googlepay_vault_active: Boolean

  """Braintree Local Payment Methods allowed payment methods."""
  braintree_local_payment_allowed_methods: String

  """Braintree Local Payment Methods fallback button text."""
  braintree_local_payment_fallback_button_text: String

  """Braintree Local Payment Methods redirect URL on failed payment."""
  braintree_local_payment_redirect_on_fail: String

  """Braintree Merchant Account ID."""
  braintree_merchant_account_id: String

  """Braintree PayPal Credit mini-cart & cart button style color."""
  braintree_paypal_button_location_cart_type_credit_color: String

  """Braintree PayPal Credit mini-cart & cart button style label."""
  braintree_paypal_button_location_cart_type_credit_label: String

  """Braintree PayPal Credit mini-cart & cart button style shape."""
  braintree_paypal_button_location_cart_type_credit_shape: String

  """Braintree PayPal Credit mini-cart & cart button show status."""
  braintree_paypal_button_location_cart_type_credit_show: Boolean

  """Braintree PayPal Pay Later messaging mini-cart & cart style layout."""
  braintree_paypal_button_location_cart_type_messaging_layout: String

  """Braintree PayPal Pay Later messaging mini-cart & cart style logo."""
  braintree_paypal_button_location_cart_type_messaging_logo: String

  """
  Braintree PayPal Pay Later messaging mini-cart & cart style logo position.
  """
  braintree_paypal_button_location_cart_type_messaging_logo_position: String

  """Braintree PayPal Pay Later messaging mini-cart & cart show status."""
  braintree_paypal_button_location_cart_type_messaging_show: Boolean

  """Braintree PayPal Pay Later messaging checkout style text color."""
  braintree_paypal_button_location_cart_type_messaging_text_color: String

  """Braintree PayPal Pay Later mini-cart & cart button style color."""
  braintree_paypal_button_location_cart_type_paylater_color: String

  """Braintree PayPal Pay Later mini-cart & cart button style label."""
  braintree_paypal_button_location_cart_type_paylater_label: String

  """Braintree PayPal Pay Later mini-cart & cart button style shape."""
  braintree_paypal_button_location_cart_type_paylater_shape: String

  """Braintree PayPal Pay Later mini-cart & cart button show status."""
  braintree_paypal_button_location_cart_type_paylater_show: Boolean

  """Braintree PayPal mini-cart & cart button style color."""
  braintree_paypal_button_location_cart_type_paypal_color: String

  """Braintree PayPal mini-cart & cart button style label."""
  braintree_paypal_button_location_cart_type_paypal_label: String

  """Braintree PayPal mini-cart & cart button style shape."""
  braintree_paypal_button_location_cart_type_paypal_shape: String

  """Braintree PayPal mini-cart & cart button show."""
  braintree_paypal_button_location_cart_type_paypal_show: Boolean

  """Braintree PayPal Credit checkout button style color."""
  braintree_paypal_button_location_checkout_type_credit_color: String

  """Braintree PayPal Credit checkout button style label."""
  braintree_paypal_button_location_checkout_type_credit_label: String

  """Braintree PayPal Credit checkout button style shape."""
  braintree_paypal_button_location_checkout_type_credit_shape: String

  """Braintree PayPal Credit checkout button show status."""
  braintree_paypal_button_location_checkout_type_credit_show: Boolean

  """Braintree PayPal Pay Later messaging checkout style layout."""
  braintree_paypal_button_location_checkout_type_messaging_layout: String

  """Braintree PayPal Pay Later messaging checkout style logo."""
  braintree_paypal_button_location_checkout_type_messaging_logo: String

  """Braintree PayPal Pay Later messaging checkout style logo position."""
  braintree_paypal_button_location_checkout_type_messaging_logo_position: String

  """Braintree PayPal Pay Later messaging checkout show status."""
  braintree_paypal_button_location_checkout_type_messaging_show: Boolean

  """Braintree PayPal Pay Later messaging checkout style text color."""
  braintree_paypal_button_location_checkout_type_messaging_text_color: String

  """Braintree PayPal Pay Later checkout button style color."""
  braintree_paypal_button_location_checkout_type_paylater_color: String

  """Braintree PayPal Pay Later checkout button style label."""
  braintree_paypal_button_location_checkout_type_paylater_label: String

  """Braintree PayPal Pay Later checkout button style shape."""
  braintree_paypal_button_location_checkout_type_paylater_shape: String

  """Braintree PayPal Pay Later checkout button show status."""
  braintree_paypal_button_location_checkout_type_paylater_show: Boolean

  """Braintree PayPal checkout button style color."""
  braintree_paypal_button_location_checkout_type_paypal_color: String

  """Braintree PayPal checkout button style label."""
  braintree_paypal_button_location_checkout_type_paypal_label: String

  """Braintree PayPal checkout button style shape."""
  braintree_paypal_button_location_checkout_type_paypal_shape: String

  """Braintree PayPal checkout button show."""
  braintree_paypal_button_location_checkout_type_paypal_show: Boolean

  """Braintree PayPal Credit PDP button style color."""
  braintree_paypal_button_location_productpage_type_credit_color: String

  """Braintree PayPal Credit PDP button style label."""
  braintree_paypal_button_location_productpage_type_credit_label: String

  """Braintree PayPal Credit PDP button style shape."""
  braintree_paypal_button_location_productpage_type_credit_shape: String

  """Braintree PayPal Credit PDP button show status."""
  braintree_paypal_button_location_productpage_type_credit_show: Boolean

  """Braintree PayPal Pay Later messaging PDP style layout."""
  braintree_paypal_button_location_productpage_type_messaging_layout: String

  """Braintree PayPal Pay Later messaging PDP style logo."""
  braintree_paypal_button_location_productpage_type_messaging_logo: String

  """Braintree PayPal Pay Later messaging PDP style logo position."""
  braintree_paypal_button_location_productpage_type_messaging_logo_position: String

  """Braintree PayPal Pay Later messaging PDP show status."""
  braintree_paypal_button_location_productpage_type_messaging_show: Boolean

  """Braintree PayPal Pay Later messaging PDP style text color."""
  braintree_paypal_button_location_productpage_type_messaging_text_color: String

  """Braintree PayPal Pay Later PDP button style color."""
  braintree_paypal_button_location_productpage_type_paylater_color: String

  """Braintree PayPal Pay Later PDP button style label."""
  braintree_paypal_button_location_productpage_type_paylater_label: String

  """Braintree PayPal Pay Later PDP button style shape."""
  braintree_paypal_button_location_productpage_type_paylater_shape: String

  """Braintree PayPal Pay Later PDP button show status."""
  braintree_paypal_button_location_productpage_type_paylater_show: Boolean

  """Braintree PayPal PDP button style color."""
  braintree_paypal_button_location_productpage_type_paypal_color: String

  """Braintree PayPal PDP button style label."""
  braintree_paypal_button_location_productpage_type_paypal_label: String

  """Braintree PayPal PDP button style shape."""
  braintree_paypal_button_location_productpage_type_paypal_shape: String

  """Braintree PayPal PDP button show."""
  braintree_paypal_button_location_productpage_type_paypal_show: Boolean

  """Braintree PayPal Credit Merchant Name on the FCA Register."""
  braintree_paypal_credit_uk_merchant_name: String

  """Should display Braintree PayPal in mini-cart & cart?"""
  braintree_paypal_display_on_shopping_cart: Boolean

  """Braintree PayPal merchant's country."""
  braintree_paypal_merchant_country: String

  """Braintree PayPal override for Merchant Name."""
  braintree_paypal_merchant_name_override: String

  """Does Braintree PayPal require the customer's billing address?"""
  braintree_paypal_require_billing_address: Boolean

  """Does Braintree PayPal require the order line items?"""
  braintree_paypal_send_cart_line_items: Boolean

  """Braintree PayPal vault status."""
  braintree_paypal_vault_active: Boolean

  """Extended Config Data - checkout/cart/delete_quote_after"""
  cart_expires_in_days: Int

  """Extended Config Data - checkout/cart_link/use_qty"""
  cart_summary_display_quantity: Int

  """The default sort order of the search results list."""
  catalog_default_sort_by: String

  """
  Corresponds to the 'Display Prices In Product Lists' field in the Admin. It indicates how FPT information is displayed on category pages.
  """
  category_fixed_product_tax_display_setting: FixedProductTaxDisplaySettings

  """The suffix applied to category pages, such as `.htm` or `.html`."""
  category_url_suffix: String

  """Indicates whether only specific countries can use this payment method."""
  check_money_order_enable_for_specific_countries: Boolean

  """Indicates whether the Check/Money Order payment method is enabled."""
  check_money_order_enabled: Boolean

  """The name of the party to whom the check must be payable."""
  check_money_order_make_check_payable_to: String

  """
  The maximum order amount required to qualify for the Check/Money Order payment method.
  """
  check_money_order_max_order_total: String

  """
  The minimum order amount required to qualify for the Check/Money Order payment method.
  """
  check_money_order_min_order_total: String

  """
  The status of new orders placed using the Check/Money Order payment method.
  """
  check_money_order_new_order_status: String

  """
  A comma-separated list of specific countries allowed to use the Check/Money Order payment method.
  """
  check_money_order_payment_from_specific_countries: String

  """The full street address or PO Box where the checks are mailed."""
  check_money_order_send_check_to: String

  """
  A number indicating the position of the Check/Money Order payment method in the list of available payment methods during checkout.
  """
  check_money_order_sort_order: Int

  """
  The title of the Check/Money Order payment method displayed on the storefront.
  """
  check_money_order_title: String

  """The name of the CMS page that identifies the home page for the store."""
  cms_home_page: String

  """
  A specific CMS page that displays when cookies are not enabled for the browser.
  """
  cms_no_cookies: String

  """
  A specific CMS page that displays when a 404 'Page Not Found' error occurs.
  """
  cms_no_route: String

  """A code assigned to the store to identify it."""
  code: String @deprecated(reason: "Use `store_code` instead.")

  """
  Indicates whether the `parent` or child (`itself`) thumbnail should be used in the cart for configurable products.
  """
  configurable_thumbnail_source: String

  """Indicates whether the Contact Us form in enabled."""
  contact_enabled: Boolean!

  """The copyright statement that appears at the bottom of each page."""
  copyright: String

  """Extended Config Data - general/region/state_required"""
  countries_with_required_region: String

  """Indicates if the new accounts need confirmation."""
  create_account_confirmation: Boolean

  """Customer access token lifetime."""
  customer_access_token_lifetime: Float

  """Extended Config Data - general/country/default"""
  default_country: String

  """
  The description that provides a summary of your site for search engine listings. It should not be more than 160 characters in length.
  """
  default_description: String

  """The default display currency code."""
  default_display_currency_code: String

  """
  A series of keywords that describe your store, each separated by a comma.
  """
  default_keywords: String

  """
  The title that appears at the title bar of each page when viewed in a browser.
  """
  default_title: String

  """
  Controls the display of the demo store notice at the top of the page. Options: 0 (No) or 1 (Yes).
  """
  demonotice: Int

  """Extended Config Data - general/region/display_all"""
  display_state_if_optional: Boolean

  """The landing page that is associated with the base URL."""
  front: String

  """The default number of products per page in Grid View."""
  grid_per_page: Int

  """
  A list of numbers that define how many products can be displayed in Grid View.
  """
  grid_per_page_values: String

  """
  Scripts that must be included in the HTML before the closing `<head>` tag.
  """
  head_includes: String

  """
  The small graphic image (favicon) that appears in the address bar and tab of the browser.
  """
  head_shortcut_icon: String

  """The path to the logo that appears in the header."""
  header_logo_src: String

  """The ID number assigned to the store."""
  id: Int @deprecated(reason: "Use `store_code` instead.")

  """
  Indicates whether the store view has been designated as the default within the store group.
  """
  is_default_store: Boolean

  """
  Indicates whether the store group has been designated as the default within the website.
  """
  is_default_store_group: Boolean

  """Extended Config Data - checkout/options/guest_checkout"""
  is_guest_checkout_enabled: Boolean

  """Extended Config Data - checkout/options/onepage_checkout_enabled"""
  is_one_page_checkout_enabled: Boolean

  """The format of the search results list."""
  list_mode: String

  """The default number of products per page in List View."""
  list_per_page: Int

  """
  A list of numbers that define how many products can be displayed in List View.
  """
  list_per_page_values: String

  """The store locale."""
  locale: String

  """The Alt text that is associated with the logo."""
  logo_alt: String

  """The height of the logo image, in pixels."""
  logo_height: Int

  """The width of the logo image, in pixels."""
  logo_width: Int

  """Indicates whether wishlists are enabled (1) or disabled (0)."""
  magento_wishlist_general_is_enabled: String

  """Extended Config Data - checkout/options/max_items_display_count"""
  max_items_in_order_summary: Int

  """Extended Config Data - checkout/sidebar/display"""
  minicart_display: Boolean

  """Extended Config Data - checkout/sidebar/count"""
  minicart_max_items: Int

  """The minimum number of characters required for a valid password."""
  minimum_password_length: String

  """Indicates whether newsletters are enabled."""
  newsletter_enabled: Boolean!

  """
  The default page that displays when a 404 'Page not Found' error occurs.
  """
  no_route: String

  """Extended Config Data - general/country/optional_zip_countries"""
  optional_zip_countries: String

  """Indicates whether orders can be cancelled by customers or not."""
  order_cancellation_enabled: Boolean!

  """An array containing available cancellation reasons."""
  order_cancellation_reasons: [CancellationReason]!

  """Payflow Pro vault status."""
  payment_payflowpro_cc_vault_active: String

  """
  Corresponds to the 'Display Prices On Product View Page' field in the Admin. It indicates how FPT information is displayed on product pages.
  """
  product_fixed_product_tax_display_setting: FixedProductTaxDisplaySettings

  """
  Indicates whether product reviews are enabled. Possible values: 1 (Yes) and 0 (No).
  """
  product_reviews_enabled: String

  """The suffix applied to product pages, such as `.htm` or `.html`."""
  product_url_suffix: String

  """
  The number of different character classes (lowercase, uppercase, digits, special characters) required in a password.
  """
  required_character_classes_number: String

  """The ID of the root category."""
  root_category_id: Int @deprecated(reason: "Use `root_category_uid` instead.")

  """The unique ID for a `CategoryInterface` object."""
  root_category_uid: ID

  """
  Corresponds to the 'Display Prices In Sales Modules' field in the Admin. It indicates how FPT information is displayed on cart, checkout, and order pages.
  """
  sales_fixed_product_tax_display_setting: FixedProductTaxDisplaySettings

  """
  A secure fully-qualified URL that is used to create relative links to the `base_url`.
  """
  secure_base_link_url: String

  """
  The secure fully-qualified URL that specifies the location of media files.
  """
  secure_base_media_url: String

  """
  The secure fully-qualified URL that specifies the location of static view files.
  """
  secure_base_static_url: String

  """The store’s fully-qualified secure base URL."""
  secure_base_url: String

  """Email to a Friend configuration."""
  send_friend: SendFriendConfiguration

  """Extended Config Data - tax/cart_display/full_summary"""
  shopping_cart_display_full_summary: Boolean

  """Extended Config Data - tax/cart_display/grandtotal"""
  shopping_cart_display_grand_total: Boolean

  """Extended Config Data - tax/cart_display/price"""
  shopping_cart_display_price: Int

  """Extended Config Data - tax/cart_display/shipping"""
  shopping_cart_display_shipping: Int

  """Extended Config Data - tax/cart_display/subtotal"""
  shopping_cart_display_subtotal: Int

  """Extended Config Data - tax/cart_display/gift_wrapping"""
  shopping_cart_display_tax_gift_wrapping: TaxWrappingEnum

  """Extended Config Data - tax/cart_display/zero_tax"""
  shopping_cart_display_zero_tax: Boolean

  """
  Indicates whether a breadcrumb trail appears on all CMS pages in the catalog. 0 (No) or 1 (Yes).
  """
  show_cms_breadcrumbs: Int

  """
  The unique ID of the store view. In the Admin, this is called the Store View Code. When making a GraphQL call, assign this value to the `Store` header to provide the scope.
  """
  store_code: ID

  """
  The unique ID assigned to the store group. In the Admin, this is called the Store Name.
  """
  store_group_code: ID

  """The label assigned to the store group."""
  store_group_name: String

  """The label assigned to the store view."""
  store_name: String

  """The store view sort order."""
  store_sort_order: Int

  """The time zone of the store."""
  timezone: String

  """
  A prefix that appears before the title to create a two- or three-part title.
  """
  title_prefix: String

  """
  The character that separates the category name and subcategory in the browser title bar.
  """
  title_separator: String

  """
  A suffix that appears after the title to create a two- or three-part title.
  """
  title_suffix: String

  """Indicates whether the store code should be used in the URL."""
  use_store_in_url: Boolean

  """The unique ID for the website."""
  website_code: ID

  """The ID number assigned to the website store."""
  website_id: Int @deprecated(reason: "The field should not be used on the storefront.")

  """The label assigned to the website."""
  website_name: String

  """The unit of weight."""
  weight_unit: String

  """
  Text that appears in the header of the page and includes the name of the logged in customer.
  """
  welcome: String

  """Indicates whether only specific countries can use this payment method."""
  zero_subtotal_enable_for_specific_countries: Boolean

  """Indicates whether the Zero Subtotal payment method is enabled."""
  zero_subtotal_enabled: Boolean

  """
  The status of new orders placed using the Zero Subtotal payment method.
  """
  zero_subtotal_new_order_status: String

  """
  When the new order status is 'Processing', this can be set to `authorize_capture` to automatically invoice all items that have a zero balance.
  """
  zero_subtotal_payment_action: String

  """
  A comma-separated list of specific countries allowed to use the Zero Subtotal payment method.
  """
  zero_subtotal_payment_from_specific_countries: String

  """
  A number indicating the position of the Zero Subtotal payment method in the list of available payment methods during checkout.
  """
  zero_subtotal_sort_order: Int

  """
  The title of the Zero Subtotal payment method displayed on the storefront.
  """
  zero_subtotal_title: String
}

"""Defines details about an individual checkout agreement."""
type CheckoutAgreement {
  """The ID for a checkout agreement."""
  agreement_id: Int!

  """The checkbox text for the checkout agreement."""
  checkbox_text: String!

  """Required. The text of the agreement."""
  content: String!

  """
  The height of the text box where the Terms and Conditions statement appears during checkout.
  """
  content_height: String

  """Indicates whether the `content` text is in HTML format."""
  is_html: Boolean!

  """Indicates whether agreements are accepted automatically or manually."""
  mode: CheckoutAgreementMode!

  """The name given to the condition."""
  name: String!
}

"""Indicates how agreements are accepted."""
enum CheckoutAgreementMode {
  """Conditions are automatically accepted upon checkout."""
  AUTO

  """Shoppers must manually accept the conditions to place an order."""
  MANUAL
}

"""Contains details about a CMS page."""
type CmsPage implements RoutableInterface {
  """The content of the CMS page in raw HTML."""
  content: String

  """The heading that displays at the top of the CMS page."""
  content_heading: String

  """The ID of a CMS page."""
  identifier: String

  """A brief description of the page for search results listings."""
  meta_description: String

  """A brief description of the page for search results listings."""
  meta_keywords: String

  """
  A page title that is indexed by search engines and appears in search results listings.
  """
  meta_title: String

  """
  The design layout of the page, indicating the number of columns and navigation features used on the page.
  """
  page_layout: String

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """
  The name that appears in the breadcrumb trail navigation and in the browser title bar and tab.
  """
  title: String

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  The URL key of the CMS page, which is often based on the `content_heading`.
  """
  url_key: String
}

"""Contains an array CMS block items."""
type CmsBlocks {
  """An array of CMS blocks."""
  items: [CmsBlock]
}

"""Contains details about a specific CMS block."""
type CmsBlock {
  """The content of the CMS block in raw HTML."""
  content: String

  """The CMS block identifier."""
  identifier: String

  """The title assigned to the CMS block."""
  title: String
}

"""This enumeration defines the entity type."""
enum UrlRewriteEntityTypeEnum {
  """"""
  CMS_PAGE

  """"""
  PRODUCT

  """"""
  CATEGORY
}

"""
Deprecated. Use `ProductPrice` instead. Defines the price of a product as well as any tax-related adjustments.
"""
type Price {
  """
  An array that provides information about tax, weee, or weee_tax adjustments.
  """
  adjustments: [PriceAdjustment] @deprecated(reason: "Use `ProductPrice` instead.")

  """The price of a product plus a three-letter currency code."""
  amount: Money @deprecated(reason: "Use `ProductPrice` instead.")
}

"""
Deprecated. Taxes will be included or excluded in the price. Defines the amount of money to apply as an adjustment, the type of adjustment to apply, and whether the item is included or excluded from the adjustment.
"""
type PriceAdjustment {
  """The amount of the price adjustment and its currency code."""
  amount: Money

  """Indicates whether the adjustment involves tax, weee, or weee_tax."""
  code: PriceAdjustmentCodesEnum @deprecated(reason: "`PriceAdjustment` is deprecated.")

  """
  Indicates whether the entity described by the code attribute is included or excluded from the adjustment.
  """
  description: PriceAdjustmentDescriptionEnum @deprecated(reason: "`PriceAdjustment` is deprecated.")
}

"""`PriceAdjustment.code` is deprecated."""
enum PriceAdjustmentCodesEnum {
  """"""
  TAX @deprecated(reason: "`PriceAdjustmentCodesEnum` is deprecated. Tax is included or excluded in the price. Tax is not shown separately in Catalog.")

  """"""
  WEEE @deprecated(reason: "WEEE code is deprecated. Use `fixed_product_taxes.label` instead.")

  """"""
  WEEE_TAX @deprecated(reason: "Use `fixed_product_taxes` instead.  Tax is included or excluded in price. The tax is not shown separtely in Catalog.")
}

"""
`PriceAdjustmentDescriptionEnum` is deprecated. States whether a price adjustment is included or excluded.
"""
enum PriceAdjustmentDescriptionEnum {
  """"""
  INCLUDED

  """"""
  EXCLUDED
}

"""Defines the price type."""
enum PriceTypeEnum {
  """"""
  FIXED

  """"""
  PERCENT

  """"""
  DYNAMIC
}

"""Defines the customizable date type."""
enum CustomizableDateTypeEnum {
  """"""
  DATE

  """"""
  DATE_TIME

  """"""
  TIME
}

"""
Deprecated. Use `PriceRange` instead. Contains the regular price of an item, as well as its minimum and maximum prices. Only composite products, which include bundle, configurable, and grouped products, can contain a minimum and maximum price.
"""
type ProductPrices {
  """
  The highest possible final price for all the options defined within a composite product. If you are specifying a price range, this would be the `to` value.
  """
  maximalPrice: Price @deprecated(reason: "Use `PriceRange.maximum_price` instead.")

  """
  The lowest possible final price for all the options defined within a composite product. If you are specifying a price range, this would be the `from` value.
  """
  minimalPrice: Price @deprecated(reason: "Use `PriceRange.minimum_price` instead.")

  """The base price of a product."""
  regularPrice: Price @deprecated(reason: "Use `regular_price` from `PriceRange.minimum_price` or `PriceRange.maximum_price` instead.")
}

"""
Contains the price range for a product. If the product has a single price, the minimum and maximum price will be the same.
"""
type PriceRange {
  """The highest possible price for the product."""
  maximum_price: ProductPrice

  """The lowest possible price for the product."""
  minimum_price: ProductPrice!
}

"""Represents a product price."""
type ProductPrice {
  """
  The price discount. Represents the difference between the regular and final price.
  """
  discount: ProductDiscount

  """The final price of the product after applying discounts."""
  final_price: Money!

  """
  An array of the multiple Fixed Product Taxes that can be applied to a product price.
  """
  fixed_product_taxes: [FixedProductTax]

  """The regular price of the product."""
  regular_price: Money!
}

"""Contains the discount applied to a product price."""
type ProductDiscount {
  """The actual value of the discount."""
  amount_off: Float

  """The discount expressed a percentage."""
  percent_off: Float
}

"""An implementation of `ProductLinksInterface`."""
type ProductLinks implements ProductLinksInterface {
  """One of related, associated, upsell, or crosssell."""
  link_type: String

  """The SKU of the linked product."""
  linked_product_sku: String

  """
  The type of linked product (simple, virtual, bundle, downloadable, grouped, configurable).
  """
  linked_product_type: String

  """The position within the list of product links."""
  position: Int

  """The identifier of the linked product."""
  sku: String
}

"""
Contains information about linked products, including the link type and product type of each item.
"""
interface ProductLinksInterface {
  """One of related, associated, upsell, or crosssell."""
  link_type: String

  """The SKU of the linked product."""
  linked_product_sku: String

  """
  The type of linked product (simple, virtual, bundle, downloadable, grouped, configurable).
  """
  linked_product_type: String

  """The position within the list of product links."""
  position: Int

  """The identifier of the linked product."""
  sku: String
}

"""Contains fields that are common to all types of products."""
interface ProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")
}

"""Contains attributes specific to tangible products."""
interface PhysicalProductInterface {
  """The weight of the item, in units defined by the store."""
  weight: Float
}

"""
Contains information about a text area that is defined as part of a customizable option.
"""
type CustomizableAreaOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """The Stock Keeping Unit of the base product."""
  product_sku: String

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An object that defines a text area."""
  value: CustomizableAreaValue
}

"""
Defines the price and sku of a product whose page contains a customized text area.
"""
type CustomizableAreaValue {
  """
  The maximum number of characters that can be entered for this customizable option.
  """
  max_characters: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The unique ID for a `CustomizableAreaValue` object."""
  uid: ID!
}

"""Contains the hierarchy of categories."""
type CategoryTree implements CategoryInterface & RoutableInterface {
  available_sort_by: [String]

  """An array of breadcrumb items."""
  breadcrumbs: [Breadcrumb]

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Categories' is enabled.
  """
  canonical_url: String

  """A tree of child categories."""
  children: [CategoryTree]
  children_count: String

  """Contains a category CMS block."""
  cms_block: CmsBlock

  """The timestamp indicating when the category was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")
  custom_layout_update_file: String

  """The attribute to use for sorting."""
  default_sort_by: String

  """An optional description of the category."""
  description: String
  display_mode: String
  filter_price_range: Float

  """An ID that uniquely identifies the category."""
  id: Int @deprecated(reason: "Use `uid` instead.")
  image: String
  include_in_menu: Int
  is_anchor: Int
  landing_page: Int

  """The depth of the category within the tree."""
  level: Int
  meta_description: String
  meta_keywords: String
  meta_title: String

  """The display name of the category."""
  name: String

  """The full category path."""
  path: String

  """The category path within the store."""
  path_in_store: String

  """
  The position of the category relative to other categories at the same level in tree.
  """
  position: Int

  """
  The number of products in the category that are marked as visible. By default, in complex products, parent products are visible, but their child products are not.
  """
  product_count: Int

  """The list of products assigned to the category."""
  products(
    """
    The maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """The page of results to return. The default value is 1."""
    currentPage: Int = 1

    """
    The attributes to sort on, and whether to return the results in ascending or descending order.
    """
    sort: ProductAttributeSortInput
  ): CategoryProducts

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """The unique ID for a `CategoryInterface` object."""
  uid: ID!

  """The timestamp indicating when the category was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """The URL key assigned to the category."""
  url_key: String

  """The URL path assigned to the category."""
  url_path: String

  """The part of the category URL that is appended after the url key"""
  url_suffix: String
}

"""
Contains a collection of `CategoryTree` objects and pagination information.
"""
type CategoryResult {
  """A list of categories that match the filter criteria."""
  items: [CategoryTree]

  """
  An object that includes the `page_info` and `currentPage` values specified in the query.
  """
  page_info: SearchResultPageInfo

  """The total number of categories that match the criteria."""
  total_count: Int
}

"""
Contains information about a date picker that is defined as part of a customizable option.
"""
type CustomizableDateOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """The Stock Keeping Unit of the base product."""
  product_sku: String

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An object that defines a date field in a customizable option."""
  value: CustomizableDateValue
}

"""
Defines the price and sku of a product whose page contains a customized date picker.
"""
type CustomizableDateValue {
  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """DATE, DATE_TIME or TIME"""
  type: CustomizableDateTypeEnum

  """The unique ID for a `CustomizableDateValue` object."""
  uid: ID!
}

"""
Contains information about a drop down menu that is defined as part of a customizable option.
"""
type CustomizableDropDownOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An array that defines the set of options for a drop down menu."""
  value: [CustomizableDropDownValue]
}

"""
Defines the price and sku of a product whose page contains a customized drop down menu.
"""
type CustomizableDropDownValue {
  """The ID assigned to the value."""
  option_type_id: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableDropDownValue` object."""
  uid: ID!
}

"""
Contains information about a multiselect that is defined as part of a customizable option.
"""
type CustomizableMultipleOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An array that defines the set of options for a multiselect."""
  value: [CustomizableMultipleValue]
}

"""
Defines the price and sku of a product whose page contains a customized multiselect.
"""
type CustomizableMultipleValue {
  """The ID assigned to the value."""
  option_type_id: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableMultipleValue` object."""
  uid: ID!
}

"""
Contains information about a text field that is defined as part of a customizable option.
"""
type CustomizableFieldOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """The Stock Keeping Unit of the base product."""
  product_sku: String

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An object that defines a text field."""
  value: CustomizableFieldValue
}

"""
Defines the price and sku of a product whose page contains a customized text field.
"""
type CustomizableFieldValue {
  """
  The maximum number of characters that can be entered for this customizable option.
  """
  max_characters: Int

  """The price of the custom value."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The unique ID for a `CustomizableFieldValue` object."""
  uid: ID!
}

"""
Contains information about a file picker that is defined as part of a customizable option.
"""
type CustomizableFileOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """The Stock Keeping Unit of the base product."""
  product_sku: String

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An object that defines a file value."""
  value: CustomizableFileValue
}

"""
Defines the price and sku of a product whose page contains a customized file picker.
"""
type CustomizableFileValue {
  """The file extension to accept."""
  file_extension: String

  """The maximum width of an image."""
  image_size_x: Int

  """The maximum height of an image."""
  image_size_y: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The unique ID for a `CustomizableFileValue` object."""
  uid: ID!
}

"""Contains basic information about a product image or video."""
interface MediaGalleryInterface {
  """Indicates whether the image is hidden from view."""
  disabled: Boolean

  """The label of the product image or video."""
  label: String

  """The media item's position after it has been sorted."""
  position: Int

  """The URL of the product image or video."""
  url: String
}

"""Contains product image information, including the image URL and label."""
type ProductImage implements MediaGalleryInterface {
  """Indicates whether the image is hidden from view."""
  disabled: Boolean

  """The label of the product image or video."""
  label: String

  """The media item's position after it has been sorted."""
  position: Int

  """The URL of the product image or video."""
  url: String
}

"""Contains information about a product video."""
type ProductVideo implements MediaGalleryInterface {
  """Indicates whether the image is hidden from view."""
  disabled: Boolean

  """The label of the product image or video."""
  label: String

  """The media item's position after it has been sorted."""
  position: Int

  """The URL of the product image or video."""
  url: String

  """Contains a `ProductMediaGalleryEntriesVideoContent` object."""
  video_content: ProductMediaGalleryEntriesVideoContent
}

"""
Contains basic information about a customizable option. It can be implemented by several types of configurable options.
"""
interface CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!
}

"""Contains information about customizable product options."""
interface CustomizableProductInterface {
  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]
}

"""
Contains the full set of attributes that can be returned in a category search.
"""
interface CategoryInterface {
  available_sort_by: [String]

  """An array of breadcrumb items."""
  breadcrumbs: [Breadcrumb]

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Categories' is enabled.
  """
  canonical_url: String
  children_count: String

  """Contains a category CMS block."""
  cms_block: CmsBlock

  """The timestamp indicating when the category was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")
  custom_layout_update_file: String

  """The attribute to use for sorting."""
  default_sort_by: String

  """An optional description of the category."""
  description: String
  display_mode: String
  filter_price_range: Float

  """An ID that uniquely identifies the category."""
  id: Int @deprecated(reason: "Use `uid` instead.")
  image: String
  include_in_menu: Int
  is_anchor: Int
  landing_page: Int

  """The depth of the category within the tree."""
  level: Int
  meta_description: String
  meta_keywords: String
  meta_title: String

  """The display name of the category."""
  name: String

  """The full category path."""
  path: String

  """The category path within the store."""
  path_in_store: String

  """
  The position of the category relative to other categories at the same level in tree.
  """
  position: Int

  """
  The number of products in the category that are marked as visible. By default, in complex products, parent products are visible, but their child products are not.
  """
  product_count: Int

  """The list of products assigned to the category."""
  products(
    """
    The maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """The page of results to return. The default value is 1."""
    currentPage: Int = 1

    """
    The attributes to sort on, and whether to return the results in ascending or descending order.
    """
    sort: ProductAttributeSortInput
  ): CategoryProducts

  """The unique ID for a `CategoryInterface` object."""
  uid: ID!

  """The timestamp indicating when the category was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """The URL key assigned to the category."""
  url_key: String

  """The URL path assigned to the category."""
  url_path: String

  """The part of the category URL that is appended after the url key"""
  url_suffix: String
}

"""
Contains details about an individual category that comprises a breadcrumb.
"""
type Breadcrumb {
  """The ID of the category."""
  category_id: Int @deprecated(reason: "Use `category_uid` instead.")

  """The category level."""
  category_level: Int

  """The display name of the category."""
  category_name: String

  """The unique ID for a `Breadcrumb` object."""
  category_uid: ID!

  """The URL key of the category."""
  category_url_key: String

  """The URL path of the category."""
  category_url_path: String
}

"""
Contains information about a set of radio buttons that are defined as part of a customizable option.
"""
type CustomizableRadioOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An array that defines a set of radio buttons."""
  value: [CustomizableRadioValue]
}

"""
Defines the price and sku of a product whose page contains a customized set of radio buttons.
"""
type CustomizableRadioValue {
  """The ID assigned to the value."""
  option_type_id: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The order in which the radio button is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableRadioValue` object."""
  uid: ID!
}

"""
Contains information about a set of checkbox values that are defined as part of a customizable option.
"""
type CustomizableCheckboxOption implements CustomizableOptionInterface {
  """Option ID."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether the option is required."""
  required: Boolean

  """The order in which the option is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableOptionInterface` object."""
  uid: ID!

  """An array that defines a set of checkbox values."""
  value: [CustomizableCheckboxValue]
}

"""
Defines the price and sku of a product whose page contains a customized set of checkbox values.
"""
type CustomizableCheckboxValue {
  """The ID assigned to the value."""
  option_type_id: Int

  """The price assigned to this option."""
  price: Float

  """FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """The Stock Keeping Unit for this option."""
  sku: String

  """The order in which the checkbox value is displayed."""
  sort_order: Int

  """The display name for this option."""
  title: String

  """The unique ID for a `CustomizableCheckboxValue` object."""
  uid: ID!
}

"""
Defines a virtual product, which is a non-tangible product that does not require shipping and is not kept in inventory.
"""
type VirtualProduct implements ProductInterface & RoutableInterface & CustomizableProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")
}

"""
Defines a simple product, which is tangible and is usually sold in single units or in fixed quantities.
"""
type SimpleProduct implements ProductInterface & RoutableInterface & PhysicalProductInterface & CustomizableProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")

  """The weight of the item, in units defined by the store."""
  weight: Float
}

"""Contains the results of a `products` query."""
type Products {
  """
  A bucket that contains the attribute code and label for each filterable option.
  """
  aggregations(filter: AggregationsFilterInput): [Aggregation]

  """Layered navigation filters array."""
  filters: [LayerFilter] @deprecated(reason: "Use `aggregations` instead.")

  """An array of products that match the specified search criteria."""
  items: [ProductInterface]

  """
  An object that includes the page_info and currentPage values specified in the query.
  """
  page_info: SearchResultPageInfo

  """
  An object that includes the default sort field and all available sort fields.
  """
  sort_fields: SortFields

  """
  An array of search suggestions for case when search query have no results.
  """
  suggestions: [SearchSuggestion]

  """
  The number of products that are marked as visible. By default, in complex products, parent products are visible, but their child products are not.
  """
  total_count: Int
}

"""
An input object that specifies the filters used in product aggregations.
"""
input AggregationsFilterInput {
  """Filter category aggregations in layered navigation."""
  category: AggregationsCategoryFilterInput
}

"""Filter category aggregations in layered navigation."""
input AggregationsCategoryFilterInput {
  """
  Indicates whether to include only direct subcategories or all children categories at all levels.
  """
  includeDirectChildrenOnly: Boolean
}

"""Contains details about the products assigned to a category."""
type CategoryProducts {
  """An array of products that are assigned to the category."""
  items: [ProductInterface]

  """Pagination metadata."""
  page_info: SearchResultPageInfo

  """
  The number of products in the category that are marked as visible. By default, in complex products, parent products are visible, but their child products are not.
  """
  total_count: Int
}

"""
Defines the filters to be used in the search. A filter contains at least one attribute, a comparison operator, and the value that is being searched for.
"""
input ProductAttributeFilterInput {
  """Attribute label: Activity"""
  activity: FilterEqualTypeInput

  """Attribute label: Category Gear"""
  category_gear: FilterEqualTypeInput

  """Deprecated: use `category_uid` to filter product by category ID."""
  category_id: FilterEqualTypeInput

  """Filter product by the unique ID for a `CategoryInterface` object."""
  category_uid: FilterEqualTypeInput

  """Filter product by category URL path."""
  category_url_path: FilterEqualTypeInput

  """Attribute label: Climate"""
  climate: FilterEqualTypeInput

  """Attribute label: Collar"""
  collar: FilterEqualTypeInput

  """Attribute label: Color"""
  color: FilterEqualTypeInput

  """Attribute label: Description"""
  description: FilterMatchTypeInput

  """Attribute label: Eco Collection"""
  eco_collection: FilterEqualTypeInput

  """Attribute label: Erin Recommends"""
  erin_recommends: FilterEqualTypeInput

  """Attribute label: Features"""
  features_bags: FilterEqualTypeInput

  """Attribute label: Format"""
  format: FilterEqualTypeInput

  """Attribute label: Gender"""
  gender: FilterEqualTypeInput

  """Attribute label: Product Name"""
  name: FilterMatchTypeInput

  """Attribute label: New"""
  new: FilterEqualTypeInput

  """Attribute label: Pattern"""
  pattern: FilterEqualTypeInput

  """Attribute label: Performance Fabric"""
  performance_fabric: FilterEqualTypeInput

  """Attribute label: Price"""
  price: FilterRangeTypeInput

  """Attribute label: Sale"""
  sale: FilterEqualTypeInput

  """Attribute label: Short Description"""
  short_description: FilterMatchTypeInput

  """Attribute label: Size"""
  size: FilterEqualTypeInput

  """Attribute label: SKU"""
  sku: FilterEqualTypeInput

  """Attribute label: Sleeve"""
  sleeve: FilterEqualTypeInput

  """Attribute label: Strap/Handle"""
  strap_bags: FilterEqualTypeInput

  """Attribute label: Style Bags"""
  style_bags: FilterEqualTypeInput

  """Attribute label: Style Bottom"""
  style_bottom: FilterEqualTypeInput

  """Attribute label: Style General"""
  style_general: FilterEqualTypeInput

  """The part of the URL that identifies the product"""
  url_key: FilterEqualTypeInput
}

"""
Defines the filters to be used in the search. A filter contains at least one attribute, a comparison operator, and the value that is being searched for.
"""
input CategoryFilterInput {
  """Filter by the unique category ID for a `CategoryInterface` object."""
  category_uid: FilterEqualTypeInput

  """
  Deprecated: use 'category_uid' to filter uniquely identifiers of categories.
  """
  ids: FilterEqualTypeInput

  """Filter by the display name of the category."""
  name: FilterMatchTypeInput

  """
  Filter by the unique parent category ID for a `CategoryInterface` object.
  """
  parent_category_uid: FilterEqualTypeInput

  """
  Filter by the unique parent category ID for a `CategoryInterface` object.
  """
  parent_id: FilterEqualTypeInput

  """Filter by the part of the URL that identifies the category."""
  url_key: FilterEqualTypeInput

  """Filter by the URL path for the category."""
  url_path: FilterEqualTypeInput
}

"""
ProductFilterInput is deprecated, use @ProductAttributeFilterInput instead. ProductFilterInput defines the filters to be used in the search. A filter contains at least one attribute, a comparison operator, and the value that is being searched for.
"""
input ProductFilterInput {
  """The category ID the product belongs to."""
  category_id: FilterTypeInput

  """The product's country of origin."""
  country_of_manufacture: FilterTypeInput

  """The timestamp indicating when the product was created."""
  created_at: FilterTypeInput

  """The name of a custom layout."""
  custom_layout: FilterTypeInput

  """XML code that is applied as a layout update to the product page."""
  custom_layout_update: FilterTypeInput

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: FilterTypeInput

  """Indicates whether a gift message is available."""
  gift_message_available: FilterTypeInput

  """
  Indicates whether additional attributes have been created for the product.
  """
  has_options: FilterTypeInput

  """The relative path to the main image on the product page."""
  image: FilterTypeInput

  """The label assigned to a product image."""
  image_label: FilterTypeInput

  """A number representing the product's manufacturer."""
  manufacturer: FilterTypeInput

  """
  The numeric maximal price of the product. Do not include the currency code.
  """
  max_price: FilterTypeInput

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: FilterTypeInput

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: FilterTypeInput

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: FilterTypeInput

  """
  The numeric minimal price of the product. Do not include the currency code.
  """
  min_price: FilterTypeInput

  """The product name. Customers use this name to identify the product."""
  name: FilterTypeInput

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  news_from_date: FilterTypeInput

  """The end date for new product listings."""
  news_to_date: FilterTypeInput

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: FilterTypeInput

  """The keyword required to perform a logical OR comparison."""
  or: ProductFilterInput

  """The price of an item."""
  price: FilterTypeInput

  """Indicates whether the product has required options."""
  required_options: FilterTypeInput

  """A short description of the product. Its use depends on the theme."""
  short_description: FilterTypeInput

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: FilterTypeInput

  """The relative path to the small image, which is used on catalog pages."""
  small_image: FilterTypeInput

  """The label assigned to a product's small image."""
  small_image_label: FilterTypeInput

  """The beginning date that a product has a special price."""
  special_from_date: FilterTypeInput

  """The discounted price of the product. Do not include the currency code."""
  special_price: FilterTypeInput

  """The end date that a product has a special price."""
  special_to_date: FilterTypeInput

  """The file name of a swatch image."""
  swatch_image: FilterTypeInput

  """The relative path to the product's thumbnail image."""
  thumbnail: FilterTypeInput

  """The label assigned to a product's thumbnail image."""
  thumbnail_label: FilterTypeInput

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: FilterTypeInput

  """The timestamp indicating when the product was updated."""
  updated_at: FilterTypeInput

  """The part of the URL that identifies the product"""
  url_key: FilterTypeInput

  """"""
  url_path: FilterTypeInput

  """The weight of the item, in units defined by the store."""
  weight: FilterTypeInput
}

"""
Contains an image in base64 format and basic information about the image.
"""
type ProductMediaGalleryEntriesContent {
  """The image in base64 format."""
  base64_encoded_data: String

  """The file name of the image."""
  name: String

  """The MIME type of the file, such as image/png."""
  type: String
}

"""Contains a link to a video file and basic information about the video."""
type ProductMediaGalleryEntriesVideoContent {
  """Must be external-video."""
  media_type: String

  """A description of the video."""
  video_description: String

  """Optional data about the video."""
  video_metadata: String

  """Describes the video source."""
  video_provider: String

  """The title of the video."""
  video_title: String

  """The URL to the video."""
  video_url: String
}

"""
Deprecated. Use `ProductAttributeSortInput` instead. Specifies the attribute to use for sorting search results and indicates whether the results are sorted in ascending or descending order.
"""
input ProductSortInput {
  """The product's country of origin."""
  country_of_manufacture: SortEnum

  """The timestamp indicating when the product was created."""
  created_at: SortEnum

  """The name of a custom layout."""
  custom_layout: SortEnum

  """XML code that is applied as a layout update to the product page."""
  custom_layout_update: SortEnum

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: SortEnum

  """Indicates whether a gift message is available."""
  gift_message_available: SortEnum

  """
  Indicates whether additional attributes have been created for the product.
  """
  has_options: SortEnum

  """The relative path to the main image on the product page."""
  image: SortEnum

  """The label assigned to a product image."""
  image_label: SortEnum

  """A number representing the product's manufacturer."""
  manufacturer: SortEnum

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: SortEnum

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: SortEnum

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: SortEnum

  """The product name. Customers use this name to identify the product."""
  name: SortEnum

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  news_from_date: SortEnum

  """The end date for new product listings."""
  news_to_date: SortEnum

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: SortEnum

  """The price of the item."""
  price: SortEnum

  """Indicates whether the product has required options."""
  required_options: SortEnum

  """A short description of the product. Its use depends on the theme."""
  short_description: SortEnum

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: SortEnum

  """The relative path to the small image, which is used on catalog pages."""
  small_image: SortEnum

  """The label assigned to a product's small image."""
  small_image_label: SortEnum

  """The beginning date that a product has a special price."""
  special_from_date: SortEnum

  """The discounted price of the product."""
  special_price: SortEnum

  """The end date that a product has a special price."""
  special_to_date: SortEnum

  """Indicates the criteria to sort swatches."""
  swatch_image: SortEnum

  """The relative path to the product's thumbnail image."""
  thumbnail: SortEnum

  """The label assigned to a product's thumbnail image."""
  thumbnail_label: SortEnum

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: SortEnum

  """The timestamp indicating when the product was updated."""
  updated_at: SortEnum

  """The part of the URL that identifies the product"""
  url_key: SortEnum

  """"""
  url_path: SortEnum

  """The weight of the item, in units defined by the store."""
  weight: SortEnum
}

"""
Specifies the attribute to use for sorting search results and indicates whether the results are sorted in ascending or descending order. It's possible to sort products using searchable attributes with enabled 'Use in Filter Options' option
"""
input ProductAttributeSortInput {
  """Attribute label: Product Name"""
  name: SortEnum

  """Sort by the position assigned to each product."""
  position: SortEnum

  """Attribute label: Price"""
  price: SortEnum

  """Sort by the search relevance score (default)."""
  relevance: SortEnum
}

"""
Defines characteristics about images and videos associated with a specific product.
"""
type MediaGalleryEntry {
  """Details about the content of the media gallery item."""
  content: ProductMediaGalleryEntriesContent

  """Indicates whether the image is hidden from view."""
  disabled: Boolean

  """The path of the image on the server."""
  file: String

  """The identifier assigned to the object."""
  id: Int @deprecated(reason: "Use `uid` instead.")

  """
  The alt text displayed on the storefront when the user points to the image.
  """
  label: String

  """Either `image` or `video`."""
  media_type: String

  """The media item's position after it has been sorted."""
  position: Int

  """
  Array of image types. It can have the following values: image, small_image, thumbnail.
  """
  types: [String]

  """The unique ID for a `MediaGalleryEntry` object."""
  uid: ID!

  """Details about the content of a video item."""
  video_content: ProductMediaGalleryEntriesVideoContent
}

"""Contains information for rendering layered navigation."""
type LayerFilter {
  """An array of filter items."""
  filter_items: [LayerFilterItemInterface] @deprecated(reason: "Use `Aggregation.options` instead.")

  """The count of filter items in filter group."""
  filter_items_count: Int @deprecated(reason: "Use `Aggregation.count` instead.")

  """The name of a layered navigation filter."""
  name: String @deprecated(reason: "Use `Aggregation.label` instead.")

  """The request variable name for a filter query."""
  request_var: String @deprecated(reason: "Use `Aggregation.attribute_code` instead.")
}

""""""
interface LayerFilterItemInterface {
  """The count of items per filter."""
  items_count: Int @deprecated(reason: "Use `AggregationOption.count` instead.")

  """The label for a filter."""
  label: String @deprecated(reason: "Use `AggregationOption.label` instead.")

  """The value of a filter request variable to be used in query."""
  value_string: String @deprecated(reason: "Use `AggregationOption.value` instead.")
}

""""""
type LayerFilterItem implements LayerFilterItemInterface {
  """The count of items per filter."""
  items_count: Int @deprecated(reason: "Use `AggregationOption.count` instead.")

  """The label for a filter."""
  label: String @deprecated(reason: "Use `AggregationOption.label` instead.")

  """The value of a filter request variable to be used in query."""
  value_string: String @deprecated(reason: "Use `AggregationOption.value` instead.")
}

"""
Contains information for each filterable option (such as price, category `UID`, and custom attributes).
"""
type Aggregation {
  """Attribute code of the aggregation group."""
  attribute_code: String!

  """The number of options in the aggregation group."""
  count: Int

  """The aggregation display name."""
  label: String

  """Array of options for the aggregation."""
  options: [AggregationOption]

  """The relative position of the attribute in a layered navigation block."""
  position: Int
}

"""A string that contains search suggestion"""
type SearchSuggestion {
  """The search suggestion of existing product."""
  search: String!
}

"""Defines aggregation option fields."""
interface AggregationOptionInterface {
  """The number of items that match the aggregation option."""
  count: Int

  """The display label for an aggregation option."""
  label: String

  """The internal ID that represents the value of the option."""
  value: String!
}

"""An implementation of `AggregationOptionInterface`."""
type AggregationOption implements AggregationOptionInterface {
  """The number of items that match the aggregation option."""
  count: Int

  """The display label for an aggregation option."""
  label: String

  """The internal ID that represents the value of the option."""
  value: String!
}

"""Defines a possible sort field."""
type SortField {
  """The label of the sort field."""
  label: String

  """The attribute code of the sort field."""
  value: String
}

"""
Contains a default value for sort fields and all available sort fields.
"""
type SortFields {
  """The default sort field value."""
  default: String

  """An array of possible sort fields."""
  options: [SortField]
}

"""Contains a simple product wish list item."""
type SimpleWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""Contains a virtual product wish list item."""
type VirtualWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""Swatch attribute metadata."""
type CatalogAttributeMetadata implements CustomAttributeMetadataInterface {
  """To which catalog types an attribute can be applied."""
  apply_to: [CatalogAttributeApplyToEnum]

  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  code: ID!

  """Default attribute value."""
  default_value: String

  """The type of entity that defines the attribute."""
  entity_type: AttributeEntityTypeEnum!

  """The frontend class of the attribute."""
  frontend_class: String

  """The frontend input type of the attribute."""
  frontend_input: AttributeFrontendInputEnum

  """
  Whether a product or category attribute can be compared against another or not.
  """
  is_comparable: Boolean

  """Whether a product or category attribute can be filtered or not."""
  is_filterable: Boolean

  """
  Whether a product or category attribute can be filtered in search or not.
  """
  is_filterable_in_search: Boolean

  """Whether a product or category attribute can use HTML on front or not."""
  is_html_allowed_on_front: Boolean

  """Whether the attribute value is required."""
  is_required: Boolean!

  """Whether a product or category attribute can be searched or not."""
  is_searchable: Boolean

  """Whether the attribute value must be unique."""
  is_unique: Boolean!

  """
  Whether a product or category attribute can be used for price rules or not.
  """
  is_used_for_price_rules: Boolean

  """
  Whether a product or category attribute is used for promo rules or not.
  """
  is_used_for_promo_rules: Boolean

  """
  Whether a product or category attribute is visible in advanced search or not.
  """
  is_visible_in_advanced_search: Boolean

  """Whether a product or category attribute is visible on front or not."""
  is_visible_on_front: Boolean

  """Whether a product or category attribute has WYSIWYG enabled or not."""
  is_wysiwyg_enabled: Boolean

  """The label assigned to the attribute."""
  label: String

  """Attribute options."""
  options: [CustomAttributeOptionInterface]!

  """Input type of the swatch attribute option."""
  swatch_input_type: SwatchInputTypeEnum

  """Whether update product preview image or not."""
  update_product_preview_image: Boolean

  """Whether use product image for swatch or not."""
  use_product_image_for_swatch: Boolean

  """
  Whether a product or category attribute is used in product listing or not.
  """
  used_in_product_listing: Boolean
}

""""""
enum CatalogAttributeApplyToEnum {
  """"""
  SIMPLE

  """"""
  VIRTUAL

  """"""
  BUNDLE

  """"""
  DOWNLOADABLE

  """"""
  CONFIGURABLE

  """"""
  GROUPED

  """"""
  CATEGORY
}

"""Product custom attributes"""
type ProductCustomAttributes {
  """Errors when retrieving custom attributes metadata."""
  errors: [AttributeMetadataError]!

  """Requested custom attributes"""
  items: [AttributeValueInterface]!
}

""""""
input CreateGuestCartInput {
  """Optional client-generated ID"""
  cart_uid: ID
}

"""Assigns a specific `cart_id` to the empty cart."""
input createEmptyCartInput {
  """The ID to assign to the cart."""
  cart_id: String
}

"""Defines the simple and group products to add to the cart."""
input AddSimpleProductsToCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """An array of simple and group items to add."""
  cart_items: [SimpleProductCartItemInput]!
}

"""Defines a single product to add to the cart."""
input SimpleProductCartItemInput {
  """An array that defines customizable options for the product."""
  customizable_options: [CustomizableOptionInput]

  """
  An object containing the `sku`, `quantity`, and other relevant information about the product.
  """
  data: CartItemInput!
}

"""Defines the virtual products to add to the cart."""
input AddVirtualProductsToCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """An array of virtual products to add."""
  cart_items: [VirtualProductCartItemInput]!
}

"""Defines a single product to add to the cart."""
input VirtualProductCartItemInput {
  """An array that defines customizable options for the product."""
  customizable_options: [CustomizableOptionInput]

  """
  An object containing the `sku`, `quantity`, and other relevant information about the product.
  """
  data: CartItemInput!
}

"""Defines an item to be added to the cart."""
input CartItemInput {
  """
  An array of entered options for the base product, such as personalization text.
  """
  entered_options: [EnteredOptionInput]

  """For a child product, the SKU of its parent product."""
  parent_sku: String

  """The amount or number of an item to add."""
  quantity: Float!

  """
  The selected options for the base product, such as color or size, using the unique ID for an object such as `CustomizableRadioOption`, `CustomizableDropDownOption`, or `ConfigurableProductOptionsValues`.
  """
  selected_options: [ID]

  """The SKU of the product."""
  sku: String!
}

"""Specifies the field to use for sorting quote items"""
enum SortQuoteItemsEnum {
  """"""
  ITEM_ID

  """"""
  CREATED_AT

  """"""
  UPDATED_AT

  """"""
  PRODUCT_ID

  """"""
  SKU

  """"""
  NAME

  """"""
  DESCRIPTION

  """"""
  WEIGHT

  """"""
  QTY

  """"""
  PRICE

  """"""
  BASE_PRICE

  """"""
  CUSTOM_PRICE

  """"""
  DISCOUNT_PERCENT

  """"""
  DISCOUNT_AMOUNT

  """"""
  BASE_DISCOUNT_AMOUNT

  """"""
  TAX_PERCENT

  """"""
  TAX_AMOUNT

  """"""
  BASE_TAX_AMOUNT

  """"""
  ROW_TOTAL

  """"""
  BASE_ROW_TOTAL

  """"""
  ROW_TOTAL_WITH_DISCOUNT

  """"""
  ROW_WEIGHT

  """"""
  PRODUCT_TYPE

  """"""
  BASE_TAX_BEFORE_DISCOUNT

  """"""
  TAX_BEFORE_DISCOUNT

  """"""
  ORIGINAL_CUSTOM_PRICE

  """"""
  PRICE_INC_TAX

  """"""
  BASE_PRICE_INC_TAX

  """"""
  ROW_TOTAL_INC_TAX

  """"""
  BASE_ROW_TOTAL_INC_TAX

  """"""
  DISCOUNT_TAX_COMPENSATION_AMOUNT

  """"""
  BASE_DISCOUNT_TAX_COMPENSATION_AMOUNT

  """"""
  FREE_SHIPPING
}

"""Specifies the field to use for sorting quote items"""
input QuoteItemsSortInput {
  """Specifies the quote items field to sort by"""
  field: SortQuoteItemsEnum!

  """Specifies the order of quote items' sorting"""
  order: SortEnum!
}

"""Defines a customizable option."""
input CustomizableOptionInput {
  """The customizable option ID of the product."""
  id: Int

  """The unique ID for a `CartItemInterface` object."""
  uid: ID

  """The string value of the option."""
  value_string: String!
}

"""Specifies the coupon code to apply to the cart."""
input ApplyCouponToCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """A valid coupon code."""
  coupon_code: String!
}

"""Modifies the specified items in the cart."""
input UpdateCartItemsInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """An array of items to be updated."""
  cart_items: [CartItemUpdateInput]!
}

"""A single item to be updated."""
input CartItemUpdateInput {
  """Deprecated. Use `cart_item_uid` instead."""
  cart_item_id: Int

  """The unique ID for a `CartItemInterface` object."""
  cart_item_uid: ID

  """An array that defines customizable options for the product."""
  customizable_options: [CustomizableOptionInput]

  """Gift message details for the cart item"""
  gift_message: GiftMessageInput

  """The new quantity of the item."""
  quantity: Float
}

"""Specifies which items to remove from the cart."""
input RemoveItemFromCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """Deprecated. Use `cart_item_uid` instead."""
  cart_item_id: Int

  """Required field. The unique ID for a `CartItemInterface` object."""
  cart_item_uid: ID
}

"""Specifies an array of addresses to use for shipping."""
input SetShippingAddressesOnCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """An array of shipping addresses."""
  shipping_addresses: [ShippingAddressInput]!
}

"""Defines a single shipping address."""
input ShippingAddressInput {
  """Defines a shipping address."""
  address: CartAddressInput

  """
  An ID from the customer's address book that uniquely identifies the address to be used for shipping.
  """
  customer_address_id: Int

  """Text provided by the shopper."""
  customer_notes: String

  """The code of Pickup Location which will be used for In-Store Pickup."""
  pickup_location_code: String
}

"""Sets the billing address."""
input SetBillingAddressOnCartInput {
  """The billing address."""
  billing_address: BillingAddressInput!

  """The unique ID of a `Cart` object."""
  cart_id: String!
}

"""Defines the billing address."""
input BillingAddressInput {
  """Defines a billing address."""
  address: CartAddressInput

  """
  An ID from the customer's address book that uniquely identifies the address to be used for billing.
  """
  customer_address_id: Int

  """
  Indicates whether to set the billing address to be the same as the existing shipping address on the cart.
  """
  same_as_shipping: Boolean

  """
  Indicates whether to set the shipping address to be the same as this billing address.
  """
  use_for_shipping: Boolean
}

"""Defines the billing or shipping address to be applied to the cart."""
input CartAddressInput {
  """The city specified for the billing or shipping address."""
  city: String!

  """The company specified for the billing or shipping address."""
  company: String

  """The country code and label for the billing or shipping address."""
  country_code: String!

  """The customer's fax number."""
  fax: String

  """The first name of the customer or guest."""
  firstname: String!

  """The last name of the customer or guest."""
  lastname: String!

  """
  The middle name of the person associated with the billing/shipping address.
  """
  middlename: String

  """The ZIP or postal code of the billing or shipping address."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """
  A string that defines the state or province of the billing or shipping address.
  """
  region: String

  """
  An integer that defines the state or province of the billing or shipping address.
  """
  region_id: Int

  """
  Determines whether to save the address in the customer's address book. The default value is true.
  """
  save_in_address_book: Boolean

  """An array containing the street for the billing or shipping address."""
  street: [String]!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The telephone number for the billing or shipping address."""
  telephone: String

  """The VAT company number for billing or shipping address."""
  vat_id: String
}

"""Applies one or shipping methods to the cart."""
input SetShippingMethodsOnCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """An array of shipping methods."""
  shipping_methods: [ShippingMethodInput]!
}

"""Defines the shipping carrier and method."""
input ShippingMethodInput {
  """
  A string that identifies a commercial carrier or an offline delivery method.
  """
  carrier_code: String!

  """
  A string that indicates which service a commercial carrier will use to ship items. For offline delivery methods, this value is similar to the label displayed on the checkout page.
  """
  method_code: String!
}

"""Applies a payment method to the quote."""
input SetPaymentMethodAndPlaceOrderInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """The payment method data to apply to the cart."""
  payment_method: PaymentMethodInput!
}

"""Specifies the quote to be converted to an order."""
input PlaceOrderInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!
}

"""Applies a payment method to the cart."""
input SetPaymentMethodOnCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """The payment method data to apply to the cart."""
  payment_method: PaymentMethodInput!
}

"""Defines the payment method."""
input PaymentMethodInput {
  """"""
  braintree: BraintreeInput

  """"""
  braintree_ach_direct_debit: BraintreeInput

  """"""
  braintree_ach_direct_debit_vault: BraintreeVaultInput

  """"""
  braintree_applepay_vault: BraintreeVaultInput

  """"""
  braintree_cc_vault: BraintreeCcVaultInput

  """"""
  braintree_googlepay_vault: BraintreeVaultInput

  """"""
  braintree_paypal: BraintreeInput

  """"""
  braintree_paypal_vault: BraintreeVaultInput

  """The internal name for the payment method."""
  code: String!

  """Required input for PayPal Hosted pro payments."""
  hosted_pro: HostedProInput

  """Required input for Payflow Express Checkout payments."""
  payflow_express: PayflowExpressInput

  """Required input for PayPal Payflow Link and Payments Advanced payments."""
  payflow_link: PayflowLinkInput

  """Required input for PayPal Payflow Pro and Payment Pro payments."""
  payflowpro: PayflowProInput

  """Required input for PayPal Payflow Pro vault payments."""
  payflowpro_cc_vault: VaultTokenInput

  """Required input for Apple Pay button"""
  payment_services_paypal_apple_pay: ApplePayMethodInput

  """Required input for Google Pay button"""
  payment_services_paypal_google_pay: GooglePayMethodInput

  """Required input for Hosted Fields"""
  payment_services_paypal_hosted_fields: HostedFieldsInput

  """Required input for Smart buttons"""
  payment_services_paypal_smart_buttons: SmartButtonMethodInput

  """Required input for vault"""
  payment_services_paypal_vault: VaultMethodInput

  """Required input for Express Checkout and Payments Standard payments."""
  paypal_express: PaypalExpressInput

  """The purchase order number. Optional for most payment methods."""
  purchase_order_number: String
}

"""Defines the guest email and cart."""
input SetGuestEmailOnCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!

  """The email address of the guest."""
  email: String!
}

"""
Contains details about the final price of items in the cart, including discount and tax information.
"""
type CartPrices {
  """
  An array containing the names and amounts of taxes applied to each item in the cart.
  """
  applied_taxes: [CartTaxItem]
  discount: CartDiscount @deprecated(reason: "Use discounts instead.")

  """
  An array containing cart rule discounts, store credit and gift cards applied to the cart.
  """
  discounts: [Discount]

  """The total, including discounts, taxes, shipping, and other fees."""
  grand_total: Money

  """The subtotal without any applied taxes."""
  subtotal_excluding_tax: Money

  """The subtotal including any applied taxes."""
  subtotal_including_tax: Money

  """The subtotal with any discounts applied, but not taxes."""
  subtotal_with_discount_excluding_tax: Money
}

"""Contains tax information about an item in the cart."""
type CartTaxItem {
  """The amount of tax applied to the item."""
  amount: Money!

  """The description of the tax."""
  label: String!
}

"""Contains information about discounts applied to the cart."""
type CartDiscount {
  """The amount of the discount applied to the item."""
  amount: Money!

  """The description of the discount."""
  label: [String]!
}

""""""
type CreateGuestCartOutput {
  """The newly created cart."""
  cart: Cart
}

"""Contains details about the cart after setting the payment method."""
type SetPaymentMethodOnCartOutput {
  """The cart after setting the payment method."""
  cart: Cart!
}

"""Contains details about the cart after setting the billing address."""
type SetBillingAddressOnCartOutput {
  """The cart after setting the billing address."""
  cart: Cart!
}

"""Contains details about the cart after setting the shipping addresses."""
type SetShippingAddressesOnCartOutput {
  """The cart after setting the shipping addresses."""
  cart: Cart!
}

"""Contains details about the cart after setting the shipping methods."""
type SetShippingMethodsOnCartOutput {
  """The cart after setting the shipping methods."""
  cart: Cart!
}

"""Contains details about the cart after applying a coupon."""
type ApplyCouponToCartOutput {
  """The cart after applying a coupon."""
  cart: Cart!
}

"""Contains the results of the request to place an order."""
type PlaceOrderOutput {
  """An array of place order errors."""
  errors: [PlaceOrderError]!

  """The ID of the order."""
  order: Order @deprecated(reason: "Use `orderV2` instead.")

  """Full order information."""
  orderV2: CustomerOrder
}

"""An error encountered while placing an order."""
type PlaceOrderError {
  """An error code that is specific to place order."""
  code: PlaceOrderErrorCodes!

  """A localized error message."""
  message: String!
}

"""
Contains the contents and other details about a guest or customer cart.
"""
type Cart {
  applied_coupon: AppliedCoupon @deprecated(reason: "Use `applied_coupons` instead.")

  """
  An array of `AppliedCoupon` objects. Each object contains the `code` text attribute, which specifies the coupon code.
  """
  applied_coupons: [AppliedCoupon]

  """An array of available payment methods."""
  available_payment_methods: [AvailablePaymentMethod]

  """The billing address assigned to the cart."""
  billing_address: BillingCartAddress

  """The email address of the guest or customer."""
  email: String

  """The entered gift message for the cart"""
  gift_message: GiftMessage

  """The unique ID for a `Cart` object."""
  id: ID!

  """Indicates whether the cart contains only virtual products."""
  is_virtual: Boolean!

  """An array of products that have been added to the cart."""
  items: [CartItemInterface] @deprecated(reason: "Use `itemsV2` instead.")
  itemsV2(pageSize: Int = 20, currentPage: Int = 1, sort: QuoteItemsSortInput): CartItems

  """Pricing details for the quote."""
  prices: CartPrices

  """Indicates which payment method was applied to the cart."""
  selected_payment_method: SelectedPaymentMethod

  """An array of shipping addresses assigned to the cart."""
  shipping_addresses: [ShippingCartAddress]!

  """The total number of items in the cart."""
  total_quantity: Float!
}

""""""
type CartItems {
  """An array of products that have been added to the cart."""
  items: [CartItemInterface]!

  """Metadata for pagination rendering."""
  page_info: SearchResultPageInfo

  """The number of returned cart items."""
  total_count: Int!
}

""""""
interface CartAddressInterface {
  """The city specified for the billing or shipping address."""
  city: String!

  """The company specified for the billing or shipping address."""
  company: String

  """An object containing the country label and code."""
  country: CartAddressCountry!

  """The customer's fax number."""
  fax: String

  """The first name of the customer or guest."""
  firstname: String!

  """The last name of the customer or guest."""
  lastname: String!

  """
  The middle name of the person associated with the billing/shipping address.
  """
  middlename: String

  """The ZIP or postal code of the billing or shipping address."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """An object containing the region label and code."""
  region: CartAddressRegion

  """An array containing the street for the billing or shipping address."""
  street: [String]!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The telephone number for the billing or shipping address."""
  telephone: String

  """The unique id of the customer address."""
  uid: String!

  """The VAT company number for billing or shipping address."""
  vat_id: String
}

"""Contains shipping addresses and methods."""
type ShippingCartAddress implements CartAddressInterface {
  """
  An array that lists the shipping methods that can be applied to the cart.
  """
  available_shipping_methods: [AvailableShippingMethod]
  cart_items: [CartItemQuantity] @deprecated(reason: "Use `cart_items_v2` instead.")

  """An array that lists the items in the cart."""
  cart_items_v2: [CartItemInterface]

  """The city specified for the billing or shipping address."""
  city: String!

  """The company specified for the billing or shipping address."""
  company: String

  """An object containing the country label and code."""
  country: CartAddressCountry!

  """Text provided by the shopper."""
  customer_notes: String

  """The customer's fax number."""
  fax: String

  """The first name of the customer or guest."""
  firstname: String!
  items_weight: Float @deprecated(reason: "This information should not be exposed on the frontend.")

  """The last name of the customer or guest."""
  lastname: String!

  """
  The middle name of the person associated with the billing/shipping address.
  """
  middlename: String
  pickup_location_code: String

  """The ZIP or postal code of the billing or shipping address."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """An object containing the region label and code."""
  region: CartAddressRegion

  """An object that describes the selected shipping method."""
  selected_shipping_method: SelectedShippingMethod

  """An array containing the street for the billing or shipping address."""
  street: [String]!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The telephone number for the billing or shipping address."""
  telephone: String

  """The unique id of the customer address."""
  uid: String!

  """The VAT company number for billing or shipping address."""
  vat_id: String
}

"""Contains details about the billing address."""
type BillingCartAddress implements CartAddressInterface {
  """The city specified for the billing or shipping address."""
  city: String!

  """The company specified for the billing or shipping address."""
  company: String

  """An object containing the country label and code."""
  country: CartAddressCountry!
  customer_notes: String @deprecated(reason: "The field is used only in shipping address.")

  """The customer's fax number."""
  fax: String

  """The first name of the customer or guest."""
  firstname: String!

  """The last name of the customer or guest."""
  lastname: String!

  """
  The middle name of the person associated with the billing/shipping address.
  """
  middlename: String

  """The ZIP or postal code of the billing or shipping address."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """An object containing the region label and code."""
  region: CartAddressRegion

  """An array containing the street for the billing or shipping address."""
  street: [String]!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The telephone number for the billing or shipping address."""
  telephone: String

  """The unique id of the customer address."""
  uid: String!

  """The VAT company number for billing or shipping address."""
  vat_id: String
}

"""
Deprecated: The `ShippingCartAddress.cart_items` field now returns `CartItemInterface`.
"""
type CartItemQuantity {
  cart_item_id: Int! @deprecated(reason: "The `ShippingCartAddress.cart_items` field now returns `CartItemInterface`.")
  quantity: Float! @deprecated(reason: "The `ShippingCartAddress.cart_items` field now returns `CartItemInterface`.")
}

"""Contains details about the region in a billing or shipping address."""
type CartAddressRegion {
  """The state or province code."""
  code: String

  """The display label for the region."""
  label: String

  """The unique ID for a pre-defined region."""
  region_id: Int
}

"""Contains details the country in a billing or shipping address."""
type CartAddressCountry {
  """The country code."""
  code: String!

  """The display label for the country."""
  label: String!
}

"""Contains details about the selected shipping method and carrier."""
type SelectedShippingMethod {
  """The cost of shipping using this shipping method."""
  amount: Money!
  base_amount: Money @deprecated(reason: "The field should not be used on the storefront.")

  """
  A string that identifies a commercial carrier or an offline shipping method.
  """
  carrier_code: String!

  """The label for the carrier code."""
  carrier_title: String!

  """A shipping method code associated with a carrier."""
  method_code: String!

  """The label for the method code."""
  method_title: String!

  """The cost of shipping using this shipping method, excluding tax."""
  price_excl_tax: Money!

  """The cost of shipping using this shipping method, including tax."""
  price_incl_tax: Money!
}

"""Contains details about the possible shipping methods and carriers."""
type AvailableShippingMethod {
  """The cost of shipping using this shipping method."""
  amount: Money!

  """Indicates whether this shipping method can be applied to the cart."""
  available: Boolean!
  base_amount: Money @deprecated(reason: "The field should not be used on the storefront.")

  """
  A string that identifies a commercial carrier or an offline shipping method.
  """
  carrier_code: String!

  """The label for the carrier code."""
  carrier_title: String!

  """Describes an error condition."""
  error_message: String

  """
  A shipping method code associated with a carrier. The value could be null if no method is available.
  """
  method_code: String

  """
  The label for the shipping method code. The value could be null if no method is available.
  """
  method_title: String

  """The cost of shipping using this shipping method, excluding tax."""
  price_excl_tax: Money!

  """The cost of shipping using this shipping method, including tax."""
  price_incl_tax: Money!
}

"""
Describes a payment method that the shopper can use to pay for the order.
"""
type AvailablePaymentMethod {
  """The payment method code."""
  code: String!

  """If the payment method is an online integration"""
  is_deferred: Boolean!

  """The payment method title."""
  title: String!
}

"""Describes the payment method the shopper selected."""
type SelectedPaymentMethod {
  """The payment method code."""
  code: String!

  """The purchase order number."""
  purchase_order_number: String

  """The payment method title."""
  title: String!
}

"""Contains the applied coupon code."""
type AppliedCoupon {
  """The coupon code the shopper applied to the card."""
  code: String!
}

"""Specifies the cart from which to remove a coupon."""
input RemoveCouponFromCartInput {
  """The unique ID of a `Cart` object."""
  cart_id: String!
}

"""Contains details about the cart after removing a coupon."""
type RemoveCouponFromCartOutput {
  """The cart after removing a coupon."""
  cart: Cart
}

"""Contains details about the cart after adding simple or group products."""
type AddSimpleProductsToCartOutput {
  """The cart after adding products."""
  cart: Cart!
}

"""Contains details about the cart after adding virtual products."""
type AddVirtualProductsToCartOutput {
  """The cart after adding products."""
  cart: Cart!
}

"""Contains details about the cart after updating items."""
type UpdateCartItemsOutput {
  """The cart after updating products."""
  cart: Cart!
}

"""Contains details about the cart after removing an item."""
type RemoveItemFromCartOutput {
  """The cart after removing an item."""
  cart: Cart!
}

"""Contains details about the cart after setting the email of a guest."""
type SetGuestEmailOnCartOutput {
  """The cart after setting the guest email."""
  cart: Cart!
}

"""An implementation for simple product cart items."""
type SimpleCartItem implements CartItemInterface {
  """An array containing the customizable options the shopper selected."""
  customizable_options: [SelectedCustomizableOption]!

  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]

  """The entered gift message for the cart item"""
  gift_message: GiftMessage
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

"""An implementation for virtual product cart items."""
type VirtualCartItem implements CartItemInterface {
  """An array containing customizable options the shopper selected."""
  customizable_options: [SelectedCustomizableOption]!

  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

"""An interface for products in a cart."""
interface CartItemInterface {
  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

""""""
type CartItemError {
  """An error code that describes the error encountered"""
  code: CartItemErrorType!

  """A localized error message"""
  message: String!
}

""""""
enum CartItemErrorType {
  """"""
  UNDEFINED

  """"""
  ITEM_QTY

  """"""
  ITEM_INCREMENTS
}

"""
Defines an individual discount. A discount can be applied to the cart as a whole or to an item, shipping.
"""
type Discount {
  """The amount of the discount."""
  amount: Money!

  """The type of the entity the discount is applied to."""
  applied_to: CartDiscountType!

  """The coupon related to the discount."""
  coupon: AppliedCoupon

  """A description of the discount."""
  label: String!
}

""""""
enum CartDiscountType {
  """"""
  ITEM

  """"""
  SHIPPING
}

"""
Contains details about the price of the item, including taxes and discounts.
"""
type CartItemPrices {
  """An array of discounts to be applied to the cart item."""
  discounts: [Discount]

  """An array of FPTs applied to the cart item."""
  fixed_product_taxes: [FixedProductTax]

  """
  The price of the item before any discounts were applied. The price that might include tax, depending on the configured display settings for cart.
  """
  price: Money!

  """
  The price of the item before any discounts were applied. The price that might include tax, depending on the configured display settings for cart.
  """
  price_including_tax: Money!

  """The value of the price multiplied by the quantity of the item."""
  row_total: Money!

  """The value of `row_total` plus the tax applied to the item."""
  row_total_including_tax: Money!

  """The total of all discounts applied to the item."""
  total_item_discount: Money
}

"""Identifies a customized product that has been placed in a cart."""
type SelectedCustomizableOption {
  """
  The unique ID for a specific `CustomizableOptionInterface` object, such as a `CustomizableFieldOption`, `CustomizableFileOption`, or `CustomizableAreaOption` object.
  """
  customizable_option_uid: ID!
  id: Int! @deprecated(reason: "Use `SelectedCustomizableOption.customizable_option_uid` instead.")

  """Indicates whether the customizable option is required."""
  is_required: Boolean!

  """The display name of the selected customizable option."""
  label: String!

  """A value indicating the order to display this option."""
  sort_order: Int!

  """The type of `CustomizableOptionInterface` object."""
  type: String!

  """An array of selectable values."""
  values: [SelectedCustomizableOptionValue]!
}

"""Identifies the value of the selected customized option."""
type SelectedCustomizableOptionValue {
  """
  The unique ID for a value object that corresponds to the object represented by the `customizable_option_uid` attribute.
  """
  customizable_option_value_uid: ID!
  id: Int! @deprecated(reason: "Use `SelectedCustomizableOptionValue.customizable_option_value_uid` instead.")

  """The display name of the selected value."""
  label: String!

  """The price of the selected customizable value."""
  price: CartItemSelectedOptionValuePrice!

  """The text identifying the selected value."""
  value: String!
}

"""Contains details about the price of a selected customizable value."""
type CartItemSelectedOptionValuePrice {
  """Indicates whether the price type is fixed, percent, or dynamic."""
  type: PriceTypeEnum!

  """A string that describes the unit of the value."""
  units: String!

  """A price value."""
  value: Float!
}

"""Contains the order ID."""
type Order {
  order_id: String @deprecated(reason: "Use `order_number` instead.")

  """The unique ID for an `Order` object."""
  order_number: String!
}

"""An error encountered while adding an item to the the cart."""
type CartUserInputError {
  """A cart-specific error code."""
  code: CartUserInputErrorType!

  """A localized error message."""
  message: String!
}

"""Contains details about the cart after adding products to it."""
type AddProductsToCartOutput {
  """The cart after products have been added."""
  cart: Cart!

  """Contains errors encountered while adding an item to the cart."""
  user_errors: [CartUserInputError]!
}

""""""
enum CartUserInputErrorType {
  """"""
  PRODUCT_NOT_FOUND

  """"""
  NOT_SALABLE

  """"""
  INSUFFICIENT_STOCK

  """"""
  UNDEFINED
}

""""""
enum PlaceOrderErrorCodes {
  """"""
  CART_NOT_FOUND

  """"""
  CART_NOT_ACTIVE

  """"""
  GUEST_EMAIL_MISSING

  """"""
  UNABLE_TO_PLACE_ORDER

  """"""
  UNDEFINED
}

""""""
input EstimateTotalsInput {
  """Customer's address to estimate totals."""
  address: EstimateAddressInput!

  """The unique ID of the cart to query."""
  cart_id: String!

  """Selected shipping method to estimate totals."""
  shipping_method: ShippingMethodInput
}

"""Estimate totals output."""
type EstimateTotalsOutput {
  """Cart after totals estimation"""
  cart: Cart
}

"""Contains details about an address."""
input EstimateAddressInput {
  """The two-letter code representing the customer's country."""
  country_code: CountryCodeEnum!

  """The customer's ZIP or postal code."""
  postcode: String

  """An object containing the region name, region code, and region ID."""
  region: CustomerAddressRegionInput
}

""""""
input ContactUsInput {
  """The shopper's comment to the merchant."""
  comment: String!

  """The email address of the shopper."""
  email: String!

  """The full name of the shopper."""
  name: String!

  """The shopper's telephone number."""
  telephone: String
}

"""Contains the status of the request."""
type ContactUsOutput {
  """Indicates whether the request was successful."""
  status: Boolean!
}

""""""
input AddDownloadableProductsToCartInput {
  """The ID of the cart."""
  cart_id: String!

  """An array of downloadable products to add."""
  cart_items: [DownloadableProductCartItemInput]!
}

"""Defines a single downloadable product."""
input DownloadableProductCartItemInput {
  """The ID and value of the option."""
  customizable_options: [CustomizableOptionInput]

  """The quantity and SKU of the downloadable product."""
  data: CartItemInput!

  """
  An array of objects containing the link_id of the downloadable product link.
  """
  downloadable_product_links: [DownloadableProductLinksInput]
}

"""Contains the link ID for the downloadable product."""
input DownloadableProductLinksInput {
  """The unique ID of the downloadable product link."""
  link_id: Int!
}

"""Contains details about the cart after adding downloadable products."""
type AddDownloadableProductsToCartOutput {
  """The cart after adding products."""
  cart: Cart!
}

"""An implementation for downloadable product cart items."""
type DownloadableCartItem implements CartItemInterface {
  """An array containing the customizable options the shopper selected."""
  customizable_options: [SelectedCustomizableOption]!

  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  An array containing information about the links for the downloadable product added to the cart.
  """
  links: [DownloadableProductLinks]

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """
  An array containing information about samples of the selected downloadable product.
  """
  samples: [DownloadableProductSamples]

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

"""Defines a product that the shopper downloads."""
type DownloadableProduct implements ProductInterface & RoutableInterface & CustomizableProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  An array containing information about the links for this downloadable product.
  """
  downloadable_product_links: [DownloadableProductLinks]

  """
  An array containing information about samples of this downloadable product.
  """
  downloadable_product_samples: [DownloadableProductSamples]
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A value of 1 indicates that each link in the array must be purchased separately.
  """
  links_purchased_separately: Int

  """The heading above the list of downloadable products."""
  links_title: String

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")
}

""""""
enum DownloadableFileTypeEnum {
  """"""
  FILE @deprecated(reason: "`sample_url` serves to get the downloadable sample")

  """"""
  URL @deprecated(reason: "`sample_url` serves to get the downloadable sample")
}

"""Defines characteristics of a downloadable product."""
type DownloadableProductLinks {
  id: Int @deprecated(reason: "This information should not be exposed on frontend.")
  is_shareable: Boolean @deprecated(reason: "This information should not be exposed on frontend.")
  link_type: DownloadableFileTypeEnum @deprecated(reason: "`sample_url` serves to get the downloadable sample")
  number_of_downloads: Int @deprecated(reason: "This information should not be exposed on frontend.")

  """The price of the downloadable product."""
  price: Float
  sample_file: String @deprecated(reason: "`sample_url` serves to get the downloadable sample")
  sample_type: DownloadableFileTypeEnum @deprecated(reason: "`sample_url` serves to get the downloadable sample")

  """The full URL to the downloadable sample."""
  sample_url: String

  """A number indicating the sort order."""
  sort_order: Int

  """The display name of the link."""
  title: String

  """The unique ID for a `DownloadableProductLinks` object."""
  uid: ID!
}

"""Defines characteristics of a downloadable product."""
type DownloadableProductSamples {
  id: Int @deprecated(reason: "This information should not be exposed on frontend.")
  sample_file: String @deprecated(reason: "`sample_url` serves to get the downloadable sample")
  sample_type: DownloadableFileTypeEnum @deprecated(reason: "`sample_url` serves to get the downloadable sample")

  """The full URL to the downloadable sample."""
  sample_url: String

  """A number indicating the sort order."""
  sort_order: Int

  """The display name of the sample."""
  title: String
}

"""Defines downloadable product options for `OrderItemInterface`."""
type DownloadableOrderItem implements OrderItemInterface {
  """The final discount information for the product."""
  discounts: [Discount]

  """
  A list of downloadable links that are ordered from the downloadable product.
  """
  downloadable_links: [DownloadableItemsLinks]

  """The entered option for the base product, such as a logo or image."""
  entered_options: [OrderItemOption]

  """The selected gift message for the order item"""
  gift_message: GiftMessage

  """The unique ID for an `OrderItemInterface` object."""
  id: ID!

  """
  The ProductInterface object, which contains details about the base product
  """
  product: ProductInterface

  """The name of the base product."""
  product_name: String

  """The sale price of the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The type of product, such as simple, configurable, etc."""
  product_type: String

  """URL key of the base product."""
  product_url_key: String

  """The number of canceled items."""
  quantity_canceled: Float

  """The number of invoiced items."""
  quantity_invoiced: Float

  """The number of units ordered for this item."""
  quantity_ordered: Float

  """The number of refunded items."""
  quantity_refunded: Float

  """The number of returned items."""
  quantity_returned: Float

  """The number of shipped items."""
  quantity_shipped: Float

  """The selected options for the base product, such as color or size."""
  selected_options: [OrderItemOption]

  """The status of the order item."""
  status: String
}

"""Defines downloadable product options for `InvoiceItemInterface`."""
type DownloadableInvoiceItem implements InvoiceItemInterface {
  """
  Information about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """
  A list of downloadable links that are invoiced from the downloadable product.
  """
  downloadable_links: [DownloadableItemsLinks]

  """The unique ID for an `InvoiceItemInterface` object."""
  id: ID!

  """Details about an individual order item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of invoiced items."""
  quantity_invoiced: Float
}

"""Defines downloadable product options for `CreditMemoItemInterface`."""
type DownloadableCreditMemoItem implements CreditMemoItemInterface {
  """
  Details about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """
  A list of downloadable links that are refunded from the downloadable product.
  """
  downloadable_links: [DownloadableItemsLinks]

  """The unique ID for a `CreditMemoItemInterface` object."""
  id: ID!

  """The order item the credit memo is applied to."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of refunded items."""
  quantity_refunded: Float
}

"""Defines characteristics of the links for downloadable product."""
type DownloadableItemsLinks {
  """A number indicating the sort order."""
  sort_order: Int

  """The display name of the link."""
  title: String

  """The unique ID for a `DownloadableItemsLinks` object."""
  uid: ID!
}

"""A downloadable product wish list item."""
type DownloadableWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """An array containing information about the selected links."""
  links_v2: [DownloadableProductLinks]

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!

  """An array containing information about the selected samples."""
  samples: [DownloadableProductSamples]
}

""""""
type Currency {
  """
  An array of three-letter currency codes accepted by the store, such as USD and EUR.
  """
  available_currency_codes: [String]

  """The base currency set for the store, such as USD."""
  base_currency_code: String

  """The symbol for the specified base currency, such as $."""
  base_currency_symbol: String
  default_display_currecy_code: String @deprecated(reason: "Symbol was missed. Use `default_display_currency_code`.")
  default_display_currecy_symbol: String @deprecated(reason: "Symbol was missed. Use `default_display_currency_code`.")

  """The currency that is displayed by default, such as USD."""
  default_display_currency_code: String

  """The currency symbol that is displayed by default, such as $."""
  default_display_currency_symbol: String

  """An array of exchange rates for currencies defined in the store."""
  exchange_rates: [ExchangeRate]
}

"""Lists the exchange rate."""
type ExchangeRate {
  """Specifies the store’s default currency to exchange to."""
  currency_to: String

  """The exchange rate for the store’s default currency."""
  rate: Float
}

""""""
type Country {
  """An array of regions within a particular country."""
  available_regions: [Region]

  """The name of the country in English."""
  full_name_english: String

  """The name of the country in the current locale."""
  full_name_locale: String

  """The unique ID for a `Country` object."""
  id: String

  """The three-letter abbreviation of the country, such as USA."""
  three_letter_abbreviation: String

  """The two-letter abbreviation of the country, such as US."""
  two_letter_abbreviation: String
}

""""""
type Region {
  """The two-letter code for the region, such as TX for Texas."""
  code: String

  """The unique ID for a `Region` object."""
  id: Int

  """The name of the region, such as Texas."""
  name: String
}

"""Contains a list of downloadable products."""
type CustomerDownloadableProducts {
  """An array of purchased downloadable items."""
  items: [CustomerDownloadableProduct]
}

"""Contains details about a single downloadable product."""
type CustomerDownloadableProduct {
  """The date and time the purchase was made."""
  date: String

  """The fully qualified URL to the download file."""
  download_url: String

  """The unique ID assigned to the item."""
  order_increment_id: String

  """The remaining number of times the customer can download the product."""
  remaining_downloads: String

  """
  Indicates when the product becomes available for download. Options are `Pending` and `Invoiced`.
  """
  status: String
}

"""
Deprecated. Use `TierPrice` instead. Defines a tier price, which is a quantity discount offered to a specific customer group.
"""
type ProductTierPrices {
  """The ID of the customer group."""
  customer_group_id: String @deprecated(reason: "Not relevant for the storefront.")

  """The percentage discount of the item."""
  percentage_value: Float @deprecated(reason: "Use `TierPrice.discount` instead.")

  """
  The number of items that must be purchased to qualify for tier pricing.
  """
  qty: Float @deprecated(reason: "Use `TierPrice.quantity` instead.")

  """The price of the fixed price item."""
  value: Float @deprecated(reason: "Use `TierPrice.final_price` instead.")

  """The ID assigned to the website."""
  website_id: Float @deprecated(reason: "Not relevant for the storefront.")
}

"""Defines a price based on the quantity purchased."""
type TierPrice {
  """The price discount that this tier represents."""
  discount: ProductDiscount

  """The price of the product at this tier."""
  final_price: Money

  """
  The minimum number of items that must be purchased to qualify for this price tier.
  """
  quantity: Float
}

"""An implementation for configurable product cart items."""
type ConfigurableCartItem implements CartItemInterface {
  """An array containing the configuranle options the shopper selected."""
  configurable_options: [SelectedConfigurableOption]!

  """Product details of the cart item."""
  configured_variant: ProductInterface!

  """An array containing the customizable options the shopper selected."""
  customizable_options: [SelectedCustomizableOption]!

  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]

  """The entered gift message for the cart item"""
  gift_message: GiftMessage
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

"""An implementation for bundle product cart items."""
type BundleCartItem implements CartItemInterface {
  """An array containing the bundle options the shopper selected."""
  bundle_options: [SelectedBundleOption]!

  """An array containing the customizable options the shopper selected."""
  customizable_options: [SelectedCustomizableOption]!

  """An array of errors encountered while loading the cart item"""
  errors: [CartItemError]

  """The entered gift message for the cart item"""
  gift_message: GiftMessage
  id: String! @deprecated(reason: "Use `uid` instead.")

  """
  True if requested quantity is less than available stock, false otherwise.
  """
  is_available: Boolean!

  """
  Contains details about the price of the item, including taxes and discounts.
  """
  prices: CartItemPrices

  """Details about an item in the cart."""
  product: ProductInterface!

  """The quantity of this item in the cart."""
  quantity: Float!

  """The unique ID for a `CartItemInterface` object."""
  uid: ID!
}

"""Contains the text of a gift message, its sender, and recipient"""
type GiftMessage {
  """Sender name"""
  from: String!

  """Gift message text"""
  message: String!

  """Recipient name"""
  to: String!
}

"""Contains the text of a gift message, its sender, and recipient"""
input GiftMessageInput {
  """Sender name"""
  from: String!

  """Gift message text"""
  message: String!

  """Recipient name"""
  to: String!
}

""""""
type SalesItemInterface {
  """The entered gift message for the order item"""
  gift_message: GiftMessage
}

"""Contains details about each of the customer's orders."""
type CustomerOrder {
  """Coupons applied to the order."""
  applied_coupons: [AppliedCoupon]!

  """The billing address for the order."""
  billing_address: OrderAddress

  """The shipping carrier for the order delivery."""
  carrier: String

  """Comments about the order."""
  comments: [SalesCommentItem]
  created_at: String @deprecated(reason: "Use the `order_date` field instead.")

  """A list of credit memos."""
  credit_memos: [CreditMemo]

  """Order customer email."""
  email: String

  """The entered gift message for the order"""
  gift_message: GiftMessage
  grand_total: Float @deprecated(reason: "Use the `totals.grand_total` field instead.")

  """The unique ID for a `CustomerOrder` object."""
  id: ID!
  increment_id: String @deprecated(reason: "Use the `id` field instead.")

  """A list of invoices for the order."""
  invoices: [Invoice]!

  """An array containing the items purchased in this order."""
  items: [OrderItemInterface]

  """The order number."""
  number: String!

  """The date the order was placed."""
  order_date: String!
  order_number: String! @deprecated(reason: "Use the `number` field instead.")

  """Payment details for the order."""
  payment_methods: [OrderPaymentMethod]

  """A list of shipments for the order."""
  shipments: [OrderShipment]

  """The shipping address for the order."""
  shipping_address: OrderAddress

  """The delivery method for the order."""
  shipping_method: String

  """The current status of the order."""
  status: String!

  """The token that can be used to retrieve the order using order query."""
  token: String!

  """Details about the calculated totals for this order."""
  total: OrderTotal
}

"""Order item details."""
interface OrderItemInterface {
  """The final discount information for the product."""
  discounts: [Discount]

  """The entered option for the base product, such as a logo or image."""
  entered_options: [OrderItemOption]

  """The selected gift message for the order item"""
  gift_message: GiftMessage

  """The unique ID for an `OrderItemInterface` object."""
  id: ID!

  """
  The ProductInterface object, which contains details about the base product
  """
  product: ProductInterface

  """The name of the base product."""
  product_name: String

  """The sale price of the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The type of product, such as simple, configurable, etc."""
  product_type: String

  """URL key of the base product."""
  product_url_key: String

  """The number of canceled items."""
  quantity_canceled: Float

  """The number of invoiced items."""
  quantity_invoiced: Float

  """The number of units ordered for this item."""
  quantity_ordered: Float

  """The number of refunded items."""
  quantity_refunded: Float

  """The number of returned items."""
  quantity_returned: Float

  """The number of shipped items."""
  quantity_shipped: Float

  """The selected options for the base product, such as color or size."""
  selected_options: [OrderItemOption]

  """The status of the order item."""
  status: String
}

"""Defines the bundle products to add to the cart."""
input AddBundleProductsToCartInput {
  """The ID of the cart."""
  cart_id: String!

  """An array of bundle products to add."""
  cart_items: [BundleProductCartItemInput]!
}

"""Defines a single bundle product."""
input BundleProductCartItemInput {
  """
  A mandatory array of options for the bundle product, including each chosen option and specified quantity.
  """
  bundle_options: [BundleOptionInput]!

  """The ID and value of the option."""
  customizable_options: [CustomizableOptionInput]

  """The quantity and SKU of the bundle product."""
  data: CartItemInput!
}

"""Defines the input for a bundle option."""
input BundleOptionInput {
  """The ID of the option."""
  id: Int!

  """The number of the selected item to add to the cart."""
  quantity: Float!

  """An array with the chosen value of the option."""
  value: [String]!
}

"""Contains details about the cart after adding bundle products."""
type AddBundleProductsToCartOutput {
  """The cart after adding products."""
  cart: Cart!
}

"""Contains details about a selected bundle option."""
type SelectedBundleOption {
  id: Int! @deprecated(reason: "Use `uid` instead")

  """The display name of the selected bundle product option."""
  label: String!

  """The type of selected bundle product option."""
  type: String!

  """The unique ID for a `SelectedBundleOption` object"""
  uid: ID!

  """An array of selected bundle option values."""
  values: [SelectedBundleOptionValue]!
}

"""Contains details about a value for a selected bundle option."""
type SelectedBundleOptionValue {
  """Use `uid` instead"""
  id: Int!

  """The display name of the value for the selected bundle product option."""
  label: String!

  """The price of the value for the selected bundle product option."""
  price: Float!

  """The quantity of the value for the selected bundle product option."""
  quantity: Float!

  """The unique ID for a `SelectedBundleOptionValue` object"""
  uid: ID!
}

"""
Can be used to retrieve the main price details in case of bundle product
"""
type PriceDetails {
  """The percentage of discount applied to the main product price"""
  discount_percentage: Float

  """The final price after applying the discount to the main product"""
  main_final_price: Float

  """The regular price of the main product"""
  main_price: Float
}

"""Defines an individual item within a bundle product."""
type BundleItem {
  """An ID assigned to each type of item in a bundle product."""
  option_id: Int @deprecated(reason: "Use `uid` instead")

  """An array of additional options for this bundle item."""
  options: [BundleItemOption]

  """
  A number indicating the sequence order of this item compared to the other bundle items.
  """
  position: Int

  """The range of prices for the product"""
  price_range: PriceRange!

  """Indicates whether the item must be included in the bundle."""
  required: Boolean

  """The SKU of the bundle product."""
  sku: String

  """The display name of the item."""
  title: String

  """
  The input type that the customer uses to select the item. Examples include radio button and checkbox.
  """
  type: String

  """The unique ID for a `BundleItem` object."""
  uid: ID
}

"""
Defines the characteristics that comprise a specific bundle item and its options.
"""
type BundleItemOption {
  """
  Indicates whether the customer can change the number of items for this option.
  """
  can_change_quantity: Boolean

  """The ID assigned to the bundled item option."""
  id: Int @deprecated(reason: "Use `uid` instead")

  """Indicates whether this option is the default option."""
  is_default: Boolean

  """The text that identifies the bundled item option."""
  label: String

  """
  When a bundle item contains multiple options, the relative position of this option compared to the other options.
  """
  position: Int

  """The price of the selected option."""
  price: Float

  """One of FIXED, PERCENT, or DYNAMIC."""
  price_type: PriceTypeEnum

  """Contains details about this product option."""
  product: ProductInterface

  """Indicates the quantity of this specific bundle item."""
  qty: Float @deprecated(reason: "Use `quantity` instead.")

  """The quantity of this specific bundle item."""
  quantity: Float

  """The unique ID for a `BundleItemOption` object."""
  uid: ID!
}

"""
Defines basic features of a bundle product and contains multiple BundleItems.
"""
type BundleProduct implements ProductInterface & RoutableInterface & PhysicalProductInterface & CustomizableProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether the bundle product has a dynamic price."""
  dynamic_price: Boolean

  """Indicates whether the bundle product has a dynamic SKU."""
  dynamic_sku: Boolean

  """
  Indicates whether the bundle product has a dynamically calculated weight.
  """
  dynamic_weight: Boolean
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array containing information about individual bundle items."""
  items: [BundleItem]
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The price details of the main product"""
  price_details: PriceDetails

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]

  """One of PRICE_RANGE or AS_LOW_AS."""
  price_view: PriceViewEnum
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether to ship bundle items together or individually."""
  ship_bundle_items: ShipBundleItemsEnum

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")

  """The weight of the item, in units defined by the store."""
  weight: Float
}

"""
Defines whether a bundle product's price is displayed as the lowest possible value or as a range.
"""
enum PriceViewEnum {
  """"""
  PRICE_RANGE

  """"""
  AS_LOW_AS
}

"""Defines whether bundle items must be shipped together."""
enum ShipBundleItemsEnum {
  """"""
  TOGETHER

  """"""
  SEPARATELY
}

"""Defines bundle product options for `OrderItemInterface`."""
type BundleOrderItem implements OrderItemInterface {
  """A list of bundle options that are assigned to the bundle product."""
  bundle_options: [ItemSelectedBundleOption]

  """The final discount information for the product."""
  discounts: [Discount]

  """The entered option for the base product, such as a logo or image."""
  entered_options: [OrderItemOption]

  """The selected gift message for the order item"""
  gift_message: GiftMessage

  """The unique ID for an `OrderItemInterface` object."""
  id: ID!

  """
  The ProductInterface object, which contains details about the base product
  """
  product: ProductInterface

  """The name of the base product."""
  product_name: String

  """The sale price of the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The type of product, such as simple, configurable, etc."""
  product_type: String

  """URL key of the base product."""
  product_url_key: String

  """The number of canceled items."""
  quantity_canceled: Float

  """The number of invoiced items."""
  quantity_invoiced: Float

  """The number of units ordered for this item."""
  quantity_ordered: Float

  """The number of refunded items."""
  quantity_refunded: Float

  """The number of returned items."""
  quantity_returned: Float

  """The number of shipped items."""
  quantity_shipped: Float

  """The selected options for the base product, such as color or size."""
  selected_options: [OrderItemOption]

  """The status of the order item."""
  status: String
}

"""Defines bundle product options for `InvoiceItemInterface`."""
type BundleInvoiceItem implements InvoiceItemInterface {
  """
  A list of bundle options that are assigned to an invoiced bundle product.
  """
  bundle_options: [ItemSelectedBundleOption]

  """
  Information about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for an `InvoiceItemInterface` object."""
  id: ID!

  """Details about an individual order item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of invoiced items."""
  quantity_invoiced: Float
}

"""Defines bundle product options for `ShipmentItemInterface`."""
type BundleShipmentItem implements ShipmentItemInterface {
  """A list of bundle options that are assigned to a shipped product."""
  bundle_options: [ItemSelectedBundleOption]

  """The unique ID for a `ShipmentItemInterface` object."""
  id: ID!

  """The order item associated with the shipment item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of shipped items."""
  quantity_shipped: Float!
}

"""Defines bundle product options for `CreditMemoItemInterface`."""
type BundleCreditMemoItem implements CreditMemoItemInterface {
  """
  A list of bundle options that are assigned to a bundle product that is part of a credit memo.
  """
  bundle_options: [ItemSelectedBundleOption]

  """
  Details about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for a `CreditMemoItemInterface` object."""
  id: ID!

  """The order item the credit memo is applied to."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of refunded items."""
  quantity_refunded: Float
}

"""A list of options of the selected bundle product."""
type ItemSelectedBundleOption {
  """The unique ID for a `ItemSelectedBundleOption` object."""
  id: ID! @deprecated(reason: "Use `uid` instead.")

  """The label of the option."""
  label: String!

  """The unique ID for a `ItemSelectedBundleOption` object."""
  uid: ID!

  """A list of products that represent the values of the parent option."""
  values: [ItemSelectedBundleOptionValue]
}

"""A list of values for the selected bundle product."""
type ItemSelectedBundleOptionValue {
  """The unique ID for a `ItemSelectedBundleOptionValue` object."""
  id: ID! @deprecated(reason: "Use `uid` instead.")

  """The price of the child bundle product."""
  price: Money!

  """The name of the child bundle product."""
  product_name: String!

  """The SKU of the child bundle product."""
  product_sku: String!

  """The number of this bundle product that were ordered."""
  quantity: Float!

  """The unique ID for a `ItemSelectedBundleOptionValue` object."""
  uid: ID!
}

"""Defines bundle product options for `WishlistItemInterface`."""
type BundleWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """An array containing information about the selected bundle items."""
  bundle_options: [SelectedBundleOption]

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""
Defines a grouped product, which consists of simple standalone products that are presented as a group.
"""
type GroupedProduct implements ProductInterface & RoutableInterface & PhysicalProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array containing grouped product items."""
  items: [GroupedProductItem]
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")

  """The weight of the item, in units defined by the store."""
  weight: Float
}

"""Contains information about an individual grouped product item."""
type GroupedProductItem {
  """The relative position of this item compared to the other group items."""
  position: Int

  """Details about this product option."""
  product: ProductInterface

  """The quantity of this grouped product item."""
  qty: Float
}

"""A grouped product wish list item."""
type GroupedProductWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""
Defines basic features of a configurable product and its simple product variants.
"""
type ConfigurableProduct implements ProductInterface & RoutableInterface & PhysicalProductInterface & CustomizableProductInterface {
  Installation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Installation_Height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  Length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  activity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The attribute set assigned to the product."""
  attribute_set_id: Int @deprecated(reason: "The field should not be used on the storefront.")
  basin_mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_feet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bath_style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  bracket_weight_tolerance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brakcet_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  brand: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  button_location: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The relative canonical URL. This value is returned only if the system setting 'Use Canonical Link Meta Tag For Products' is enabled.
  """
  canonical_url: String
  capacity: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The categories assigned to a product."""
  categories: [CategoryInterface]
  category_gear: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  certification: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_frame_required: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cistern_prong_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  cleaning: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  climate: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  closing_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  coating: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  collar: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  color: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  concealed_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for the configurable product."""
  configurable_options: [ConfigurableProductOptions]

  """
  An array of media gallery items and other details about selected configurable product options as well as details about remaining selectable options.
  """
  configurable_product_options_selection(configurableOptionValueUids: [ID!]): ConfigurableProductOptionsSelection

  """The product's country of origin."""
  country_of_manufacture: String

  """Timestamp indicating when the product was created."""
  created_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Crosssell Products"""
  crosssell_products: [ProductInterface]

  """Product custom attributes."""
  custom_attributesV2(filters: AttributeFilterInput): ProductCustomAttributes

  """
  Detailed information about the product. The value can include simple HTML tags.
  """
  description: ComplexTextValue
  door_handle: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  drainage_capacity_max: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  drum: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  ean: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  eco_collection: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  erin_recommends: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  excludes_new: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  features_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  flush_volume: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  format: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  frame: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_weight: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  front_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  gender: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates whether a gift message is available."""
  gift_message_available: String
  glass_thickness: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handed: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  handset_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_centres: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_fitting_distance: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  hinge_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The ID number assigned to the product."""
  id: Int @deprecated(reason: "Use the `uid` field instead.")

  """The relative path to the main image on the product page."""
  image: ProductImage
  includes: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_cistern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_pan_connector: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_push_button: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_tray: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  includes_waste: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  inlet_connections: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  instructions: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  is_featured: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lever: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  lighting: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A number representing the product's manufacturer."""
  manufacturer: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  maximum_water_pressure: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of media gallery objects."""
  media_gallery: [MediaGalleryInterface]

  """An array of MediaGalleryEntry objects."""
  media_gallery_entries: [MediaGalleryEntry] @deprecated(reason: "Use `media_gallery` instead.")

  """
  A brief overview of the product for search results listings, maximum 255 characters.
  """
  meta_description: String

  """
  A comma-separated list of keywords that are visible only to search engines.
  """
  meta_keyword: String

  """
  A string that is displayed in the title bar and tab of the browser and in search results lists.
  """
  meta_title: String
  minimum_room_height: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_depth_mm: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mirror_features: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  model: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mounting: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  mp_exclude_sitemap: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  mpn: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The product name. Customers use this name to identify the product."""
  name: String
  new: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  The beginning date for new product listings, and determines if the product is featured as a new product.
  """
  new_from_date: String

  """The end date for new product listings."""
  new_to_date: String

  """Product stock only x left count"""
  only_x_left_in_stock: Float
  opening_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of options for a customizable product."""
  options: [CustomizableOptionInterface]

  """
  If the product has multiple options, determines where they appear on the product page.
  """
  options_container: String
  orientation: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_cap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  overflow_include: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pan_to_soil_pipe_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  passage_width: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  pattern: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  performance_fabric: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  plug_cap_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Indicates the price of an item."""
  price: ProductPrices @deprecated(reason: "Use `price_range` for product price information.")

  """The range of prices for the product"""
  price_range: PriceRange!

  """An array of `TierPrice` objects."""
  price_tiers: [TierPrice]
  product_label: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_background_color: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_image: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_select: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  product_label_shape: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of `ProductLinks` objects."""
  product_links: [ProductLinksInterface]
  quick_release_mechanism: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  range_name: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The average of all the ratings given to the product."""
  rating_summary: Float!

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """An array of products to be displayed in a Related Products block."""
  related_products: [ProductInterface]

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """The total count of all the reviews given to the product."""
  review_count: Int!

  """The list of products reviews."""
  reviews(
    """The maximum number of results to return at once. The default is 20."""
    pageSize: Int = 20

    """The page of results to return. The default is 1."""
    currentPage: Int = 1
  ): ProductReviews!
  rimless_bowl_design: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  running_track_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  sale: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  seat_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shape: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_finish: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shelf_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """A short description of the product. Its use depends on the theme."""
  short_description: ComplexTextValue
  shower_arm_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_arm_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_door: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  shower_flex_material: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  size: Int @deprecated(reason: "Use the `custom_attributes` field instead.")

  """
  A number or code assigned to a product to identify the product, options, price, and manufacturer.
  """
  sku: String
  sku_dis: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  sleeve: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the small image, which is used on catalog pages."""
  small_image: ProductImage

  """The beginning date that a product has a special price."""
  special_from_date: String @deprecated(reason: "The field should not be used on the storefront.")

  """The discounted price of the product."""
  special_price: Float
  special_price_from_date: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The end date for a product with a special price."""
  special_to_date: String
  spigot_length: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """Stock status of the product"""
  stock_status: ProductStockStatus
  strap_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bags: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_bottom: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  style_general: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The file name of a swatch image."""
  swatch_image: String
  tap: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole: Int @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_hole_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_tails_included: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  tap_type: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  thermostatic_control: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """The relative path to the product's thumbnail image."""
  thumbnail: ProductImage

  """
  The price when tier pricing is in effect and the items purchased threshold has been reached.
  """
  tier_price: Float @deprecated(reason: "Use `price_tiers` for product tier price information.")

  """An array of ProductTierPrices objects."""
  tier_prices: [ProductTierPrices] @deprecated(reason: "Use `price_tiers` for product tier price information.")
  toilet_seat_colour: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum

  """
  One of simple, virtual, bundle, downloadable, grouped, or configurable.
  """
  type_id: String @deprecated(reason: "Use `__typename` instead.")

  """The unique ID for a `ProductInterface` object."""
  uid: ID!

  """Timestamp indicating when the product was updated."""
  updated_at: String @deprecated(reason: "The field should not be used on the storefront.")

  """Upsell Products"""
  upsell_products: [ProductInterface]

  """The part of the URL that identifies the product"""
  url_key: String
  url_path: String @deprecated(reason: "Use product's `canonical_url` or url rewrites instead")

  """URL rewrites list"""
  url_rewrites: [UrlRewrite]

  """The part of the product URL that is appended after the url key"""
  url_suffix: String
  valve_handles: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of simple product variants."""
  variants: [ConfigurableVariant]
  waste_bottle: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_cover_tile: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_outlet_diameter: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  waste_plug: String @deprecated(reason: "Use the `custom_attributes` field instead.")
  water_pressure_requirement: String @deprecated(reason: "Use the `custom_attributes` field instead.")

  """An array of websites in which the product is available."""
  websites: [Website] @deprecated(reason: "The field should not be used on the storefront.")

  """The weight of the item, in units defined by the store."""
  weight: Float
}

"""Contains all the simple product variants of a configurable product."""
type ConfigurableVariant {
  """An array of configurable attribute options."""
  attributes: [ConfigurableAttributeOption]

  """An array of linked simple products."""
  product: SimpleProduct
}

"""Contains details about a configurable product attribute option."""
type ConfigurableAttributeOption {
  """The ID assigned to the attribute."""
  code: String

  """A string that describes the configurable attribute option."""
  label: String

  """The unique ID for a `ConfigurableAttributeOption` object."""
  uid: ID!

  """A unique index number assigned to the configurable product option."""
  value_index: Int
}

"""Defines configurable attributes for the specified product."""
type ConfigurableProductOptions {
  """A string that identifies the attribute."""
  attribute_code: String

  """The ID assigned to the attribute."""
  attribute_id: String @deprecated(reason: "Use `attribute_uid` instead.")

  """The ID assigned to the attribute."""
  attribute_id_v2: Int @deprecated(reason: "Use `attribute_uid` instead.")

  """The unique ID for an `Attribute` object."""
  attribute_uid: ID!

  """The configurable option ID number assigned by the system."""
  id: Int @deprecated(reason: "Use `uid` instead.")

  """A displayed string that describes the configurable product option."""
  label: String

  """A number that indicates the order in which the attribute is displayed."""
  position: Int

  """This is the same as a product's `id` field."""
  product_id: Int @deprecated(reason: "`product_id` is not needed and can be obtained from its parent.")

  """The unique ID for a `ConfigurableProductOptions` object."""
  uid: ID!

  """Indicates whether the option is the default."""
  use_default: Boolean

  """
  An array that defines the `value_index` codes assigned to the configurable product.
  """
  values: [ConfigurableProductOptionsValues]
}

"""Contains the index number assigned to a configurable product option."""
type ConfigurableProductOptionsValues {
  """The label of the product on the default store."""
  default_label: String

  """The label of the product."""
  label: String

  """The label of the product on the current store."""
  store_label: String

  """Swatch data for a configurable product option."""
  swatch_data: SwatchDataInterface

  """The unique ID for a `ConfigurableProductOptionsValues` object."""
  uid: ID

  """Indicates whether to use the default_label."""
  use_default_value: Boolean

  """A unique index number assigned to the configurable product option."""
  value_index: Int @deprecated(reason: "Use `uid` instead.")
}

"""Defines the configurable products to add to the cart."""
input AddConfigurableProductsToCartInput {
  """The ID of the cart."""
  cart_id: String!

  """An array of configurable products to add."""
  cart_items: [ConfigurableProductCartItemInput]!
}

"""Contains details about the cart after adding configurable products."""
type AddConfigurableProductsToCartOutput {
  """The cart after adding products."""
  cart: Cart!
}

""""""
input ConfigurableProductCartItemInput {
  """The ID and value of the option."""
  customizable_options: [CustomizableOptionInput]

  """The quantity and SKU of the configurable product."""
  data: CartItemInput!

  """The SKU of the parent configurable product."""
  parent_sku: String

  """Deprecated. Use `CartItemInput.sku` instead."""
  variant_sku: String
}

"""Contains details about a selected configurable option."""
type SelectedConfigurableOption {
  """The unique ID for a `ConfigurableProductOptions` object."""
  configurable_product_option_uid: ID!

  """The unique ID for a `ConfigurableProductOptionsValues` object."""
  configurable_product_option_value_uid: ID!
  id: Int! @deprecated(reason: "Use `SelectedConfigurableOption.configurable_product_option_uid` instead.")

  """The display text for the option."""
  option_label: String!
  value_id: Int! @deprecated(reason: "Use `SelectedConfigurableOption.configurable_product_option_value_uid` instead.")

  """The display name of the selected configurable option."""
  value_label: String!
}

"""A configurable product wish list item."""
type ConfigurableWishlistItem implements WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """
  The SKU of the simple product corresponding to a set of selected configurable options.
  """
  child_sku: String! @deprecated(reason: "Use `ConfigurableWishlistItem.configured_variant.sku` instead.")

  """An array of selected configurable options."""
  configurable_options: [SelectedConfigurableOption]

  """
  Product details of the selected variant. The value is null if some options are not configured.
  """
  configured_variant: ProductInterface

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""Contains metadata corresponding to the selected configurable options."""
type ConfigurableProductOptionsSelection {
  """An array of all possible configurable options."""
  configurable_options: [ConfigurableProductOption]

  """
  Product images and videos corresponding to the specified configurable options selection.
  """
  media_gallery: [MediaGalleryInterface]

  """
  The configurable options available for further selection based on the current selection.
  """
  options_available_for_selection: [ConfigurableOptionAvailableForSelection]

  """
  A variant represented by the specified configurable options selection. The value is expected to be null until selections are made for each configurable option.
  """
  variant: SimpleProduct
}

"""
Describes configurable options that have been selected and can be selected as a result of the previous selections.
"""
type ConfigurableOptionAvailableForSelection {
  """An attribute code that uniquely identifies a configurable option."""
  attribute_code: String!

  """An array of selectable option value IDs."""
  option_value_uids: [ID]!
}

"""Contains details about configurable product options."""
type ConfigurableProductOption {
  """An attribute code that uniquely identifies a configurable option."""
  attribute_code: String!

  """The display name of the option."""
  label: String!

  """The unique ID of the configurable option."""
  uid: ID!

  """An array of values that are applicable for this option."""
  values: [ConfigurableProductOptionValue]
}

"""Defines a value for a configurable product option."""
type ConfigurableProductOptionValue {
  """Indicates whether the product is available with this selected option."""
  is_available: Boolean!

  """Indicates whether the value is the default."""
  is_use_default: Boolean!

  """The display name of the value."""
  label: String!

  """The URL assigned to the thumbnail of the swatch image."""
  swatch: SwatchDataInterface

  """The unique ID of the value."""
  uid: ID!
}

"""
AreaInput defines the parameters which will be used for filter by specified location.
"""
input AreaInput {
  """The radius for the search in KM."""
  radius: Int!

  """
  The country code where search must be performed. Required parameter together with region, city or postcode.
  """
  search_term: String!
}

"""
PickupLocationFilterInput defines the list of attributes and filters for the search.
"""
input PickupLocationFilterInput {
  """Filter by city."""
  city: FilterTypeInput

  """Filter by country."""
  country_id: FilterTypeInput

  """Filter by pickup location name."""
  name: FilterTypeInput

  """Filter by pickup location code."""
  pickup_location_code: FilterTypeInput

  """Filter by postcode."""
  postcode: FilterTypeInput

  """Filter by region."""
  region: FilterTypeInput

  """Filter by region id."""
  region_id: FilterTypeInput

  """Filter by street."""
  street: FilterTypeInput
}

"""
PickupLocationSortInput specifies attribute to use for sorting search results and indicates whether the results are sorted in ascending or descending order.
"""
input PickupLocationSortInput {
  """City where pickup location is placed."""
  city: SortEnum

  """Name of the contact person."""
  contact_name: SortEnum

  """Id of the country in two letters."""
  country_id: SortEnum

  """Description of the pickup location."""
  description: SortEnum

  """
  Distance to the address, requested by distance filter. Applicable only with distance filter. If distance sort order is present, all other sort orders will be ignored.
  """
  distance: SortEnum

  """Contact email of the pickup location."""
  email: SortEnum

  """Contact fax of the pickup location."""
  fax: SortEnum

  """Geographic latitude where pickup location is placed."""
  latitude: SortEnum

  """Geographic longitude where pickup location is placed."""
  longitude: SortEnum

  """
  The pickup location name. Customer use this to identify the pickup location.
  """
  name: SortEnum

  """Contact phone number of the pickup location."""
  phone: SortEnum

  """A code assigned to pickup location to identify the source."""
  pickup_location_code: SortEnum

  """Postcode where pickup location is placed."""
  postcode: SortEnum

  """Name of the region."""
  region: SortEnum

  """Id of the region."""
  region_id: SortEnum

  """Street where pickup location is placed."""
  street: SortEnum
}

"""Top level object returned in a pickup locations search."""
type PickupLocations {
  """An array of pickup locations that match the specific search request."""
  items: [PickupLocation]

  """
  An object that includes the page_info and currentPage values specified in the query.
  """
  page_info: SearchResultPageInfo

  """The number of products returned."""
  total_count: Int
}

"""Defines Pickup Location information."""
type PickupLocation {
  city: String
  contact_name: String
  country_id: String
  description: String
  email: String
  fax: String
  latitude: Float
  longitude: Float
  name: String
  phone: String
  pickup_location_code: String
  postcode: String
  region: String
  region_id: Int
  street: String
}

"""Product Information used for Pickup Locations search."""
input ProductInfoInput {
  """Product SKU."""
  sku: String!
}

"""
This enumeration states whether a product stock status is in stock or out of stock
"""
enum ProductStockStatus {
  """"""
  IN_STOCK

  """"""
  OUT_OF_STOCK
}

"""Identifies which customer requires remote shopping assistance."""
input GenerateCustomerTokenAsAdminInput {
  """
  The email address of the customer requesting remote shopping assistance.
  """
  customer_email: String!
}

"""Contains the generated customer token."""
type GenerateCustomerTokenAsAdminOutput {
  """The generated customer token."""
  customer_token: String!
}

"""Defines the customer name, addresses, and other details."""
type Customer {
  """An array containing the customer's shipping and billing addresses."""
  addresses: [CustomerAddress]

  """Indicates whether the customer has enabled remote shopping assistance."""
  allow_remote_shopping_assistance: Boolean!

  """The contents of the customer's compare list."""
  compare_list: CompareList

  """The customer's confirmation status."""
  confirmation_status: ConfirmationStatusEnum!

  """Timestamp indicating when the account was created."""
  created_at: String

  """Customer's custom attributes."""
  custom_attributes(attributeCodes: [ID!]): [AttributeValueInterface]

  """The customer's date of birth."""
  date_of_birth: String

  """The ID assigned to the billing address."""
  default_billing: String

  """The ID assigned to the shipping address."""
  default_shipping: String

  """The customer's date of birth."""
  dob: String @deprecated(reason: "Use `date_of_birth` instead.")

  """The customer's email address. Required."""
  email: String

  """The customer's first name."""
  firstname: String

  """The customer's gender (Male - 1, Female - 2)."""
  gender: Int
  group_id: Int @deprecated(reason: "Customer group should not be exposed in the storefront scenarios.")

  """The ID assigned to the customer."""
  id: Int @deprecated(reason: "`id` is not needed as part of `Customer`, because on the server side, it can be identified based on the customer token used for authentication. There is no need to know customer ID on the client side.")

  """
  Indicates whether the customer is subscribed to the company's newsletter.
  """
  is_subscribed: Boolean

  """The customer's family name."""
  lastname: String

  """The customer's middle name."""
  middlename: String
  orders(
    """Defines the filter to use for searching customer orders."""
    filter: CustomerOrdersFilterInput

    """Specifies which page of results to return. The default value is 1."""
    currentPage: Int = 1

    """
    Specifies the maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """
    Specifies which field to sort on, and whether to return the results in ascending or descending order.
    """
    sort: CustomerOrderSortInput

    """
    Specifies the scope to search for customer orders. The Store request header identifies the customer's store view code. The default value of STORE limits the search to the value specified in the header. Specify WEBSITE to expand the search to include all customer orders assigned to the website that is defined in the header, or specify GLOBAL to include all customer orders across all websites and stores.
    """
    scope: ScopeTypeEnum
  ): CustomerOrders

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """Contains the customer's product reviews."""
  reviews(
    """
    The maximum number of results to return at once. The default value is 20.
    """
    pageSize: Int = 20

    """The page of results to return. The default value is 1."""
    currentPage: Int = 1
  ): ProductReviews!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's Value-added tax (VAT) number (for corporate customers)."""
  taxvat: String

  """Return a customer's wish lists."""
  wishlist: Wishlist! @deprecated(reason: "Use `Customer.wishlists` or `Customer.wishlist_v2` instead.")

  """
  Retrieve the wish list identified by the unique ID for a `Wishlist` object.
  """
  wishlist_v2(id: ID!): Wishlist

  """
  An array of wishlists. In Magento Open Source, customers are limited to one wish list. The number of wish lists is configurable for Adobe Commerce.
  """
  wishlists(
    """
    Specifies the maximum number of results to return at once. This attribute is optional.
    """
    pageSize: Int = 20

    """Specifies which page of results to return. The default value is 1."""
    currentPage: Int = 1
  ): [Wishlist]!
}

"""An input object for creating a customer."""
input CustomerCreateInput {
  """Indicates whether the customer has enabled remote shopping assistance."""
  allow_remote_shopping_assistance: Boolean

  """The customer's custom attributes."""
  custom_attributes: [AttributeValueInput]

  """The customer's date of birth."""
  date_of_birth: String

  """Deprecated: Use `date_of_birth` instead."""
  dob: String

  """The customer's email address."""
  email: String!

  """The customer's first name."""
  firstname: String!

  """The customer's gender (Male - 1, Female - 2)."""
  gender: Int

  """
  Indicates whether the customer is subscribed to the company's newsletter.
  """
  is_subscribed: Boolean

  """The customer's family name."""
  lastname: String!

  """The customer's middle name."""
  middlename: String

  """The customer's password."""
  password: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's Tax/VAT number (for corporate customers)."""
  taxvat: String
}

"""An input object for updating a customer."""
input CustomerUpdateInput {
  """Indicates whether the customer has enabled remote shopping assistance."""
  allow_remote_shopping_assistance: Boolean

  """The customer's custom attributes."""
  custom_attributes: [AttributeValueInput]

  """The customer's date of birth."""
  date_of_birth: String

  """Deprecated: Use `date_of_birth` instead."""
  dob: String

  """The customer's first name."""
  firstname: String

  """The customer's gender (Male - 1, Female - 2)."""
  gender: Int

  """
  Indicates whether the customer is subscribed to the company's newsletter.
  """
  is_subscribed: Boolean

  """The customer's family name."""
  lastname: String

  """The customer's middle name."""
  middlename: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's Tax/VAT number (for corporate customers)."""
  taxvat: String
}

"""Contains details about a customer email address to confirm."""
input ConfirmEmailInput {
  """The key to confirm the email address."""
  confirmation_key: String!

  """The email address to be confirmed."""
  email: String!
}

"""Contains details about a billing or shipping address."""
input CustomerAddressInput {
  """The customer's city or town."""
  city: String

  """The customer's company."""
  company: String

  """The two-letter code representing the customer's country."""
  country_code: CountryCodeEnum

  """Deprecated: use `country_code` instead."""
  country_id: CountryCodeEnum

  """Deprecated. Use custom_attributesV2 instead."""
  custom_attributes: [CustomerAddressAttributeInput]

  """Custom attributes assigned to the customer address."""
  custom_attributesV2: [AttributeValueInput]

  """Indicates whether the address is the default billing address."""
  default_billing: Boolean

  """Indicates whether the address is the default shipping address."""
  default_shipping: Boolean

  """The customer's fax number."""
  fax: String

  """
  The first name of the person associated with the billing/shipping address.
  """
  firstname: String

  """
  The family name of the person associated with the billing/shipping address.
  """
  lastname: String

  """
  The middle name of the person associated with the billing/shipping address.
  """
  middlename: String

  """The customer's ZIP or postal code."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """An object containing the region name, region code, and region ID."""
  region: CustomerAddressRegionInput

  """An array of strings that define the street number and name."""
  street: [String]

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's telephone number."""
  telephone: String

  """The customer's Tax/VAT number (for corporate customers)."""
  vat_id: String
}

"""Defines the customer's state or province."""
input CustomerAddressRegionInput {
  """The state or province name."""
  region: String

  """The address region code."""
  region_code: String

  """The unique ID for a pre-defined region."""
  region_id: Int
}

"""Specifies the attribute code and value of a customer attribute."""
input CustomerAddressAttributeInput {
  """The name assigned to the attribute."""
  attribute_code: String!

  """The value assigned to the attribute."""
  value: String!
}

"""Contains a customer authorization token."""
type CustomerToken {
  """The customer authorization token."""
  token: String
}

"""An input object that assigns or updates customer attributes."""
input CustomerInput {
  """The customer's date of birth."""
  date_of_birth: String

  """Deprecated: Use `date_of_birth` instead."""
  dob: String

  """The customer's email address. Required when creating a customer."""
  email: String

  """The customer's first name."""
  firstname: String

  """The customer's gender (Male - 1, Female - 2)."""
  gender: Int

  """
  Indicates whether the customer is subscribed to the company's newsletter.
  """
  is_subscribed: Boolean

  """The customer's family name."""
  lastname: String

  """The customer's middle name."""
  middlename: String

  """The customer's password."""
  password: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's Tax/VAT number (for corporate customers)."""
  taxvat: String
}

"""Contains details about a newly-created or updated customer."""
type CustomerOutput {
  """Customer details after creating or updating a customer."""
  customer: Customer!
}

"""Contains the result of a request to revoke a customer token."""
type RevokeCustomerTokenOutput {
  """The result of a request to revoke a customer token."""
  result: Boolean!
}

"""
Contains detailed information about a customer's billing or shipping address.
"""
type CustomerAddress {
  """The customer's city or town."""
  city: String

  """The customer's company."""
  company: String

  """The customer's country."""
  country_code: CountryCodeEnum

  """The customer's country."""
  country_id: String @deprecated(reason: "Use `country_code` instead.")
  custom_attributes: [CustomerAddressAttribute] @deprecated(reason: "Use custom_attributesV2 instead.")

  """Custom attributes assigned to the customer address."""
  custom_attributesV2(attributeCodes: [ID!]): [AttributeValueInterface]!

  """The customer ID"""
  customer_id: Int @deprecated(reason: "`customer_id` is not needed as part of `CustomerAddress`. The `id` is a unique identifier for the addresses.")

  """
  Indicates whether the address is the customer's default billing address.
  """
  default_billing: Boolean

  """
  Indicates whether the address is the customer's default shipping address.
  """
  default_shipping: Boolean

  """Contains any extension attributes for the address."""
  extension_attributes: [CustomerAddressAttribute]

  """The customer's fax number."""
  fax: String

  """
  The first name of the person associated with the shipping/billing address.
  """
  firstname: String

  """The ID of a `CustomerAddress` object."""
  id: Int

  """
  The family name of the person associated with the shipping/billing address.
  """
  lastname: String

  """
  The middle name of the person associated with the shipping/billing address.
  """
  middlename: String

  """The customer's ZIP or postal code."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """An object containing the region name, region code, and region ID."""
  region: CustomerAddressRegion

  """The unique ID for a pre-defined region."""
  region_id: Int

  """An array of strings that define the street number and name."""
  street: [String]

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The customer's telephone number."""
  telephone: String

  """The customer's Value-added tax (VAT) number (for corporate customers)."""
  vat_id: String
}

"""Defines the customer's state or province."""
type CustomerAddressRegion {
  """The state or province name."""
  region: String

  """The address region code."""
  region_code: String

  """The unique ID for a pre-defined region."""
  region_id: Int
}

"""
Specifies the attribute code and value of a customer address attribute.
"""
type CustomerAddressAttribute {
  """The name assigned to the customer address attribute."""
  attribute_code: String

  """The value assigned to the customer address attribute."""
  value: String
}

"""Contains the result of the `isEmailAvailable` query."""
type IsEmailAvailableOutput {
  """
  Indicates whether the specified email address can be used to create a customer.
  """
  is_email_available: Boolean
}

"""The list of country codes."""
enum CountryCodeEnum {
  """Afghanistan"""
  AF

  """Åland Islands"""
  AX

  """Albania"""
  AL

  """Algeria"""
  DZ

  """American Samoa"""
  AS

  """Andorra"""
  AD

  """Angola"""
  AO

  """Anguilla"""
  AI

  """Antarctica"""
  AQ

  """Antigua & Barbuda"""
  AG

  """Argentina"""
  AR

  """Armenia"""
  AM

  """Aruba"""
  AW

  """Australia"""
  AU

  """Austria"""
  AT

  """Azerbaijan"""
  AZ

  """Bahamas"""
  BS

  """Bahrain"""
  BH

  """Bangladesh"""
  BD

  """Barbados"""
  BB

  """Belarus"""
  BY

  """Belgium"""
  BE

  """Belize"""
  BZ

  """Benin"""
  BJ

  """Bermuda"""
  BM

  """Bhutan"""
  BT

  """Bolivia"""
  BO

  """Bosnia & Herzegovina"""
  BA

  """Botswana"""
  BW

  """Bouvet Island"""
  BV

  """Brazil"""
  BR

  """British Indian Ocean Territory"""
  IO

  """British Virgin Islands"""
  VG

  """Brunei"""
  BN

  """Bulgaria"""
  BG

  """Burkina Faso"""
  BF

  """Burundi"""
  BI

  """Cambodia"""
  KH

  """Cameroon"""
  CM

  """Canada"""
  CA

  """Cape Verde"""
  CV

  """Cayman Islands"""
  KY

  """Central African Republic"""
  CF

  """Chad"""
  TD

  """Chile"""
  CL

  """China"""
  CN

  """Christmas Island"""
  CX

  """Cocos (Keeling) Islands"""
  CC

  """Colombia"""
  CO

  """Comoros"""
  KM

  """Congo-Brazzaville"""
  CG

  """Congo-Kinshasa"""
  CD

  """Cook Islands"""
  CK

  """Costa Rica"""
  CR

  """Côte d’Ivoire"""
  CI

  """Croatia"""
  HR

  """Cuba"""
  CU

  """Cyprus"""
  CY

  """Czech Republic"""
  CZ

  """Denmark"""
  DK

  """Djibouti"""
  DJ

  """Dominica"""
  DM

  """Dominican Republic"""
  DO

  """Ecuador"""
  EC

  """Egypt"""
  EG

  """El Salvador"""
  SV

  """Equatorial Guinea"""
  GQ

  """Eritrea"""
  ER

  """Estonia"""
  EE

  """Eswatini"""
  SZ

  """Ethiopia"""
  ET

  """Falkland Islands"""
  FK

  """Faroe Islands"""
  FO

  """Fiji"""
  FJ

  """Finland"""
  FI

  """France"""
  FR

  """French Guiana"""
  GF

  """French Polynesia"""
  PF

  """French Southern Territories"""
  TF

  """Gabon"""
  GA

  """Gambia"""
  GM

  """Georgia"""
  GE

  """Germany"""
  DE

  """Ghana"""
  GH

  """Gibraltar"""
  GI

  """Greece"""
  GR

  """Greenland"""
  GL

  """Grenada"""
  GD

  """Guadeloupe"""
  GP

  """Guam"""
  GU

  """Guatemala"""
  GT

  """Guernsey"""
  GG

  """Guinea"""
  GN

  """Guinea-Bissau"""
  GW

  """Guyana"""
  GY

  """Haiti"""
  HT

  """Heard &amp; McDonald Islands"""
  HM

  """Honduras"""
  HN

  """Hong Kong SAR China"""
  HK

  """Hungary"""
  HU

  """Iceland"""
  IS

  """India"""
  IN

  """Indonesia"""
  ID

  """Iran"""
  IR

  """Iraq"""
  IQ

  """Ireland"""
  IE

  """Isle of Man"""
  IM

  """Israel"""
  IL

  """Italy"""
  IT

  """Jamaica"""
  JM

  """Japan"""
  JP

  """Jersey"""
  JE

  """Jordan"""
  JO

  """Kazakhstan"""
  KZ

  """Kenya"""
  KE

  """Kiribati"""
  KI

  """Kuwait"""
  KW

  """Kyrgyzstan"""
  KG

  """Laos"""
  LA

  """Latvia"""
  LV

  """Lebanon"""
  LB

  """Lesotho"""
  LS

  """Liberia"""
  LR

  """Libya"""
  LY

  """Liechtenstein"""
  LI

  """Lithuania"""
  LT

  """Luxembourg"""
  LU

  """Macau SAR China"""
  MO

  """Macedonia"""
  MK

  """Madagascar"""
  MG

  """Malawi"""
  MW

  """Malaysia"""
  MY

  """Maldives"""
  MV

  """Mali"""
  ML

  """Malta"""
  MT

  """Marshall Islands"""
  MH

  """Martinique"""
  MQ

  """Mauritania"""
  MR

  """Mauritius"""
  MU

  """Mayotte"""
  YT

  """Mexico"""
  MX

  """Micronesia"""
  FM

  """Moldova"""
  MD

  """Monaco"""
  MC

  """Mongolia"""
  MN

  """Montenegro"""
  ME

  """Montserrat"""
  MS

  """Morocco"""
  MA

  """Mozambique"""
  MZ

  """Myanmar (Burma)"""
  MM

  """Namibia"""
  NA

  """Nauru"""
  NR

  """Nepal"""
  NP

  """Netherlands"""
  NL

  """Netherlands Antilles"""
  AN

  """New Caledonia"""
  NC

  """New Zealand"""
  NZ

  """Nicaragua"""
  NI

  """Niger"""
  NE

  """Nigeria"""
  NG

  """Niue"""
  NU

  """Norfolk Island"""
  NF

  """Northern Mariana Islands"""
  MP

  """North Korea"""
  KP

  """Norway"""
  NO

  """Oman"""
  OM

  """Pakistan"""
  PK

  """Palau"""
  PW

  """Palestinian Territories"""
  PS

  """Panama"""
  PA

  """Papua New Guinea"""
  PG

  """Paraguay"""
  PY

  """Peru"""
  PE

  """Philippines"""
  PH

  """Pitcairn Islands"""
  PN

  """Poland"""
  PL

  """Portugal"""
  PT

  """Qatar"""
  QA

  """Réunion"""
  RE

  """Romania"""
  RO

  """Russia"""
  RU

  """Rwanda"""
  RW

  """Samoa"""
  WS

  """San Marino"""
  SM

  """São Tomé & Príncipe"""
  ST

  """Saudi Arabia"""
  SA

  """Senegal"""
  SN

  """Serbia"""
  RS

  """Seychelles"""
  SC

  """Sierra Leone"""
  SL

  """Singapore"""
  SG

  """Slovakia"""
  SK

  """Slovenia"""
  SI

  """Solomon Islands"""
  SB

  """Somalia"""
  SO

  """South Africa"""
  ZA

  """South Georgia & South Sandwich Islands"""
  GS

  """South Korea"""
  KR

  """Spain"""
  ES

  """Sri Lanka"""
  LK

  """St. Barthélemy"""
  BL

  """St. Helena"""
  SH

  """St. Kitts & Nevis"""
  KN

  """St. Lucia"""
  LC

  """St. Martin"""
  MF

  """St. Pierre & Miquelon"""
  PM

  """St. Vincent & Grenadines"""
  VC

  """Sudan"""
  SD

  """Suriname"""
  SR

  """Svalbard & Jan Mayen"""
  SJ

  """Sweden"""
  SE

  """Switzerland"""
  CH

  """Syria"""
  SY

  """Taiwan"""
  TW

  """Tajikistan"""
  TJ

  """Tanzania"""
  TZ

  """Thailand"""
  TH

  """Timor-Leste"""
  TL

  """Togo"""
  TG

  """Tokelau"""
  TK

  """Tonga"""
  TO

  """Trinidad & Tobago"""
  TT

  """Tunisia"""
  TN

  """Turkey"""
  TR

  """Turkmenistan"""
  TM

  """Turks & Caicos Islands"""
  TC

  """Tuvalu"""
  TV

  """Uganda"""
  UG

  """Ukraine"""
  UA

  """United Arab Emirates"""
  AE

  """United Kingdom"""
  GB

  """United States"""
  US

  """Uruguay"""
  UY

  """U.S. Outlying Islands"""
  UM

  """U.S. Virgin Islands"""
  VI

  """Uzbekistan"""
  UZ

  """Vanuatu"""
  VU

  """Vatican City"""
  VA

  """Venezuela"""
  VE

  """Vietnam"""
  VN

  """Wallis & Futuna"""
  WF

  """Western Sahara"""
  EH

  """Yemen"""
  YE

  """Zambia"""
  ZM

  """Zimbabwe"""
  ZW
}

"""Customer attribute metadata."""
type CustomerAttributeMetadata implements CustomAttributeMetadataInterface {
  """
  The unique identifier for an attribute code. This value should be in lowercase letters without spaces.
  """
  code: ID!

  """Default attribute value."""
  default_value: String

  """The type of entity that defines the attribute."""
  entity_type: AttributeEntityTypeEnum!

  """The frontend class of the attribute."""
  frontend_class: String

  """The frontend input type of the attribute."""
  frontend_input: AttributeFrontendInputEnum

  """The template used for the input of the attribute (e.g., 'date')."""
  input_filter: InputFilterEnum

  """Whether the attribute value is required."""
  is_required: Boolean!

  """Whether the attribute value must be unique."""
  is_unique: Boolean!

  """The label assigned to the attribute."""
  label: String

  """The number of lines of the attribute value."""
  multiline_count: Int

  """Attribute options."""
  options: [CustomAttributeOptionInterface]!

  """The position of the attribute in the form."""
  sort_order: Int

  """The validation rules of the attribute value."""
  validate_rules: [ValidationRule]
}

"""List of templates/filters applied to customer attribute input."""
enum InputFilterEnum {
  """There are no templates or filters to be applied."""
  NONE

  """Forces attribute input to follow the date format."""
  DATE

  """
  Strip whitespace (or other characters) from the beginning and end of the input.
  """
  TRIM

  """Strip HTML Tags."""
  STRIPTAGS

  """Escape HTML Entities."""
  ESCAPEHTML
}

"""Defines a customer attribute validation rule."""
type ValidationRule {
  """Validation rule name applied to a customer attribute."""
  name: ValidationRuleEnum

  """Validation rule value."""
  value: String
}

"""List of validation rule names applied to a customer attribute."""
enum ValidationRuleEnum {
  """"""
  DATE_RANGE_MAX

  """"""
  DATE_RANGE_MIN

  """"""
  FILE_EXTENSIONS

  """"""
  INPUT_VALIDATION

  """"""
  MAX_TEXT_LENGTH

  """"""
  MIN_TEXT_LENGTH

  """"""
  MAX_FILE_SIZE

  """"""
  MAX_IMAGE_HEIGHT

  """"""
  MAX_IMAGE_WIDTH
}

"""List of account confirmation statuses."""
enum ConfirmationStatusEnum {
  """Account confirmed"""
  ACCOUNT_CONFIRMED

  """Account confirmation not required"""
  ACCOUNT_CONFIRMATION_NOT_REQUIRED
}

"""Contains the result of the `subscribeEmailToNewsletter` operation."""
type SubscribeEmailToNewsletterOutput {
  """The status of the subscription request."""
  status: SubscriptionStatusesEnum
}

"""Indicates the status of the request."""
enum SubscriptionStatusesEnum {
  """"""
  NOT_ACTIVE

  """"""
  SUBSCRIBED

  """"""
  UNSUBSCRIBED

  """"""
  UNCONFIRMED
}

""""""
type CancellationReason {
  description: String!
}

"""Defines the order to cancel."""
input CancelOrderInput {
  """Order ID."""
  order_id: ID!

  """Cancellation reason."""
  reason: String!
}

"""Contains the updated customer order and error message if any."""
type CancelOrderOutput {
  """Error encountered while cancelling the order."""
  error: String

  """Updated customer order."""
  order: CustomerOrder
}

"""Gets the payment SDK URLs and values"""
type GetPaymentSDKOutput {
  """The payment SDK parameters"""
  sdkParams: [PaymentSDKParamsItem]
}

""""""
type PaymentSDKParamsItem {
  """The payment method code used in the order"""
  code: String

  """The payment SDK parameters"""
  params: [SDKParams]
}

"""Contains the payment order details"""
type PaymentOrderOutput {
  """PayPal order ID"""
  id: String

  """The order ID generated by Payment Services"""
  mp_order_id: String

  """Details about the card used on the order"""
  payment_source_details: PaymentSourceDetails

  """The status of the payment order"""
  status: String
}

""""""
type PaymentSourceDetails {
  """Details about the card used on the order"""
  card: Card
}

""""""
type Card {
  """Card bin details"""
  bin_details: CardBin

  """Expiration month of the card"""
  card_expiry_month: String

  """Expiration year of the card"""
  card_expiry_year: String

  """Last four digits of the card"""
  last_digits: String

  """Name on the card"""
  name: String
}

""""""
type CardBin {
  """Card bin number"""
  bin: String
}

"""
Contains payment order details that are used while processing the payment order
"""
input CreatePaymentOrderInput {
  """The customer cart ID"""
  cartId: String!

  """Defines the origin location for that payment request"""
  location: PaymentLocation!

  """The code for the payment method used in the order"""
  methodCode: String!

  """The identifiable payment source for the payment method"""
  paymentSource: String!

  """Indicates whether the payment information should be vaulted"""
  vaultIntent: Boolean
}

"""Synchronizes the payment order details"""
input SyncPaymentOrderInput {
  """The customer cart ID"""
  cartId: String!

  """PayPal order ID"""
  id: String!
}

"""
Contains payment order details that are used while processing the payment order
"""
type CreatePaymentOrderOutput {
  """The amount of the payment order"""
  amount: Float

  """The currency of the payment order"""
  currency_code: String

  """PayPal order ID"""
  id: String

  """The order ID generated by Payment Services"""
  mp_order_id: String

  """The status of the payment order"""
  status: String
}

"""Defines the origin location for that payment request"""
enum PaymentLocation {
  """"""
  PRODUCT_DETAIL

  """"""
  MINICART

  """"""
  CART

  """"""
  CHECKOUT

  """"""
  ADMIN
}

"""Retrieves the payment configuration for a given location"""
type PaymentConfigOutput {
  """ApplePay payment method configuration"""
  apple_pay: ApplePayConfig

  """GooglePay payment method configuration"""
  google_pay: GooglePayConfig

  """Hosted fields payment method configuration"""
  hosted_fields: HostedFieldsConfig

  """Smart Buttons payment method configuration"""
  smart_buttons: SmartButtonsConfig
}

"""
Contains payment fields that are common to all types of payment methods.
"""
interface PaymentConfigItem {
  """The payment method code as defined in the payment gateway"""
  code: String

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """The name displayed for the payment method"""
  title: String
}

""""""
type PaymentCommonConfig implements PaymentConfigItem {
  """The payment method code as defined in the payment gateway"""
  code: String

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """The name displayed for the payment method"""
  title: String
}

""""""
type HostedFieldsConfig implements PaymentConfigItem {
  """Vault payment method code"""
  cc_vault_code: String

  """The payment method code as defined in the payment gateway"""
  code: String

  """Card vault enabled"""
  is_vault_enabled: Boolean

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The payment source for the payment method"""
  payment_source: String

  """Card and bin details required"""
  requires_card_details: Boolean

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """Whether 3DS is activated; true if 3DS mode is not OFF."""
  three_ds: Boolean @deprecated(reason: "Use 'three_ds_mode' instead.")

  """3DS mode"""
  three_ds_mode: ThreeDSMode

  """The name displayed for the payment method"""
  title: String
}

"""3D Secure mode."""
enum ThreeDSMode {
  """"""
  OFF

  """"""
  SCA_WHEN_REQUIRED

  """"""
  SCA_ALWAYS
}

""""""
type SmartButtonsConfig implements PaymentConfigItem {
  """The styles for the PayPal Smart Button configuration"""
  button_styles: ButtonStyles

  """The payment method code as defined in the payment gateway"""
  code: String

  """Indicates whether to display the PayPal Pay Later message"""
  display_message: Boolean

  """Indicates whether to display Venmo"""
  display_venmo: Boolean

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Contains details about the styles for the PayPal Pay Later message"""
  message_styles: MessageStyles

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """The name displayed for the payment method"""
  title: String
}

""""""
type ApplePayConfig implements PaymentConfigItem {
  """The styles for the ApplePay Smart Button configuration"""
  button_styles: ButtonStyles

  """The payment method code as defined in the payment gateway"""
  code: String

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The payment source for the payment method"""
  payment_source: String

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """The name displayed for the payment method"""
  title: String
}

""""""
type GooglePayConfig implements PaymentConfigItem {
  """The styles for the GooglePay Button configuration"""
  button_styles: GooglePayButtonStyles

  """The payment method code as defined in the payment gateway"""
  code: String

  """Indicates whether the payment method is displayed"""
  is_visible: Boolean

  """Defines the payment intent (Authorize or Capture"""
  payment_intent: String

  """The payment source for the payment method"""
  payment_source: String

  """The PayPal parameters required to load the JS SDK"""
  sdk_params: [SDKParams]

  """
  The relative order the payment method is displayed on the checkout page
  """
  sort_order: String

  """3DS mode"""
  three_ds_mode: ThreeDSMode

  """The name displayed for the payment method"""
  title: String
}

""""""
type ButtonStyles {
  """The button color"""
  color: String

  """The button height in pixels"""
  height: Int

  """The button label"""
  label: String

  """The button layout"""
  layout: String

  """The button shape"""
  shape: String

  """Indicates whether the tagline is displayed"""
  tagline: Boolean

  """
  Defines if the button uses default height. If the value is false, the value of height is used
  """
  use_default_height: Boolean
}

""""""
type GooglePayButtonStyles {
  """The button color"""
  color: String

  """The button height in pixels"""
  height: Int

  """The button type"""
  type: String
}

""""""
type MessageStyles {
  """The message layout"""
  layout: String

  """The message logo"""
  logo: MessageStyleLogo
}

""""""
type MessageStyleLogo {
  """The type of logo for the PayPal Pay Later messaging"""
  type: String
}

"""Defines the name and value of a SDK parameter"""
type SDKParams {
  """The name of the SDK parameter"""
  name: String

  """The value of the SDK parameter"""
  value: String
}

"""Vault payment inputs"""
input VaultMethodInput {
  """The payment source for the payment method"""
  payment_source: String

  """The payment services order ID"""
  payments_order_id: String

  """PayPal order ID"""
  paypal_order_id: String

  """The public hash of the token."""
  public_hash: String
}

"""Smart button payment inputs"""
input SmartButtonMethodInput {
  """The payment source for the payment method"""
  payment_source: String

  """The payment services order ID"""
  payments_order_id: String

  """PayPal order ID"""
  paypal_order_id: String
}

"""Apple Pay inputs"""
input ApplePayMethodInput {
  """The payment source for the payment method"""
  payment_source: String

  """The payment services order ID"""
  payments_order_id: String

  """PayPal order ID"""
  paypal_order_id: String
}

"""Google Pay inputs"""
input GooglePayMethodInput {
  """The payment source for the payment method"""
  payment_source: String

  """The payment services order ID"""
  payments_order_id: String

  """PayPal order ID"""
  paypal_order_id: String
}

"""Hosted Fields payment inputs"""
input HostedFieldsInput {
  """Card bin number"""
  cardBin: String

  """Expiration month of the card"""
  cardExpiryMonth: String

  """Expiration year of the card"""
  cardExpiryYear: String

  """Last four digits of the card"""
  cardLast4: String

  """Name on the card"""
  holderName: String

  """
  Indicates whether details about the shopper's credit/debit card should be tokenized for later usage. Required only if Vault is enabled for the Payment Services payment integration.
  """
  is_active_payment_token_enabler: Boolean

  """The payment source for the payment method"""
  payment_source: String

  """The payment services order ID"""
  payments_order_id: String

  """PayPal order ID"""
  paypal_order_id: String
}

"""Describe the variables needed to create a vault card setup token"""
input CreateVaultCardSetupTokenInput {
  """The setup token information"""
  setup_token: VaultSetupTokenInput!

  """The 3DS mode"""
  three_ds_mode: ThreeDSMode
}

"""The payment source information"""
input VaultSetupTokenInput {
  """The payment source information"""
  payment_source: PaymentSourceInput!
}

"""The payment source information"""
input PaymentSourceInput {
  """The card payment source information"""
  card: CardPaymentSourceInput!
}

"""The card payment source information"""
input CardPaymentSourceInput {
  """The billing address of the card"""
  billing_address: BillingAddressPaymentSourceInput!

  """The name on the cardholder"""
  name: String
}

"""The billing address information"""
input BillingAddressPaymentSourceInput {
  """The first line of the address"""
  address_line_1: String

  """The second line of the address"""
  address_line_2: String

  """The city of the address"""
  city: String

  """The country of the address"""
  country_code: String!

  """The postal code of the address"""
  postal_code: String

  """The region of the address"""
  region: String
}

"""The setup token id information"""
type CreateVaultCardSetupTokenOutput {
  """The setup token id"""
  setup_token: String!
}

"""Describe the variables needed to create a vault payment token"""
input CreateVaultCardPaymentTokenInput {
  """Description of the vaulted card"""
  card_description: String

  """The setup token obtained by the createVaultCardSetupToken endpoint"""
  setup_token_id: String!
}

"""The vault token id and information about the payment source"""
type CreateVaultCardPaymentTokenOutput {
  """The payment source information"""
  payment_source: PaymentSourceOutput!

  """The vault payment token information"""
  vault_token_id: String!
}

"""The payment source information"""
type PaymentSourceOutput {
  """The card payment source information"""
  card: CardPaymentSourceOutput!
}

"""The card payment source information"""
type CardPaymentSourceOutput {
  """The brand of the card"""
  brand: String

  """The expiry of the card"""
  expiry: String

  """The last digits of the card"""
  last_digits: String
}

"""Retrieves the vault configuration"""
type VaultConfigOutput {
  """Credit card vault method configuration"""
  credit_card: VaultCreditCardConfig
}

""""""
type VaultCreditCardConfig {
  """Is vault enabled"""
  is_vault_enabled: Boolean

  """The parameters required to load the Paypal JS SDK"""
  sdk_params: [SDKParams]

  """3DS mode"""
  three_ds_mode: ThreeDSMode
}

""""""
enum ReCaptchaFormEnum {
  """"""
  PLACE_ORDER

  """"""
  CONTACT

  """"""
  CUSTOMER_LOGIN

  """"""
  CUSTOMER_FORGOT_PASSWORD

  """"""
  CUSTOMER_CREATE

  """"""
  CUSTOMER_EDIT

  """"""
  NEWSLETTER

  """"""
  PRODUCT_REVIEW

  """"""
  SENDFRIEND

  """"""
  BRAINTREE
}

"""Contains reCAPTCHA V3-Invisible configuration details."""
type ReCaptchaConfigurationV3 {
  """The position of the invisible reCAPTCHA badge on each page."""
  badge_position: String!

  """The message that appears to the user if validation fails."""
  failure_message: String!

  """
  A list of forms on the storefront that have been configured to use reCAPTCHA V3.
  """
  forms: [ReCaptchaFormEnum]!

  """Return whether recaptcha is enabled or not"""
  is_enabled: Boolean!

  """
  A two-character code that specifies the language that is used for Google reCAPTCHA text and messaging.
  """
  language_code: String

  """
  The minimum score that identifies a user interaction as a potential risk.
  """
  minimum_score: Float!

  """
  The website key generated when the Google reCAPTCHA account was registered.
  """
  website_key: String!
}

"""Contains an array of product reviews."""
type ProductReviews {
  """An array of product reviews."""
  items: [ProductReview]!

  """Metadata for pagination rendering."""
  page_info: SearchResultPageInfo!
}

"""Contains details of a product review."""
type ProductReview {
  """The average of all ratings for this product."""
  average_rating: Float!

  """The date the review was created."""
  created_at: String!

  """The customer's nickname. Defaults to the customer name, if logged in."""
  nickname: String!

  """The reviewed product."""
  product: ProductInterface!

  """
  An array of ratings by rating category, such as quality, price, and value.
  """
  ratings_breakdown: [ProductReviewRating]!

  """The summary (title) of the review."""
  summary: String!

  """The review text."""
  text: String!
}

"""Contains data about a single aspect of a product review."""
type ProductReviewRating {
  """
  The label assigned to an aspect of a product that is being rated, such as quality or price.
  """
  name: String!

  """
  The rating value given by customer. By default, possible values range from 1 to 5.
  """
  value: String!
}

"""Contains an array of metadata about each aspect of a product review."""
type ProductReviewRatingsMetadata {
  """An array of product reviews sorted by position."""
  items: [ProductReviewRatingMetadata]!
}

"""Contains details about a single aspect of a product review."""
type ProductReviewRatingMetadata {
  """An encoded rating ID."""
  id: String!

  """
  The label assigned to an aspect of a product that is being rated, such as quality or price.
  """
  name: String!

  """List of product review ratings sorted by position."""
  values: [ProductReviewRatingValueMetadata]!
}

"""Contains details about a single value in a product review."""
type ProductReviewRatingValueMetadata {
  """A ratings scale, such as the number of stars awarded."""
  value: String!

  """An encoded rating value ID."""
  value_id: String!
}

"""Contains the completed product review."""
type CreateProductReviewOutput {
  """Product review details."""
  review: ProductReview!
}

"""Defines a new product review."""
input CreateProductReviewInput {
  """The customer's nickname. Defaults to the customer name, if logged in."""
  nickname: String!

  """
  The ratings details by category. For example, Price: 5 stars, Quality: 4 stars, etc.
  """
  ratings: [ProductReviewRatingInput]!

  """The SKU of the reviewed product."""
  sku: String!

  """The summary (title) of the review."""
  summary: String!

  """The review text."""
  text: String!
}

"""Contains the reviewer's rating for a single aspect of a review."""
input ProductReviewRatingInput {
  """An encoded rating ID."""
  id: String!

  """An encoded rating value ID."""
  value_id: String!
}

"""Contains the cart and any errors after adding products."""
type ReorderItemsOutput {
  """Detailed information about the customer's cart."""
  cart: Cart!

  """An array of reordering errors."""
  userInputErrors: [CheckoutUserInputError]!
}

"""An error encountered while adding an item to the cart."""
type CheckoutUserInputError {
  """An error code that is specific to Checkout."""
  code: CheckoutUserInputErrorCodes!

  """A localized error message."""
  message: String!

  """
  The path to the input field that caused an error. See the GraphQL specification about path errors for details: http://spec.graphql.org/draft/#sec-Errors
  """
  path: [String]!
}

"""Identifies the filter to use for filtering orders."""
input CustomerOrdersFilterInput {
  """Filters by order number."""
  number: FilterStringTypeInput
}

"""
CustomerOrderSortInput specifies the field to use for sorting search results and indicates whether the results are sorted in ascending or descending order.
"""
input CustomerOrderSortInput {
  """
  This enumeration indicates whether to return results in ascending or descending order
  """
  sort_direction: SortEnum!

  """Specifies the field to use for sorting"""
  sort_field: CustomerOrderSortableField!
}

"""Specifies the field to use for sorting"""
enum CustomerOrderSortableField {
  """Sorts customer orders by number"""
  NUMBER

  """Sorts customer orders by created_at field"""
  CREATED_AT
}

"""
The collection of orders that match the conditions defined in the filter.
"""
type CustomerOrders {
  """An array of customer orders."""
  items: [CustomerOrder]!

  """Contains pagination metadata."""
  page_info: SearchResultPageInfo

  """The total count of customer orders."""
  total_count: Int
}

"""
Contains detailed information about an order's billing and shipping addresses.
"""
type OrderAddress {
  """The city or town."""
  city: String!

  """The customer's company."""
  company: String

  """The customer's country."""
  country_code: CountryCodeEnum

  """The fax number."""
  fax: String

  """
  The first name of the person associated with the shipping/billing address.
  """
  firstname: String!

  """
  The family name of the person associated with the shipping/billing address.
  """
  lastname: String!

  """
  The middle name of the person associated with the shipping/billing address.
  """
  middlename: String

  """The customer's ZIP or postal code."""
  postcode: String

  """An honorific, such as Dr., Mr., or Mrs."""
  prefix: String

  """The state or province name."""
  region: String

  """The unique ID for a `Region` object of a pre-defined region."""
  region_id: ID

  """An array of strings that define the street number and name."""
  street: [String]!

  """A value such as Sr., Jr., or III."""
  suffix: String

  """The telephone number."""
  telephone: String

  """The customer's Value-added tax (VAT) number (for corporate customers)."""
  vat_id: String
}

""""""
type OrderItem implements OrderItemInterface {
  """The final discount information for the product."""
  discounts: [Discount]

  """The entered option for the base product, such as a logo or image."""
  entered_options: [OrderItemOption]

  """The selected gift message for the order item"""
  gift_message: GiftMessage

  """The unique ID for an `OrderItemInterface` object."""
  id: ID!

  """
  The ProductInterface object, which contains details about the base product
  """
  product: ProductInterface

  """The name of the base product."""
  product_name: String

  """The sale price of the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The type of product, such as simple, configurable, etc."""
  product_type: String

  """URL key of the base product."""
  product_url_key: String

  """The number of canceled items."""
  quantity_canceled: Float

  """The number of invoiced items."""
  quantity_invoiced: Float

  """The number of units ordered for this item."""
  quantity_ordered: Float

  """The number of refunded items."""
  quantity_refunded: Float

  """The number of returned items."""
  quantity_returned: Float

  """The number of shipped items."""
  quantity_shipped: Float

  """The selected options for the base product, such as color or size."""
  selected_options: [OrderItemOption]

  """The status of the order item."""
  status: String
}

"""Represents order item options like selected or entered."""
type OrderItemOption {
  """The name of the option."""
  label: String!

  """The value of the option."""
  value: String!
}

"""Contains tax item details."""
type TaxItem {
  """The amount of tax applied to the item."""
  amount: Money!

  """The rate used to calculate the tax."""
  rate: Float!

  """A title that describes the tax."""
  title: String!
}

"""
Contains details about the sales total amounts used to calculate the final price.
"""
type OrderTotal {
  """The final base grand total amount in the base currency."""
  base_grand_total: Money!

  """The applied discounts to the order."""
  discounts: [Discount]

  """The final total amount, including shipping, discounts, and taxes."""
  grand_total: Money!

  """Details about the shipping and handling costs for the order."""
  shipping_handling: ShippingHandling

  """The subtotal of the order, excluding shipping, discounts, and taxes."""
  subtotal: Money!

  """The order tax details."""
  taxes: [TaxItem]

  """The shipping amount for the order."""
  total_shipping: Money!

  """The amount of tax applied to the order."""
  total_tax: Money!
}

"""Contains invoice details."""
type Invoice {
  """Comments on the invoice."""
  comments: [SalesCommentItem]

  """The unique ID for a `Invoice` object."""
  id: ID!

  """Invoiced product details."""
  items: [InvoiceItemInterface]

  """Sequential invoice number."""
  number: String!

  """Invoice total amount details."""
  total: InvoiceTotal
}

"""Contains detailes about invoiced items."""
interface InvoiceItemInterface {
  """
  Information about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for an `InvoiceItemInterface` object."""
  id: ID!

  """Details about an individual order item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of invoiced items."""
  quantity_invoiced: Float
}

""""""
type InvoiceItem implements InvoiceItemInterface {
  """
  Information about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for an `InvoiceItemInterface` object."""
  id: ID!

  """Details about an individual order item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of invoiced items."""
  quantity_invoiced: Float
}

"""Contains price details from an invoice."""
type InvoiceTotal {
  """The final base grand total amount in the base currency."""
  base_grand_total: Money!

  """The applied discounts to the invoice."""
  discounts: [Discount]

  """The final total amount, including shipping, discounts, and taxes."""
  grand_total: Money!

  """Details about the shipping and handling costs for the invoice."""
  shipping_handling: ShippingHandling

  """The subtotal of the invoice, excluding shipping, discounts, and taxes."""
  subtotal: Money!

  """The invoice tax details."""
  taxes: [TaxItem]

  """The shipping amount for the invoice."""
  total_shipping: Money!

  """The amount of tax applied to the invoice."""
  total_tax: Money!
}

"""Contains details about shipping and handling costs."""
type ShippingHandling {
  """The shipping amount, excluding tax."""
  amount_excluding_tax: Money

  """The shipping amount, including tax."""
  amount_including_tax: Money

  """The applied discounts to the shipping."""
  discounts: [ShippingDiscount]

  """Details about taxes applied for shipping."""
  taxes: [TaxItem]

  """The total amount for shipping."""
  total_amount: Money!
}

"""
Defines an individual shipping discount. This discount can be applied to shipping.
"""
type ShippingDiscount {
  """The amount of the discount."""
  amount: Money!
}

"""Contains order shipment details."""
type OrderShipment {
  """Comments added to the shipment."""
  comments: [SalesCommentItem]

  """The unique ID for a `OrderShipment` object."""
  id: ID!

  """An array of items included in the shipment."""
  items: [ShipmentItemInterface]

  """The sequential credit shipment number."""
  number: String!

  """An array of shipment tracking details."""
  tracking: [ShipmentTracking]
}

"""Contains details about a comment."""
type SalesCommentItem {
  """The text of the message."""
  message: String!

  """The timestamp of the comment."""
  timestamp: String!
}

"""Order shipment item details."""
interface ShipmentItemInterface {
  """The unique ID for a `ShipmentItemInterface` object."""
  id: ID!

  """The order item associated with the shipment item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of shipped items."""
  quantity_shipped: Float!
}

""""""
type ShipmentItem implements ShipmentItemInterface {
  """The unique ID for a `ShipmentItemInterface` object."""
  id: ID!

  """The order item associated with the shipment item."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of shipped items."""
  quantity_shipped: Float!
}

"""Contains order shipment tracking details."""
type ShipmentTracking {
  """The shipping carrier for the order delivery."""
  carrier: String!

  """The tracking number of the order shipment."""
  number: String

  """The shipment tracking title."""
  title: String!
}

"""Contains details about the payment method used to pay for the order."""
type OrderPaymentMethod {
  """Additional data per payment method type."""
  additional_data: [KeyValue]

  """The label that describes the payment method."""
  name: String!

  """The payment method code that indicates how the order was paid for."""
  type: String!
}

"""Contains credit memo details."""
type CreditMemo {
  """Comments on the credit memo."""
  comments: [SalesCommentItem]

  """The unique ID for a `CreditMemo` object."""
  id: ID!

  """An array containing details about refunded items."""
  items: [CreditMemoItemInterface]

  """The sequential credit memo number."""
  number: String!

  """Details about the total refunded amount."""
  total: CreditMemoTotal
}

"""Credit memo item details."""
interface CreditMemoItemInterface {
  """
  Details about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for a `CreditMemoItemInterface` object."""
  id: ID!

  """The order item the credit memo is applied to."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of refunded items."""
  quantity_refunded: Float
}

""""""
type CreditMemoItem implements CreditMemoItemInterface {
  """
  Details about the final discount amount for the base product, including discounts on options.
  """
  discounts: [Discount]

  """The unique ID for a `CreditMemoItemInterface` object."""
  id: ID!

  """The order item the credit memo is applied to."""
  order_item: OrderItemInterface

  """The name of the base product."""
  product_name: String

  """The sale price for the base product, including selected options."""
  product_sale_price: Money!

  """The SKU of the base product."""
  product_sku: String!

  """The number of refunded items."""
  quantity_refunded: Float
}

"""Contains credit memo price details."""
type CreditMemoTotal {
  """An adjustment manually applied to the order."""
  adjustment: Money!

  """The final base grand total amount in the base currency."""
  base_grand_total: Money!

  """The applied discounts to the credit memo."""
  discounts: [Discount]

  """The final total amount, including shipping, discounts, and taxes."""
  grand_total: Money!

  """Details about the shipping and handling costs for the credit memo."""
  shipping_handling: ShippingHandling

  """The subtotal of the invoice, excluding shipping, discounts, and taxes."""
  subtotal: Money!

  """The credit memo tax details."""
  taxes: [TaxItem]

  """The shipping amount for the credit memo."""
  total_shipping: Money!

  """The amount of tax applied to the credit memo."""
  total_tax: Money!
}

"""Contains a key-value pair."""
type KeyValue {
  """The name part of the key/value pair."""
  name: String

  """The value part of the key/value pair."""
  value: String
}

""""""
enum CheckoutUserInputErrorCodes {
  """"""
  REORDER_NOT_AVAILABLE

  """"""
  PRODUCT_NOT_FOUND

  """"""
  NOT_SALABLE

  """"""
  INSUFFICIENT_STOCK

  """"""
  UNDEFINED
}

"""This enumeration defines the scope type for customer orders."""
enum ScopeTypeEnum {
  """"""
  GLOBAL

  """"""
  WEBSITE

  """"""
  STORE
}

"""Input to retrieve an order based on token."""
input OrderTokenInput {
  """Order token."""
  token: String!
}

"""Input to retrieve an order based on details."""
input OrderInformationInput {
  """Order billing address email."""
  email: String!

  """Order number."""
  number: String!

  """Order billing address postcode."""
  postcode: String!
}

"""Defines the referenced product and the email sender and recipients."""
input SendEmailToFriendInput {
  """The ID of the product that the sender is referencing."""
  product_id: Int!

  """An array containing information about each recipient."""
  recipients: [SendEmailToFriendRecipientInput]!

  """Information about the customer and the content of the message."""
  sender: SendEmailToFriendSenderInput!
}

"""Contains details about the sender."""
input SendEmailToFriendSenderInput {
  """The email address of the sender."""
  email: String!

  """The text of the message to be sent."""
  message: String!

  """The name of the sender."""
  name: String!
}

"""Contains details about a recipient."""
input SendEmailToFriendRecipientInput {
  """The email address of the recipient."""
  email: String!

  """The name of the recipient."""
  name: String!
}

"""Contains information about the sender and recipients."""
type SendEmailToFriendOutput {
  """An array containing information about each recipient."""
  recipients: [SendEmailToFriendRecipient]

  """Information about the customer and the content of the message."""
  sender: SendEmailToFriendSender
}

"""An output object that contains information about the sender."""
type SendEmailToFriendSender {
  """The email address of the sender."""
  email: String!

  """The text of the message to be sent."""
  message: String!

  """The name of the sender."""
  name: String!
}

"""An output object that contains information about the recipient."""
type SendEmailToFriendRecipient {
  """The email address of the recipient."""
  email: String!

  """The name of the recipient."""
  name: String!
}

"""
Contains details about the configuration of the Email to a Friend feature.
"""
type SendFriendConfiguration {
  """Indicates whether the Email to a Friend feature is enabled."""
  enabled_for_customers: Boolean!

  """Indicates whether the Email to a Friend feature is enabled for guests."""
  enabled_for_guests: Boolean!
}

"""Contains the `uid`, `relative_url`, and `type` attributes."""
type EntityUrl {
  canonical_url: String @deprecated(reason: "Use `relative_url` instead.")

  """
  The unique ID for a `ProductInterface`, `CategoryInterface`, `CmsPage`, or similar object associated with the specified URL. This could be a product, category, or CMS page UID.
  """
  entity_uid: ID

  """
  The ID assigned to the object associated with the specified url. This could be a product ID, category ID, or page ID.
  """
  id: Int @deprecated(reason: "Use `entity_uid` instead.")

  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirectCode: Int

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum
}

"""Contains URL rewrite details."""
type UrlRewrite {
  """An array of request parameters."""
  parameters: [HttpQueryParameter]

  """The request URL."""
  url: String
}

"""Contains target path parameters."""
type HttpQueryParameter {
  """A parameter name."""
  name: String

  """A parameter value."""
  value: String
}

"""
Default implementation of RoutableInterface. This type is returned when the URL is not linked to an entity.
"""
type RoutableUrl implements RoutableInterface {
  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum
}

"""Routable entities serve as the model for a rendered page."""
interface RoutableInterface {
  """
  Contains 0 when there is no redirect error. A value of 301 indicates the URL of the requested resource has been changed permanently, while a value of 302 indicates a temporary redirect.
  """
  redirect_code: Int!

  """
  The internal relative URL. If the specified URL is a redirect, the query returns the redirected URL, not the original.
  """
  relative_url: String

  """One of PRODUCT, CATEGORY, or CMS_PAGE."""
  type: UrlRewriteEntityTypeEnum
}

"""
Defines an object used to iterate through items for product comparisons.
"""
type ComparableItem {
  """An array of product attributes that can be used to compare products."""
  attributes: [ProductAttribute]!

  """Details about a product in a compare list."""
  product: ProductInterface!

  """The unique ID of an item in a compare list."""
  uid: ID!
}

"""Contains a product attribute code and value."""
type ProductAttribute {
  """The unique identifier for a product attribute code."""
  code: String!

  """The display value of the attribute."""
  value: String!
}

"""Contains an attribute code that is used for product comparisons."""
type ComparableAttribute {
  """An attribute code that is enabled for product comparisons."""
  code: String!

  """The label of the attribute code."""
  label: String!
}

"""
Contains iterable information such as the array of items, the count, and attributes that represent the compare list.
"""
type CompareList {
  """An array of attributes that can be used for comparing products."""
  attributes: [ComparableAttribute]

  """The number of items in the compare list."""
  item_count: Int!

  """An array of products to compare."""
  items: [ComparableItem]

  """The unique ID assigned to the compare list."""
  uid: ID!
}

"""Contains an array of product IDs to use for creating a compare list."""
input CreateCompareListInput {
  """An array of product IDs to add to the compare list."""
  products: [ID]
}

"""Contains products to add to an existing compare list."""
input AddProductsToCompareListInput {
  """An array of product IDs to add to the compare list."""
  products: [ID]!

  """The unique identifier of the compare list to modify."""
  uid: ID!
}

"""Defines which products to remove from a compare list."""
input RemoveProductsFromCompareListInput {
  """An array of product IDs to remove from the compare list."""
  products: [ID]!

  """The unique identifier of the compare list to modify."""
  uid: ID!
}

"""Contains the results of the request to delete a compare list."""
type DeleteCompareListOutput {
  """Indicates whether the compare list was successfully deleted."""
  result: Boolean!
}

"""Contains the results of the request to assign a compare list."""
type AssignCompareListToCustomerOutput {
  """The contents of the customer's compare list."""
  compare_list: CompareList

  """
  Indicates whether the compare list was successfully assigned to the customer.
  """
  result: Boolean!
}

""""""
interface SwatchLayerFilterItemInterface {
  """Data required to render a swatch filter item."""
  swatch_data: SwatchData
}

""""""
type SwatchLayerFilterItem implements LayerFilterItemInterface & SwatchLayerFilterItemInterface {
  """The count of items per filter."""
  items_count: Int @deprecated(reason: "Use `AggregationOption.count` instead.")

  """The label for a filter."""
  label: String @deprecated(reason: "Use `AggregationOption.label` instead.")

  """Data required to render a swatch filter item."""
  swatch_data: SwatchData

  """The value of a filter request variable to be used in query."""
  value_string: String @deprecated(reason: "Use `AggregationOption.value` instead.")
}

"""Describes the swatch type and a value."""
type SwatchData {
  """The type of swatch filter item: 1 - text; 2 - image."""
  type: String

  """The value for the swatch item. It could be text or an image link."""
  value: String
}

""""""
interface SwatchDataInterface {
  """The value can be represented as color (HEX code), image link, or text."""
  value: String
}

""""""
type ImageSwatchData implements SwatchDataInterface {
  """The URL assigned to the thumbnail of the swatch image."""
  thumbnail: String

  """The value can be represented as color (HEX code), image link, or text."""
  value: String
}

""""""
type TextSwatchData implements SwatchDataInterface {
  """The value can be represented as color (HEX code), image link, or text."""
  value: String
}

""""""
type ColorSwatchData implements SwatchDataInterface {
  """The value can be represented as color (HEX code), image link, or text."""
  value: String
}

"""Swatch attribute metadata input types."""
enum SwatchInputTypeEnum {
  """"""
  BOOLEAN

  """"""
  DATE

  """"""
  DATETIME

  """"""
  DROPDOWN

  """"""
  FILE

  """"""
  GALLERY

  """"""
  HIDDEN

  """"""
  IMAGE

  """"""
  MEDIA_IMAGE

  """"""
  MULTILINE

  """"""
  MULTISELECT

  """"""
  PRICE

  """"""
  SELECT

  """"""
  TEXT

  """"""
  TEXTAREA

  """"""
  UNDEFINED

  """"""
  VISUAL

  """"""
  WEIGHT
}

""""""
enum TaxWrappingEnum {
  """"""
  DISPLAY_EXCLUDING_TAX

  """"""
  DISPLAY_INCLUDING_TAX

  """"""
  DISPLAY_TYPE_BOTH
}

"""
Defines the attributes required to receive a payment token for Express Checkout and Payments Standard payment methods.
"""
input PaypalExpressTokenInput {
  """The unique ID that identifies the customer's cart."""
  cart_id: String!

  """The payment method code."""
  code: String!

  """
  Indicates whether the buyer selected the quick checkout button. The default value is false.
  """
  express_button: Boolean

  """
  A set of relative URLs that PayPal uses in response to various actions during the authorization process.
  """
  urls: PaypalExpressUrlsInput!

  """
  Indicates whether the buyer clicked the PayPal credit button. The default value is false.
  """
  use_paypal_credit: Boolean
}

"""Deprecated. Use `PaypalExpressTokenOutput` instead."""
type PaypalExpressToken {
  """
  A set of URLs that allow the buyer to authorize payment and adjust checkout details.
  """
  paypal_urls: PaypalExpressUrlList @deprecated(reason: "Use `PaypalExpressTokenOutput.paypal_urls` instead.")

  """The token returned by PayPal."""
  token: String @deprecated(reason: "Use `PaypalExpressTokenOutput.token` instead.")
}

"""
Contains the token returned by PayPal and a set of URLs that allow the buyer to authorize payment and adjust checkout details. Applies to Express Checkout and Payments Standard payment methods.
"""
type PaypalExpressTokenOutput {
  """
  A set of URLs that allow the buyer to authorize payment and adjust checkout details.
  """
  paypal_urls: PaypalExpressUrlList

  """The token returned by PayPal."""
  token: String
}

"""
Contains information used to generate PayPal iframe for transaction. Applies to Payflow Link and Payments Advanced payment methods.
"""
type PayflowLinkToken {
  """The mode for the Payflow transaction."""
  mode: PayflowLinkMode

  """The PayPal URL used for requesting a Payflow form."""
  paypal_url: String

  """The secure token generated by PayPal."""
  secure_token: String

  """The secure token ID generated by PayPal."""
  secure_token_id: String
}

"""
Contains the secure URL used for the Payments Pro Hosted Solution payment method.
"""
type HostedProUrl {
  """The secure URL generated by PayPal."""
  secure_form_url: String
}

"""
Contains the required input to request the secure URL for Payments Pro Hosted Solution payment.
"""
input HostedProUrlInput {
  """The unique ID that identifies the shopper's cart."""
  cart_id: String!
}

"""
Contains a set of relative URLs that PayPal uses in response to various actions during the authorization process. Magento prepends the base URL to this value to create a full URL. For example, if the full URL is https://www.example.com/path/to/page.html, the relative URL is path/to/page.html. Use this input for Payments Pro Hosted Solution payment method.
"""
input HostedProInput {
  """
  The relative URL of the page that PayPal redirects to when the buyer cancels the transaction in order to choose a different payment method. For example, if the full URL to this page is https://www.example.com/paypal/action/cancel.html, the relative URL is paypal/action/cancel.html.
  """
  cancel_url: String!

  """
  The relative URL of the final confirmation page that PayPal redirects to upon payment success. For example, if the full URL to this page is https://www.example.com/paypal/action/return.html, the relative URL is paypal/action/return.html.
  """
  return_url: String!
}

"""
Contains required input for Express Checkout and Payments Standard payments.
"""
input PaypalExpressInput {
  """The unique ID of the PayPal user."""
  payer_id: String!

  """The token returned by the `createPaypalExpressToken` mutation."""
  token: String!
}

"""Contains required input for Payflow Express Checkout payments."""
input PayflowExpressInput {
  """The unique ID of the PayPal user."""
  payer_id: String!

  """The token returned by the createPaypalExpressToken mutation."""
  token: String!
}

"""
Contains a set of relative URLs that PayPal uses in response to various actions during the authorization process. Magento prepends the base URL to this value to create a full URL. For example, if the full URL is https://www.example.com/path/to/page.html, the relative URL is path/to/page.html. Use this input for Express Checkout and Payments Standard payment methods.
"""
input PaypalExpressUrlsInput {
  """
  The relative URL of the page that PayPal redirects to when the buyer cancels the transaction in order to choose a different payment method. If the full URL to this page is https://www.example.com/paypal/action/cancel.html, the relative URL is paypal/action/cancel.html.
  """
  cancel_url: String!

  """
  The relative URL of the page that PayPal redirects to when the payment has been put on hold for additional review. This condition mostly applies to ACH transactions, and is not applicable to most PayPal solutions. If the full URL to this page is https://www.example.com/paypal/action/success_pending.html, the relative URL is paypal/action/success_pending.html.
  """
  pending_url: String

  """
  The relative URL of the final confirmation page that PayPal redirects to upon payment success. If the full URL to this page is https://www.example.com/paypal/action/return.html, the relative URL is paypal/action/return.html.
  """
  return_url: String!

  """
  The relative URL of the order confirmation page that PayPal redirects to when the payment is successful and additional confirmation is not needed. Not applicable to most PayPal solutions. If the full URL to this page is https://www.example.com/paypal/action/success.html, the relative URL is paypal/action/success.html.
  """
  success_url: String
}

"""
Contains a set of URLs that allow the buyer to authorize payment and adjust checkout details for Express Checkout and Payments Standard transactions.
"""
type PaypalExpressUrlList {
  """The PayPal URL that allows the buyer to edit their checkout details."""
  edit: String

  """The URL to the PayPal login page."""
  start: String
}

"""
A set of relative URLs that PayPal uses in response to various actions during the authorization process. Adobe Commerce prepends the base URL to this value to create a full URL. For example, if the full URL is https://www.example.com/path/to/page.html, the relative URL is path/to/page.html. Use this input for Payflow Link and Payments Advanced payment methods.
"""
input PayflowLinkInput {
  """
  The relative URL of the page that PayPal redirects to when the buyer cancels the transaction in order to choose a different payment method. If the full URL to this page is https://www.example.com/paypal/action/cancel.html, the relative URL is paypal/action/cancel.html.
  """
  cancel_url: String!

  """
  The relative URL of the transaction error page that PayPal redirects to upon payment error. If the full URL to this page is https://www.example.com/paypal/action/error.html, the relative URL is paypal/action/error.html.
  """
  error_url: String!

  """
  The relative URL of the order confirmation page that PayPal redirects to when the payment is successful and additional confirmation is not needed. If the full URL to this page is https://www.example.com/paypal/action/return.html, the relative URL is paypal/action/return.html.
  """
  return_url: String!
}

"""
Contains information required to fetch payment token information for the Payflow Link and Payments Advanced payment methods.
"""
input PayflowLinkTokenInput {
  """The unique ID that identifies the customer's cart."""
  cart_id: String!
}

"""
Indicates the mode for payment. Applies to the Payflow Link and Payments Advanced payment methods.
"""
enum PayflowLinkMode {
  """"""
  TEST

  """"""
  LIVE
}

"""
Contains input required to fetch payment token information for the Payflow Pro and Payments Pro payment methods.
"""
input PayflowProTokenInput {
  """The unique ID that identifies the shopper's cart."""
  cart_id: String!

  """A set of relative URLs that PayPal uses for callback."""
  urls: PayflowProUrlInput!
}

"""Contains input for the Payflow Pro and Payments Pro payment methods."""
input PayflowProInput {
  """Required input for credit card related information."""
  cc_details: CreditCardDetailsInput!

  """
  Indicates whether details about the shopper's credit/debit card should be tokenized for later usage. Required only if Vault is enabled for the PayPal Payflow Pro payment integration.
  """
  is_active_payment_token_enabler: Boolean
}

"""Required fields for Payflow Pro and Payments Pro credit card payments."""
input CreditCardDetailsInput {
  """The credit card expiration month."""
  cc_exp_month: Int!

  """The credit card expiration year."""
  cc_exp_year: Int!

  """The last 4 digits of the credit card."""
  cc_last_4: Int!

  """The credit card type."""
  cc_type: String!
}

"""
Contains a set of relative URLs that PayPal uses in response to various actions during the authorization process. Magento prepends the base URL to this value to create a full URL. For example, if the full URL is https://www.example.com/path/to/page.html, the relative URL is path/to/page.html. Use this input for the Payflow Pro and Payment Pro payment methods.
"""
input PayflowProUrlInput {
  """
  The relative URL of the page that PayPal redirects to when the buyer cancels the transaction in order to choose a different payment method. If the full URL to this page is https://www.example.com/paypal/action/cancel.html, the relative URL is paypal/action/cancel.html.
  """
  cancel_url: String!

  """
  The relative URL of the transaction error page that PayPal redirects to upon payment error. If the full URL to this page is https://www.example.com/paypal/action/error.html, the relative URL is paypal/action/error.html.
  """
  error_url: String!

  """
  The relative URL of the final confirmation page that PayPal redirects to upon payment success. If the full URL to this page is https://www.example.com/paypal/action/return.html, the relative URL is paypal/action/return.html.
  """
  return_url: String!
}

"""
Contains the secure information used to authorize transaction. Applies to Payflow Pro and Payments Pro payment methods.
"""
type PayflowProToken {
  """
  The RESPMSG returned by PayPal. If the `result` is `0`, then `response_message` is `Approved`.
  """
  response_message: String!

  """A non-zero value if any errors occurred."""
  result: Int!

  """
  The RESULT returned by PayPal. A value of `0` indicates the transaction was approved.
  """
  result_code: Int!

  """A secure token generated by PayPal."""
  secure_token: String!

  """A secure token ID generated by PayPal."""
  secure_token_id: String!
}

"""
Contains the secure information used to authorize transaction. Applies to Payflow Pro and Payments Pro payment methods.
"""
type CreatePayflowProTokenOutput {
  """
  The RESPMSG returned by PayPal. If the `result` is `0`, then `response_message` is `Approved`.
  """
  response_message: String!

  """A non-zero value if any errors occurred."""
  result: Int!

  """
  The RESULT returned by PayPal. A value of `0` indicates the transaction was approved.
  """
  result_code: Int!

  """A secure token generated by PayPal."""
  secure_token: String!

  """A secure token ID generated by PayPal."""
  secure_token_id: String!
}

"""
Input required to complete payment. Applies to Payflow Pro and Payments Pro payment methods.
"""
input PayflowProResponseInput {
  """The unique ID that identifies the shopper's cart."""
  cart_id: String!

  """The payload returned from PayPal."""
  paypal_payload: String!
}

""""""
type PayflowProResponseOutput {
  """The cart with the updated selected payment method."""
  cart: Cart!
}

"""Contains required input for payment methods with Vault support."""
input VaultTokenInput {
  """The public hash of the payment token."""
  public_hash: String!
}

"""
Indicates whether the request succeeded and returns the remaining customer payment tokens.
"""
type DeletePaymentTokenOutput {
  """A container for the customer's remaining payment tokens."""
  customerPaymentTokens: CustomerPaymentTokens

  """Indicates whether the request succeeded."""
  result: Boolean!
}

"""Contains payment tokens stored in the customer's vault."""
type CustomerPaymentTokens {
  """An array of payment tokens."""
  items: [PaymentToken]!
}

"""The stored payment method available to the customer."""
type PaymentToken {
  """A description of the stored account details."""
  details: String

  """The payment method code associated with the token."""
  payment_method_code: String!

  """The public hash of the token."""
  public_hash: String!

  """Specifies the payment token type."""
  type: PaymentTokenTypeEnum!
}

"""The list of available payment token types."""
enum PaymentTokenTypeEnum {
  """phpcs:ignore Magento2.GraphQL.ValidArgumentName"""
  card

  """phpcs:ignore Magento2.GraphQL.ValidArgumentName"""
  account
}

"""A single FPT that can be applied to a product price."""
type FixedProductTax {
  """The amount of the Fixed Product Tax."""
  amount: Money

  """The display label assigned to the Fixed Product Tax."""
  label: String
}

"""Lists display settings for the Fixed Product Tax."""
enum FixedProductTaxDisplaySettings {
  """
  The displayed price includes the FPT amount without displaying the `ProductPrice.fixed_product_taxes` values. This value corresponds to 'Including FPT only'.
  """
  INCLUDE_FPT_WITHOUT_DETAILS

  """
  The displayed price includes the FPT amount while displaying the values of `ProductPrice.fixed_product_taxes` separately. This value corresponds to 'Including FPT and FPT description'.
  """
  INCLUDE_FPT_WITH_DETAILS

  """
  The displayed price does not include the FPT amount. The values of `ProductPrice.fixed_product_taxes` and the price including the FPT are displayed separately. This value corresponds to 'Excluding FPT, Including FPT description and final price.'
  """
  EXCLUDE_FPT_AND_INCLUDE_WITH_DETAILS

  """
  The displayed price does not include the FPT amount. The values from `ProductPrice.fixed_product_taxes` are not displayed. This value corresponds to 'Excluding FPT'.
  """
  EXCLUDE_FPT_WITHOUT_DETAILS

  """
  The FPT feature is not enabled. You can omit `ProductPrice.fixed_product_taxes` from your query.
  """
  FPT_DISABLED
}

"""Deprecated: Use the `Wishlist` type instead."""
type WishlistOutput {
  """An array of items in the customer's wish list"""
  items: [WishlistItem] @deprecated(reason: "Use the `Wishlist.items` field instead.")

  """The number of items in the wish list."""
  items_count: Int @deprecated(reason: "Use the `Wishlist.items_count` field instead.")

  """
  When multiple wish lists are enabled, the name the customer assigns to the wishlist.
  """
  name: String @deprecated(reason: "This field is related to Commerce functionality and is always `null` in Open Source.")

  """An encrypted code that links to the wish list."""
  sharing_code: String @deprecated(reason: "Use the `Wishlist.sharing_code` field instead.")

  """The time of the last modification to the wish list."""
  updated_at: String @deprecated(reason: "Use the `Wishlist.updated_at` field instead.")
}

"""Contains a customer wish list."""
type Wishlist {
  """The unique ID for a `Wishlist` object."""
  id: ID
  items: [WishlistItem] @deprecated(reason: "Use the `items_v2` field instead.")

  """The number of items in the wish list."""
  items_count: Int

  """An array of items in the customer's wish list."""
  items_v2(currentPage: Int = 1, pageSize: Int = 20): WishlistItems

  """An encrypted code that Magento uses to link to the wish list."""
  sharing_code: String

  """The time of the last modification to the wish list."""
  updated_at: String
}

"""The interface for wish list items."""
interface WishlistItemInterface {
  """The date and time the item was added to the wish list."""
  added_at: String!

  """Custom options selected for the wish list item."""
  customizable_options: [SelectedCustomizableOption]!

  """The description of the item."""
  description: String

  """The unique ID for a `WishlistItemInterface` object."""
  id: ID!

  """Product details of the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item."""
  quantity: Float!
}

"""Contains an array of items in a wish list."""
type WishlistItems {
  """A list of items in the wish list."""
  items: [WishlistItemInterface]!

  """Contains pagination metadata."""
  page_info: SearchResultPageInfo
}

"""Contains details about a wish list item."""
type WishlistItem {
  """The time when the customer added the item to the wish list."""
  added_at: String

  """The customer's comment about this item."""
  description: String

  """The unique ID for a `WishlistItem` object."""
  id: Int

  """Details about the wish list item."""
  product: ProductInterface

  """The quantity of this wish list item"""
  qty: Float
}

"""Contains the resultant wish list and any error information."""
type AddWishlistItemsToCartOutput {
  """
  An array of errors encountered while adding products to the customer's cart.
  """
  add_wishlist_items_to_cart_user_errors: [WishlistCartUserInputError]!

  """
  Indicates whether the attempt to add items to the customer's cart was successful.
  """
  status: Boolean!

  """Contains the wish list with all items that were successfully added."""
  wishlist: Wishlist!
}

"""
Contains details about errors encountered when a customer added wish list items to the cart.
"""
type WishlistCartUserInputError {
  """An error code that describes the error encountered."""
  code: WishlistCartUserInputErrorType!

  """A localized error message."""
  message: String!

  """The unique ID of the `Wishlist` object containing an error."""
  wishlistId: ID!

  """The unique ID of the wish list item containing an error."""
  wishlistItemId: ID!
}

"""A list of possible error types."""
enum WishlistCartUserInputErrorType {
  """"""
  PRODUCT_NOT_FOUND

  """"""
  NOT_SALABLE

  """"""
  INSUFFICIENT_STOCK

  """"""
  UNDEFINED
}

"""Defines the items to add to a wish list."""
input WishlistItemInput {
  """An array of options that the customer entered."""
  entered_options: [EnteredOptionInput]

  """For complex product types, the SKU of the parent product."""
  parent_sku: String

  """The amount or number of items to add."""
  quantity: Float!

  """An array of strings corresponding to options the customer selected."""
  selected_options: [ID]

  """
  The SKU of the product to add. For complex product types, specify the child product SKU.
  """
  sku: String!
}

"""Contains the customer's wish list and any errors encountered."""
type AddProductsToWishlistOutput {
  """An array of errors encountered while adding products to a wish list."""
  user_errors: [WishListUserInputError]!

  """Contains the wish list with all items that were successfully added."""
  wishlist: Wishlist!
}

"""Contains the customer's wish list and any errors encountered."""
type RemoveProductsFromWishlistOutput {
  """
  An array of errors encountered while deleting products from a wish list.
  """
  user_errors: [WishListUserInputError]!

  """Contains the wish list with after items were successfully deleted."""
  wishlist: Wishlist!
}

"""Defines updates to items in a wish list."""
input WishlistItemUpdateInput {
  """Customer-entered comments about the item."""
  description: String

  """An array of options that the customer entered."""
  entered_options: [EnteredOptionInput]

  """The new amount or number of this item."""
  quantity: Float

  """An array of strings corresponding to options the customer selected."""
  selected_options: [ID]

  """The unique ID for a `WishlistItemInterface` object."""
  wishlist_item_id: ID!
}

"""Contains the customer's wish list and any errors encountered."""
type UpdateProductsInWishlistOutput {
  """An array of errors encountered while updating products in a wish list."""
  user_errors: [WishListUserInputError]!

  """Contains the wish list with all items that were successfully updated."""
  wishlist: Wishlist!
}

"""An error encountered while performing operations with WishList."""
type WishListUserInputError {
  """A wish list-specific error code."""
  code: WishListUserInputErrorType!

  """A localized error message."""
  message: String!
}

"""A list of possible error types."""
enum WishListUserInputErrorType {
  """"""
  PRODUCT_NOT_FOUND

  """"""
  UNDEFINED
}

""""""
input BraintreeInput {
  """
  Contains a fingerprint provided by Braintree JS SDK and should be sent with sale transaction details to the Braintree payment gateway.
  """
  device_data: String

  """
  States whether the payment details (Credit/Debit Card, PayPal Account) entered by a customer should be tokenized for later usage. Required only if Vault is enabled for the relevant Braintree payment integration.
  """
  is_active_payment_token_enabler: Boolean!

  """
  The one-time payment token generated by Braintree payment gateway based on payment details (Card, PayPal). Required field to make sale transaction.
  """
  payment_method_nonce: String!
}

""""""
input BraintreeCcVaultInput {
  """"""
  device_data: String

  """"""
  public_hash: String!
}

""""""
input BraintreeVaultInput {
  """"""
  device_data: String

  """"""
  public_hash: String!
}