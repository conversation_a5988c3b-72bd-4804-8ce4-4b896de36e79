import { WaitForQueries } from '@graphcommerce/ecommerce-ui'
import { DesktopHeaderBadge, IconSvg, iconShoppingBag } from '@graphcommerce/next-ui'
import { useCartEnabled, useCartShouldLoginToContinue, useCartQuery } from '@graphcommerce/magento-cart'
import { CartFabDocument } from '@graphcommerce/magento-cart/components/CartFab/CartFab.gql'
import { IconButton } from '@mui/material'
import { useRouter } from 'next/router'

export function DesktopCartIcon() {
  const router = useRouter()
  const cartEnabled = useCartEnabled()
  const shouldLoginToContinue = useCartShouldLoginToContinue()
  const cartQuery = useCartQuery(CartFabDocument, {
    skip: shouldLoginToContinue,
  })

  if (!cartEnabled) return null

  const handleClick = () => {
    router.push('/cart')
  }

  const renderContent = (quantity: number) => (
    <IconButton color='inherit' onClick={handleClick} size='medium'>
      <DesktopHeaderBadge
        color='primary'
        variant='dot'
        overlap='circular'
        badgeContent={quantity}
      >
        <IconSvg src={iconShoppingBag} size='large' />
      </DesktopHeaderBadge>
    </IconButton>
  )

  return (
    <WaitForQueries waitFor={cartQuery} fallback={renderContent(0)}>
      {renderContent(cartQuery.data?.cart?.total_quantity ?? 0)}
    </WaitForQueries>
  )
} 