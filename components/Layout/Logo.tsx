import { Logo as LogoBase } from '@graphcommerce/next-ui'
import svgLogo from './logo.png'

export function Logo() {
  return (
    <LogoBase
      sx={{
        '& .GcLogo-logo': {
          width: 'auto',
          height: { xs: '30px', md: '60px' },
          paddingLeft: { xs: '10px', md: '0px' },
          marginTop: { xs: 0, md: '15px' },
          filter: (theme) => (theme.palette.mode === 'dark' ? 'invert(100%)' : 'none'),
        },
      }}
      image={{ alt: 'Durovinbathrooms Logo', src: svgLogo, unoptimized: true }}
    />
  )
}
