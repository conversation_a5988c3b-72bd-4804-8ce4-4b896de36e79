import { RichText } from '@graphcommerce/hygraph-ui'
import { Image } from '@graphcommerce/image'
import { useCheckoutGuestEnabled } from '@graphcommerce/magento-cart'
import { StoreSwitcherButton } from '@graphcommerce/magento-store'
import { Footer as FooterBase } from '@graphcommerce/next-ui'
import { Trans } from '@lingui/macro'
import { Button, IconButton, Link, Typography, Box, Grid } from '@mui/material'
import { FooterQueryFragment } from './FooterQueryFragment.gql'

export type FooterProps = FooterQueryFragment

export function Footer(props: FooterProps) {
  const { footer } = props
  const cartEnabled = useCheckoutGuestEnabled()

  return (
    <FooterBase
      sx={(theme) => ({
        '& .Footer-root': {
          gridTemplateAreas: `
            'content content'
          `,
          gridTemplateColumns: '1fr',
          [theme.breakpoints.up('md')]: {
            gridTemplateAreas: `
              'content content'
            `,
          },
        },
      })}
    >
      <Box
        sx={(theme) => ({
          gridArea: 'content',
          width: '100%',
        })}
      >
        <Grid 
          container 
          spacing={4}
          sx={(theme) => ({
            [theme.breakpoints.down('md')]: {
              flexDirection: 'column',
              '& .MuiGrid-item': {
                paddingLeft: 0,
              },
            },
          })}
        >
          {/* About Us Section */}
          <Grid item xs={12} md={3}>
            <Box>
              <Typography 
                variant="h6" 
                component="h3" 
                sx={{ 
                  fontWeight: 'bold', 
                  mb: 2,
                  color: 'text.primary'
                }}
              >
                ABOUT US
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  mb: 2,
                  color: 'text.secondary',
                  lineHeight: 1.6
                }}
              >
                Durovin Bathrooms are based in Walsall, UK and a bathroom supplier to mainland UK. We sell a wide range of products for anyone to improve their bathrooms.
              </Typography>
              <Link 
                href="/about-us" 
                color="primary" 
                underline="always"
                sx={{ fontSize: '0.875rem' }}
              >
                Read More About Us
              </Link>
              
              {/* Social Links */}
              {footer?.socialLinks && footer.socialLinks.length > 0 && (
                <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                  {footer.socialLinks.map((link) => (
                    <IconButton 
                      key={link.title} 
                      href={link.url} 
                      color='inherit' 
                      size='small'
                      sx={{ 
                        border: 1, 
                        borderColor: 'divider',
                        '&:hover': {
                          borderColor: 'primary.main',
                          backgroundColor: 'primary.main',
                          color: 'primary.contrastText',
                        }
                      }}
                    >
                      {link.asset ? (
                        <Image
                          layout='fill'
                          src={link.asset.url}
                          width={16}
                          height={16}
                          unoptimized
                          alt={link.title}
                          sx={(theme) => ({
                            filter: theme.palette.mode === 'dark' ? 'invert(100%)' : 'invert(0%)',
                          })}
                        />
                      ) : (
                        link.title
                      )}
                    </IconButton>
                  ))}
                </Box>
              )}
            </Box>
          </Grid>

          {/* Quick Links Section */}
          <Grid item xs={12} md={3}>
            <Box>
              <Typography 
                variant="h6" 
                component="h3" 
                sx={{ 
                  fontWeight: 'bold', 
                  mb: 2,
                  color: 'text.primary'
                }}
              >
                QUICK LINKS
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                 {footer?.quickLinks?.map((link) => (
                   <Link 
                     key={link.title} 
                     href={link.url} 
                     color='textSecondary' 
                     underline='hover'
                     sx={{ 
                       fontSize: '0.875rem',
                       '&:hover': {
                         color: 'primary.main'
                       }
                     }}
                   >
                     {link.title}
                   </Link>
                 ))}
                {/* Default links if no quickLinks provided */}
                {(!footer?.quickLinks || footer.quickLinks.length === 0) && (
                  <>
                    <Link href="/faq"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Frequently Asked Questions
                    </Link>
                    <Link href="/blog"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Blog
                    </Link>
                    <Link href="/affiliate" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Affiliate Programme
                    </Link>
                    <Link href="/installation-guides" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Installation Guides
                    </Link>
                    <Link href="/product-certificates" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Products Certificates
                    </Link>
                    <Link href="/privacy-policy-cookie-restriction-mode"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Privacy and Cookie Policy
                    </Link>
                    <Link href="/search-terms" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Search Terms
                    </Link>
                    <Link href="/service/contact-us" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Contact Us
                    </Link>
                    <Link href="/terms-and-conditions"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Term and Conditions
                    </Link>
                  </>
                )}
              </Box>
            </Box>
          </Grid>

          {/* My Account Section */}
          <Grid item xs={12} md={3}>
            <Box>
              <Typography 
                variant="h6" 
                component="h3" 
                sx={{ 
                  fontWeight: 'bold', 
                  mb: 2,
                  color: 'text.primary'
                }}
              >
                MY ACCOUNT
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                 {footer?.myAccount?.map((link) => (
                   <Link 
                     key={link.title} 
                     href={link.url} 
                     underline='hover'
                     sx={{ 
                       fontSize: '0.875rem',
                       '&:hover': {
                         color: 'primary.main'
                       }
                     }}
                   >
                     {link.title}
                   </Link>
                 ))}
                {/* Default links if no myAccount provided */}
                {(!footer?.myAccount || footer.myAccount.length === 0) && (
                  <>
                    <Link href="/account" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      My Account
                    </Link>
                    <Link href="/account/signin" underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Login
                    </Link>
                    <Link href="/cart"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      My Cart
                    </Link>
                    <Link href="/account/orders"  underline='hover' sx={{ fontSize: '0.875rem' }}>
                      Trade Account
                    </Link>
                    {import.meta.graphCommerce.magentoVersion >= 247 && cartEnabled && (
                      <Link href='/guest/orderstatus' underline='hover' sx={{ fontSize: '0.875rem' }}>
                        <Trans>Order status</Trans>
                      </Link>
                    )}
                  </>
                )}
              </Box>
            </Box>
          </Grid>

          {/* Quick Contact Section */}
          <Grid item xs={12} md={3}>
            <Box>
              <Typography 
                variant="h6" 
                component="h3" 
                sx={{ 
                  fontWeight: 'bold', 
                  mb: 2,
                  color: 'text.primary'
                }}
              >
                QUICK CONTACT
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                {footer?.quickContact ? (
                                     <RichText 
                     {...footer.quickContact} 
                     sxRenderer={{
                       paragraph: {
                         fontSize: '0.875rem',
                         color: 'text.secondary',
                         lineHeight: 1.6,
                         margin: 0,
                         marginBottom: 1,
                       },
                     }}
                   />
                ) : (
                  <>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                        Address
                      </Typography>
                      <Typography variant="body2" sx={{fontSize: '0.875rem' }}>
                        Unit 14 Bentley Lane Industrial Park, Bentley Lane, Walsall, WS2 8TL
                      </Typography>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" sx={{fontWeight: 'bold', color: 'text.primary' }}>
                        E-Mail us
                      </Typography>
                      <Link 
                        href="mailto:<EMAIL>" 
                        underline='hover'
                        sx={{ fontSize: '0.875rem' }}
                      >
                        <EMAIL>
                      </Link>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" sx={{fontWeight: 'bold', color: 'text.primary' }}>
                        Phone
                      </Typography>
                      <Link 
                        href="tel:01922627773" 
                        underline='hover'
                        sx={{ fontSize: '0.875rem' }}
                      >
                        01922 627 773
                      </Link>
                    </Box>
                  </>
                )}
              </Box>
            </Box>
          </Grid>
        </Grid>

        {/* Copyright and Legal Links */}
        {(footer?.copyright || footer?.legalLinks) && (
          <Box 
            sx={(theme) => ({
              mt: 4,
              pt: 3,
              borderTop: `1px solid ${theme.palette.divider}`,
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              justifyContent: 'space-between',
              alignItems: { xs: 'flex-start', md: 'center' },
              gap: 2,
            })}
          >
            {footer?.copyright && (
              <Typography variant="body2" sx={{fontSize: '0.875rem' }}>
                {footer.copyright}
              </Typography>
            )}
            
            {/* {footer?.legalLinks && footer.legalLinks.length > 0 && (
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {footer.legalLinks.map((link) => (
                  <Link 
                    key={link.title} 
                    href={link.url} 
                    color='textSecondary' 
                    underline='hover'
                    sx={{ fontSize: '0.875rem' }}
                  >
                    {link.title}
                  </Link>
                ))}
              </Box>
            )} */}
          </Box>
        )}
      </Box>
    </FooterBase>
  )
}
