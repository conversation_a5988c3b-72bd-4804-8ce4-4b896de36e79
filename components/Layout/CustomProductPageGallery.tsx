import type { CustomSidebarGalleryProps } from './CustomSidebarGallery'
import { CustomSidebarGallery } from './CustomSidebarGallery'
import { nonNullable } from '@graphcommerce/next-ui'
import type { ProductPageGalleryFragment } from '@graphcommerce/magento-product/components/ProductPageGallery/ProductPageGallery.gql'

export type CustomProductPageGalleryProps = Omit<CustomSidebarGalleryProps, 'sidebar' | 'images'> & {
  product: ProductPageGalleryFragment
  children?: React.ReactNode
}

export function CustomProductPageGallery(props: CustomProductPageGalleryProps) {
  const { product, children, aspectRatio: [width, height] = [1532, 1678], ...sidebarProps } = props
  const { media_gallery } = product

  const images =
    media_gallery
      ?.filter(nonNullable)
      .filter((p) => p.disabled !== true)
      .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
      .map((item) =>
        item.__typename === 'ProductImage'
          ? {
              src: item.url ?? '',
              alt: item.label || undefined,
              width,
              height,
            }
          : {
              src: '',
              alt: `{${item.__typename} not yet supported}`,
            },
      ) ?? []

  return (
    <CustomSidebarGallery
      {...sidebarProps}
      sidebar={children}
      aspectRatio={[width, height]}
      images={images}
    />
  )
} 