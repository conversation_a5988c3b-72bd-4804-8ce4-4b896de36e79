import { usePrevPageRouter } from '@graphcommerce/framer-next-pages/hooks/usePrevPageRouter'
import type { MotionImageAspectProps } from '@graphcommerce/framer-scroller'
import {
  Motion<PERSON>mageAspect,
  Scroller,
  ScrollerProvider,
  unstable_usePreventScroll as usePreventScroll,
} from '@graphcommerce/framer-scroller'
import { dvh } from '@graphcommerce/framer-utils'
import type { SxProps, Theme } from '@mui/material'
import { Box, Fab, IconButton, styled, Unstable_TrapFocus as TrapFocus, useTheme } from '@mui/material'
import { m, useDomEvent } from 'framer-motion'
import { useRouter } from 'next/router'
import React, { useEffect, useRef, useState } from 'react'
import { iconChevronLeft, iconChevronRight, iconFullscreen, iconFullscreenExit } from '@graphcommerce/next-ui/icons'
import { IconSvg } from '@graphcommerce/next-ui/IconSvg'
import { Row } from '@graphcommerce/next-ui/Row/Row'
import { extendableComponent } from '@graphcommerce/next-ui/Styles'
import { responsiveVal } from '@graphcommerce/next-ui/Styles/responsiveVal'

const MotionBox = styled(m.div)({})

type SidebarGalleryVariant = 'default' | 'oneColumn'

type OwnerState = {
  zoomed: boolean
  disableZoom: boolean
  sticky: boolean
  variantMd: SidebarGalleryVariant
}

const name = 'CustomSidebarGallery'
const parts = [
  'row',
  'root',
  'scrollerContainer',
  'scroller',
  'sidebarWrapper',
  'sidebar',
  'bottomCenter',
  'sliderButtons',
  'toggleIcon',
  'topRight',
  'centerLeft',
  'centerRight',
  'dots',
] as const

const { withState, selectors } = extendableComponent<OwnerState, typeof name, typeof parts>(
  name,
  parts,
)

export type CustomSidebarGalleryProps = {
  sidebar: React.ReactNode
  images: MotionImageAspectProps[]
  aspectRatio?: [number, number]
  routeHash?: string
  sx?: SxProps<Theme>
  disableZoom?: boolean
  disableSticky?: boolean
  variantMd?: SidebarGalleryVariant
  beforeScroller?: React.ReactNode
  afterScroller?: React.ReactNode
}

export function CustomSidebarGallery(props: CustomSidebarGalleryProps) {
  const {
    sidebar,
    images,
    aspectRatio: [width, height] = [1, 1],
    sx,
    routeHash = 'gallery',
    disableZoom = false,
    disableSticky = false,
    variantMd = 'default',
    beforeScroller,
    afterScroller,
  } = props

  const router = useRouter()
  const prevRoute = usePrevPageRouter()

  const route = `#${routeHash}`
  const zoomed = router.asPath.endsWith(route)
  usePreventScroll(zoomed)

  // 添加当前图片索引状态
  const [currentIndex, setCurrentIndex] = useState(0)
  const scrollerRef = useRef<HTMLDivElement>(null)
  
  // 缩略图相关状态
  const [thumbnailStartIndex, setThumbnailStartIndex] = useState(0)
  const thumbnailsPerPage = 5 // 每页显示的缩略图数量
  const thumbnailsRef = useRef<HTMLDivElement>(null)
  
  // 图片预加载缓存
  const imageCache = useRef<Set<string>>(new Set())
  
  // 预加载图片函数
  const preloadImage = (src: string) => {
    if (imageCache.current.has(src)) return
    
    const img = new Image()
    img.onload = () => {
      imageCache.current.add(src)
    }
    img.src = src
  }

  // Cleanup if someone enters the page with #gallery
  useEffect(() => {
    if (!prevRoute?.pathname && zoomed) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      router.replace(router.asPath.replace(route, ''))
    }
  }, [prevRoute?.pathname, route, router, zoomed])

  const toggle = () => {
    if (disableZoom) {
      return
    }
    if (!zoomed) {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      router.push(route, undefined, { shallow: true })
      // 移除页面滚动，避免影响用户体验
    } else {
      router.back()
    }
  }

  const classes = withState({ zoomed, disableZoom, sticky: !disableSticky, variantMd })
  const theme = useTheme()
  const windowRef = useRef(typeof window !== 'undefined' ? window : null)

  const handleEscapeKey = (e: KeyboardEvent | Event) => {
    if (zoomed && (e as KeyboardEvent)?.key === 'Escape') toggle()
  }

  const onMouseDownScroller: React.MouseEventHandler<HTMLDivElement> = (e) => {
    // 防止任何可能的滚动行为
    e.preventDefault()
    e.stopPropagation()
  }

  const onMouseUpScroller: React.MouseEventHandler<HTMLDivElement> = (e) => {
    // 完全移除所有可能的滚动触发逻辑
    e.preventDefault()
    e.stopPropagation()
  }

  // 图片切换函数
  const switchToImage = (index: number) => {
    if (index < 0 || index >= images.length || index === currentIndex) return
    
    // 保存当前页面滚动位置
    const currentScrollY = window.pageYOffset
    
    setCurrentIndex(index)
    
    // 切换图片
    if (scrollerRef.current) {
      const itemWidth = scrollerRef.current.clientWidth
      const newScrollLeft = itemWidth * index
      scrollerRef.current.scrollLeft = newScrollLeft
    }
    
    // 确保页面滚动位置不变
    setTimeout(() => {
      if (window.pageYOffset !== currentScrollY) {
        window.scrollTo(0, currentScrollY)
      }
    }, 0)
  }

  // 监听滚动事件以同步当前索引
  useEffect(() => {
    const scrollerElement = scrollerRef.current
    if (!scrollerElement) return

    const handleScroll = () => {
      const scrollLeft = scrollerElement.scrollLeft
      const itemWidth = scrollerElement.clientWidth
      const newIndex = Math.round(scrollLeft / itemWidth)
      if (newIndex !== currentIndex && newIndex >= 0 && newIndex < images.length) {
        setCurrentIndex(newIndex)
      }
    }

    scrollerElement.addEventListener('scroll', handleScroll, { passive: true })
    return () => scrollerElement.removeEventListener('scroll', handleScroll)
  }, [currentIndex, images.length])

  // 缩略图箭头切换逻辑 - 实际是切换主图
  const switchThumbnailImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      switchToImage(currentIndex - 1) // 切换到上一张图片
    } else {
      switchToImage(currentIndex + 1) // 切换到下一张图片
    }
  }

  // 当当前图片索引变化时，自动调整缩略图显示范围
  useEffect(() => {
    const endIndex = thumbnailStartIndex + thumbnailsPerPage - 1
    
    // 如果当前图片不在显示范围内，调整缩略图显示范围
    if (currentIndex < thumbnailStartIndex) {
      setThumbnailStartIndex(Math.max(0, currentIndex - Math.floor(thumbnailsPerPage / 2)))
    } else if (currentIndex > endIndex) {
      const newStartIndex = Math.min(
        Math.max(0, images.length - thumbnailsPerPage),
        currentIndex - Math.floor(thumbnailsPerPage / 2)
      )
      setThumbnailStartIndex(newStartIndex)
    }
  }, [currentIndex, thumbnailStartIndex, thumbnailsPerPage, images.length])

  // 获取当前页显示的缩略图
  const visibleThumbnails = images.slice(thumbnailStartIndex, thumbnailStartIndex + thumbnailsPerPage)
  
  // 预加载当前可见缩略图和相邻的图片
  useEffect(() => {
    // 预加载当前页的所有缩略图
    visibleThumbnails.forEach((image) => {
      const src = typeof image.src === 'string' ? image.src : (image.src as any)?.src
      if (src) preloadImage(src)
    })
    
    // 预加载下一页的第一张图片（如果存在）
    if (thumbnailStartIndex + thumbnailsPerPage < images.length) {
      const nextImage = images[thumbnailStartIndex + thumbnailsPerPage]
      if (nextImage) {
        const src = typeof nextImage.src === 'string' ? nextImage.src : (nextImage.src as any)?.src
        if (src) preloadImage(src)
      }
    }
    
    // 预加载上一页的最后一张图片（如果存在）
    if (thumbnailStartIndex > 0) {
      const prevImage = images[thumbnailStartIndex - 1]
      if (prevImage) {
        const src = typeof prevImage.src === 'string' ? prevImage.src : (prevImage.src as any)?.src
        if (src) preloadImage(src)
      }
    }
  }, [thumbnailStartIndex, visibleThumbnails, images])

  useDomEvent(windowRef, 'keyup', handleEscapeKey, { passive: true })

  const headerHeight = `${theme.appShell.headerHeightSm} - ${theme.spacings.sm} * 2`
  const galleryMargin = theme.spacings.lg

  const maxHeight = `calc(100vh - ${headerHeight} - ${galleryMargin})`
  const ratio = `calc(${height} / ${width} * 100%)`

  const hasImages = images.length > 0

  return (
    <ScrollerProvider scrollSnapAlign='center'>
      <Row
        maxWidth={zoomed ? 'full' : 'lg'}
        disableGutters
        className={classes.row}
        breakoutLeft={variantMd === 'default' && !theme.appShell.containerSizingContent}
        sx={[
          { 
            bgcolor: theme.palette.mode === 'light' ? 'background.image' : 'background.paper',
            // Fix: Ensure proper containment when zoomed
            ...(zoomed && {
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh',
              zIndex: theme.zIndex.modal,
              overflow: 'hidden',
            })
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        <MotionBox
          className={classes.root}
          sx={[
            {
              willChange: 'transform',
              display: 'grid',
              gridTemplate: '"left" "right"',
              width: '100%',
              height: zoomed ? '100vh' : 'auto',
              [theme.breakpoints.up('md')]: {
                '&:not(.variantMdOneColumn)': {
                  gridTemplate: zoomed 
                    ? '"left right" / 1fr 0fr'  // Hide sidebar when zoomed
                    : `"left right" / 1fr calc(${responsiveVal(300, 500, undefined, theme.breakpoints.values.lg)} + ${theme.page.horizontal} * 2)`,
                },
              },
            },
          ]}
        >
          <TrapFocus open={zoomed}>
            <Box
              sx={{
                gridArea: 'left',
                display: 'flex',
                flexDirection: 'column',
                gap: zoomed ? theme.spacings.sm : theme.spacings.xs,
              }}
            >
              <MotionBox
                className={classes.scrollerContainer}
                sx={[
                  {
                    willChange: 'transform',
                    backgroundColor: theme.palette.background.image,
                    position: 'relative',
                    ...(zoomed 
                      ? {
                          width: '100vw',
                          height: 'calc(100vh - 80px)', // 留出缩略图空间
                          paddingTop: 0,
                        }
                      : {
                          // 固定高度，避免图片切换时的布局跳动
                          height: {
                            xs: '60vh', // 移动端固定高度
                            sm: '65vh', // 小屏幕
                            md: '70vh', // 中等屏幕
                            lg: '75vh', // 大屏幕
                          },
                          minHeight: '400px', // 最小高度保证
                          maxHeight: '800px', // 最大高度限制
                          paddingTop: 0, // 移除padding-top，使用固定高度
                          // 完全移除 sticky 定位，避免任何滚动行为
                          [theme.breakpoints.up('md')]: {
                            // 移除所有 sticky 相关的 CSS
                          },
                        }
                    ),
                  },
                ]}
                onLayoutAnimationComplete={() => {
                  if (!zoomed) document.body.style.overflow = ''
                }}
              >
                {beforeScroller}
                <Scroller
                  ref={scrollerRef}
                  className={`${classes.scroller} CustomSidebarGallery-scroller`}
                  hideScrollbar
                  onMouseDown={onMouseDownScroller}
                  onMouseUp={onMouseUpScroller}
                  sx={[
                    {
                      willChange: 'transform',
                      position: 'absolute',
                      top: 0,
                      width: '100%',
                      height: '100%',
                      gridAutoColumns: '100%',
                      gridTemplateRows: '100%',
                      cursor: disableZoom ? 'auto' : 'zoom-in',
                      // 禁用浏览器的平滑滚动，避免触发页面滚动
                      scrollBehavior: 'auto',
                    },
                    zoomed && {
                      cursor: 'zoom-out',
                    },
                  ]}
                >
                  {images.map((image, idx) => (
                    <MotionImageAspect
                      key={typeof image.src === 'string' ? image.src : idx}
                      src={image.src}
                      width={image.width}
                      height={image.height}
                      loading={idx === 0 ? 'eager' : 'lazy'}
                      sx={{ 
                        display: 'block', 
                        objectFit: zoomed ? 'contain' : 'contain', // 使用contain确保图片完整显示
                        width: '100%',
                        height: '100%',
                      }}
                      sizes={{
                        0: '100vw',
                        [theme.breakpoints.values.md]: zoomed ? '100vw' : '60vw',
                      }}
                      alt={image.alt || `Product Image ${idx}` || undefined}
                      dontReportWronglySizedImages
                    />
                  ))}
                </Scroller>
                {afterScroller}
                
                {/* Zoom Toggle Button */}
                <MotionBox
                  className={classes.topRight}
                  sx={{
                    display: 'grid',
                    gridAutoFlow: 'column',
                    top: theme.spacings.sm,
                    gap: theme.spacings.xxs,
                    position: 'absolute',
                    right: theme.spacings.sm,
                    zIndex: 1,
                  }}
                >
                  {!disableZoom && (
                    <Fab
                      size='small'
                      className={classes.toggleIcon}
                      disabled={!hasImages}
                      onClick={toggle}
                      aria-label='Toggle Fullscreen'
                      sx={{ boxShadow: 6 }}
                    >
                      {!zoomed ? (
                        <IconSvg src={iconFullscreen} />
                      ) : (
                        <IconSvg src={iconFullscreenExit} />
                      )}
                    </Fab>
                  )}
                </MotionBox>

                {/* Navigation Buttons - Always show when there are multiple images */}
                {images.length > 1 && (
                  <>
                    <Box
                      className={classes.centerLeft}
                      sx={{
                        display: 'grid',
                        gridAutoFlow: 'row',
                        gap: theme.spacings.xxs,
                        position: 'absolute',
                        left: theme.spacings.sm,
                        top: 'calc(50% - 28px)',
                        zIndex: 1,
                      }}
                    >
                      <IconButton
                        size='small'
                        className={classes.sliderButtons}
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          switchToImage(currentIndex - 1)
                        }}
                        disabled={currentIndex === 0}
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 1)',
                          },
                          '&:disabled': {
                            backgroundColor: 'rgba(255, 255, 255, 0.5)',
                          },
                        }}
                      >
                        <IconSvg src={iconChevronLeft} />
                      </IconButton>
                    </Box>
                    <Box
                      className={classes.centerRight}
                      sx={{
                        display: 'grid',
                        gap: theme.spacings.xxs,
                        position: 'absolute',
                        right: theme.spacings.sm,
                        top: 'calc(50% - 28px)',
                        zIndex: 1,
                      }}
                    >
                      <IconButton
                        size='small'
                        className={classes.sliderButtons}
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          switchToImage(currentIndex + 1)
                        }}
                        disabled={currentIndex === images.length - 1}
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.9)',
                          '&:hover': {
                            backgroundColor: 'rgba(255, 255, 255, 1)',
                          },
                          '&:disabled': {
                            backgroundColor: 'rgba(255, 255, 255, 0.5)',
                          },
                        }}
                      >
                        <IconSvg src={iconChevronRight} />
                      </IconButton>
                    </Box>
                  </>
                )}
              </MotionBox>
             
              {/* Custom Thumbnails with Navigation */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: {
                    xs: theme.spacings.xs, // 移动端减少padding
                    sm: theme.spacings.sm,
                  },
                  gap: {
                    xs: theme.spacings.xxs, // 移动端减少间距
                    sm: theme.spacings.xs,
                  },
                  // 确保容器不会超出屏幕宽度
                  maxWidth: '100%',
                  width: '100%',
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                  ...(zoomed && {
                    position: 'fixed',
                    bottom: theme.spacings.sm,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    zIndex: theme.zIndex.modal + 1,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    borderRadius: theme.shape.borderRadius,
                    maxWidth: '95vw', // 确保不超出视口宽度
                  }),
                }}
              >
                {/* Left Arrow for Thumbnails */}
                {images.length > thumbnailsPerPage && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      switchThumbnailImage('prev')
                    }}
                    disabled={currentIndex === 0}
                    sx={{
                      backgroundColor: zoomed ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.1)',
                      color: zoomed ? 'black' : 'inherit',
                      minWidth: {
                        xs: 32, // 移动端减小按钮尺寸
                        sm: 40,
                      },
                      width: {
                        xs: 32,
                        sm: 40,
                      },
                      height: {
                        xs: 32,
                        sm: 40,
                      },
                      '&:hover': {
                        backgroundColor: zoomed ? 'rgba(255, 255, 255, 1)' : 'rgba(0, 0, 0, 0.2)',
                      },
                      '&:disabled': {
                        backgroundColor: zoomed ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.05)',
                        color: zoomed ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                      },
                    }}
                  >
                    <IconSvg src={iconChevronLeft} size="small" />
                  </IconButton>
                )}

                {/* Thumbnails Container */}
                <Box
                  ref={thumbnailsRef}
                  sx={{
                    display: 'flex',
                    gap: {
                      xs: theme.spacings.xxs / 2, // 移动端进一步减少间距
                      sm: theme.spacings.xxs,
                    },
                    overflow: 'hidden',
                    // 响应式最大宽度，确保不会溢出
                    maxWidth: {
                      xs: zoomed ? 'calc(95vw - 80px)' : 'calc(100vw - 120px)', // 移动端：减去按钮和边距
                      sm: zoomed ? 'calc(95vw - 100px)' : 'calc(100vw - 140px)', // 小屏幕
                      md: zoomed ? '500px' : '450px', // 中等屏幕及以上保持原有尺寸
                    },
                    flex: 1, // 允许容器弹性伸缩
                  }}
                >
                  {visibleThumbnails.map((image, idx) => {
                    const actualIndex = thumbnailStartIndex + idx
                    return (
                      <Box
                        key={typeof image.src === 'string' ? image.src : actualIndex}
                        component="button"
                        type="button"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          switchToImage(actualIndex)
                        }}
                        sx={{
                          // 响应式缩略图尺寸
                          width: {
                            xs: zoomed ? 60 : 50, // 移动端缩小尺寸
                            sm: zoomed ? 70 : 60, // 小屏幕
                            md: zoomed ? 80 : 70, // 中等屏幕及以上保持原有尺寸
                          },
                          height: {
                            xs: zoomed ? 60 : 50,
                            sm: zoomed ? 70 : 60,
                            md: zoomed ? 80 : 70,
                          },
                          minWidth: {
                            xs: zoomed ? 60 : 50,
                            sm: zoomed ? 70 : 60,
                            md: zoomed ? 80 : 70,
                          },
                          borderRadius: theme.shape.borderRadius,
                          border: `2px solid ${actualIndex === currentIndex ? theme.palette.primary.main : 'transparent'}`,
                          transition: 'all 0.2s ease',
                          cursor: 'pointer',
                          padding: 0,
                          backgroundColor: 'transparent',
                          overflow: 'hidden',
                          position: 'relative',
                          '&:hover': {
                            borderColor: theme.palette.primary.light,
                            transform: {
                              xs: 'scale(1.02)', // 移动端减小hover效果
                              sm: 'scale(1.05)',
                            },
                          },
                        }}
                      >
                        <Box
                          component="img"
                          src={typeof image.src === 'string' ? image.src : (image.src as any)?.src || ''}
                          alt={image.alt || `Thumbnail ${actualIndex + 1}`}
                          loading="eager" // 改为eager加载，提升切换体验
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            display: 'block',
                          }}
                        />
                      </Box>
                    )
                  })}
                </Box>

                {/* Right Arrow for Thumbnails */}
                {images.length > thumbnailsPerPage && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      switchThumbnailImage('next')
                    }}
                    disabled={currentIndex === images.length - 1}
                    sx={{
                      backgroundColor: zoomed ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.1)',
                      color: zoomed ? 'black' : 'inherit',
                      minWidth: {
                        xs: 32, // 移动端减小按钮尺寸
                        sm: 40,
                      },
                      width: {
                        xs: 32,
                        sm: 40,
                      },
                      height: {
                        xs: 32,
                        sm: 40,
                      },
                      '&:hover': {
                        backgroundColor: zoomed ? 'rgba(255, 255, 255, 1)' : 'rgba(0, 0, 0, 0.2)',
                      },
                      '&:disabled': {
                        backgroundColor: zoomed ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.05)',
                        color: zoomed ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                      },
                    }}
                  >
                    <IconSvg src={iconChevronRight} size="small" />
                  </IconButton>
                )}


              </Box>
            </Box>
          </TrapFocus>
          
          {/* Sidebar */}
          <Box
            className={classes.sidebarWrapper}
            sx={[
              {
                gridArea: 'right',
                boxSizing: 'content-box',
                display: 'grid',
                justifyItems: 'start',
                alignContent: 'start',
                position: 'relative',
                // Hide sidebar when zoomed
                ...(zoomed && {
                  display: 'none',
                }),
              },
            ]}
          >
            <MotionBox
              className={classes.sidebar}
              sx={{
                boxSizing: 'border-box',
                width: '100%',
                '&:not(.variantMdOneColumn)': {
                  padding: `${theme.spacings.sm} ${theme.page.horizontal}`,
                  [theme.breakpoints.up('md')]: {
                    paddingLeft: theme.spacings.sm,
                  },
                },
              }}
            >
              {sidebar}
            </MotionBox>
          </Box>
        </MotionBox>
      </Row>
    </ScrollerProvider>
  )
}

CustomSidebarGallery.selectors = selectors 