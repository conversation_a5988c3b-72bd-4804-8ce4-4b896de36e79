import { useScrollOffset } from '@graphcommerce/framer-next-pages'
import { dvh } from '@graphcommerce/framer-utils'
import {
  Container,
  extendableComponent,
  LayoutProvider,
  SkipLink,
  useFabSize,
} from '@graphcommerce/next-ui'
import { Box, SxProps, Theme, Fab } from '@mui/material'
import { useScroll, useTransform } from 'framer-motion'
import config from '../../graphcommerce.config'
import { useMemo, useState, useEffect } from 'react'
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp'

export type LayoutDefaultProps = {
  className?: string
  beforeHeader?: React.ReactNode
  header?: React.ReactNode  // Keep compatibility
  headerTop?: React.ReactNode  // New: top header
  headerBottom?: React.ReactNode  // New: bottom header
  footer: React.ReactNode
  menuFab?: React.ReactNode
  cartFab?: React.ReactNode
  children?: React.ReactNode
  noSticky?: boolean
  sx?: SxProps<Theme>
} & OwnerState

type OwnerState = {
  noSticky?: boolean
}
const parts = ['root', 'fabs', 'header', 'headerTop', 'headerBottom', 'children', 'footer'] as const
const { withState } = extendableComponent<OwnerState, 'LayoutDefault', typeof parts>(
  'LayoutDefault',
  parts,
)

export function LayoutDefault(props: LayoutDefaultProps) {
  const {
    children,
    header,
    headerTop,
    headerBottom,
    beforeHeader,
    footer,
    menuFab,
    cartFab,
    noSticky,
    className,
    sx = [],
  } = props

  const { scrollY } = useScroll()
  const scrollYOffset = useTransform(
    [scrollY, useScrollOffset()],
    ([y, offset]: number[]) => y + offset,
  )

  const [showBackToTop, setShowBackToTop] = useState(false)

  useEffect(() => {
    const unsubscribe = scrollY.onChange((y) => {
      setShowBackToTop(y > 100)
    })
    return unsubscribe
  }, [scrollY])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }

  const classes = withState({ noSticky })
  const fabIconSize = useFabSize('responsive')

  const actualHeaderTop = useMemo(() => {
    // Compatibility handling: if only header is passed, display it in headerTop
    if (headerTop) return headerTop
    if (header) return header
    return null
  }, [header, headerTop])

  const topContent = actualHeaderTop
  const bottomContent = headerBottom

  return (
    <Box
      className={`${classes.root} ${className ?? ''}`}
      sx={[
        (theme) => ({
          minHeight: dvh(100),
          '@supports (-webkit-touch-callout: none)': {
            minHeight: '-webkit-fill-available',
          },
          display: 'grid',
          gridTemplateRows: { xs: 'auto 1fr auto', md: 'auto auto 1fr auto' },
          gridTemplateColumns: '100%',
          background: theme.palette.background.default,
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      <SkipLink />
      <LayoutProvider scroll={scrollYOffset}>
        {beforeHeader}
        
        {/* Top Header */}
        <Container
          maxWidth={false}
          component='header'
          className={`${classes.headerTop}${!noSticky ? ' sticky' : ''}`}
          sx={(theme) => ({
            zIndex: theme.zIndex.appBar - 1,
            display: 'flex',
            alignItems: 'center',
            // justifyContent: 'center',
            height: theme.appShell.headerHeightSm,
            pointerEvents: 'none',
            '& > *': {
              pointerEvents: 'all',
            },
            [theme.breakpoints.up('md')]: {
              height: theme.appShell.headerHeightMd,
              top: 0,
              display: 'flex',
              // justifyContent: 'center',
              width: '100%',
            },
            '&.sticky': {
              position: 'sticky',
              top: 0,
              // borderBottom: bottomContent ? 'none' : `1px solid ${theme.palette.divider}`,
              transition: theme.transitions.create(['box-shadow', 'background-color'], {
                duration: theme.transitions.duration.short,
              }),
              // boxShadow: scrollY.get() > 0 ? theme.shadows[2] : 'none',
              backgroundColor: '#fff',
            },
          })}
        >
          {topContent}
        </Container>

        {/* Bottom Header (Menu Bar) */}
        {bottomContent && (
          <Container
            maxWidth={false}
            component='nav'
            className={`${classes.headerBottom}${!noSticky ? ' sticky' : ''}`}
            sx={(theme) => ({
              paddingTop: '15px',
              zIndex: theme.zIndex.appBar - 2,
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              justifyContent: 'left',
              height: theme.appShell.headerHeightSm,
              pointerEvents: 'none',
              '& > *': {
                pointerEvents: 'all',
              },
              [theme.breakpoints.up('md')]: {
                height: theme.appShell.headerHeightMd,
                top: 0,
                display: 'flex',
                justifyContent: 'left',
                width: '100%',
              },
              '&.sticky': {
                // Styles when menu bar is fixed
                position: 'sticky', // Fixed positioning
                top: theme.appShell.headerHeightMd, // Distance from top is header height
                backgroundColor: theme.palette.background.paper, // Background color is paper color
                borderBottom: `1px solid ${theme.palette.divider}`, // Bottom border
                transition: theme.transitions.create(['box-shadow', 'background-color'], {
                  duration: theme.transitions.duration.short, // Transition animation
                }),
                // boxShadow: scrollY.get() > 150 ? theme.shadows[1] : 'none', // Show shadow when scrolled over 150px
                backdropFilter: 'blur(8px)' // Background blur effect
              },
            })}
          >
            {bottomContent}
          </Container>
        )}

        {/* FABs positioning adjustment */}
        {menuFab || cartFab ? (
          <Container
            maxWidth={false}
            className={classes.fabs}
            sx={(theme) => ({
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
              height: 0,
              zIndex: 'speedDial',
              [theme.breakpoints.up('sm')]: {
                position: 'sticky',
                marginTop: bottomContent 
                  ? `calc((${theme.appShell.appBarInnerHeightMd} * 2) * -1 - calc(${fabIconSize} / 2))`
                  : `calc(${theme.appShell.headerHeightMd} * -1 - calc(${fabIconSize} / 2))`,
                top: bottomContent
                  ? `calc(${theme.appShell.appBarInnerHeightMd} + (${theme.appShell.appBarInnerHeightMd} / 2) - (${fabIconSize} / 2))`
                  : `calc(${theme.appShell.headerHeightMd} / 2 - (${fabIconSize} / 2))`,
              },
              [theme.breakpoints.down('md')]: {
                position: 'fixed',
                top: 'unset',
                bottom: `calc(20px + ${fabIconSize})`,
                padding: '0 20px',
                '@media (max-height: 530px) and (orientation: portrait)': {
                  display: 'none',
                },
              },
            })}
          >
            {menuFab}
            {cartFab && (
              <Box
                sx={(theme) => ({
                  display: 'flex',
                  flexDirection: 'row-reverse',
                  gap: theme.spacings.sm,
                  [theme.breakpoints.up('md')]: {
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                  },
                })}
              >
                {cartFab}
              </Box>
            )}
          </Container>
        ) : (
          <div />
        )}
        <div className={classes.children}>
          <div id='skip-nav' tabIndex={-1} />
          {children}
        </div>
        <div className={classes.footer}>{footer}</div>
      </LayoutProvider>
      {showBackToTop && (
        <Fab
          color='primary'
          size='small'
          aria-label='scroll back to top'
          onClick={scrollToTop}
          sx={{
            display: { xs: 'none', md: 'flex' },
            position: 'fixed',
            bottom: 120,
            right: 32,
            zIndex: (theme) => theme.zIndex.speedDial,
          }}
        >
          <KeyboardArrowUpIcon />
        </Fab>
      )}
    </Box>
  )
} 