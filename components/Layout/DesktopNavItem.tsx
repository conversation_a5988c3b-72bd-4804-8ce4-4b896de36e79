import type { LinkProps } from '@mui/material'
import { Box, Link } from '@mui/material'
import { useRouter } from 'next/router'

export type DesktopNavItemLinkProps = LinkProps<'a'> & {
  active?: boolean
}

export type DesktopNavItemButtonProps = LinkProps<'div'> & {
  onClick: LinkProps<'button'>['onClick']
  active?: boolean
}

function isLinkProps(
  props: DesktopNavItemLinkProps | DesktopNavItemButtonProps,
): props is DesktopNavItemLinkProps {
  return 'href' in props
}

export function DesktopNavItem(props: DesktopNavItemLinkProps | DesktopNavItemButtonProps) {
  const router = useRouter()

  if (!isLinkProps(props)) {
    const { onClick, children, sx = [], active, ...linkProps } = props

    return (
      <Link
        component='div'
        color='text.primary'
        underline='none'
        {...linkProps}
        onClick={onClick}
        sx={[
          { 
            whiteSpace: 'nowrap',
            fontSize: '1rem',
            fontWeight: 500,
            color: 'text.primary',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            transition: 'color 0.2s ease',
            '&:hover': {
              color: 'primary.main',
            },
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        {children}22
      </Link>
    )
  }

  const { href, children, sx = [], active, ...linkProps } = props
  const activeValue =
    typeof active === 'undefined' ? router.asPath.startsWith((href ?? '').toString()) : active

  return (
    <Link
      href={href}
      color='text.primary'
      underline='none'
      {...linkProps}
      sx={[
        { 
          // color: activeValue ? 'primary.main' : 'text.secondary',
          // underline: activeValue ? 'underline' : 'none',
          display: 'flex',
          alignItems: 'center'
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      {children}
      {activeValue && (
        <Box
          component='span'
          sx={{
            maxWidth: 40,
            width: '100%',
            height: 2,
            background: (theme) => theme.palette.primary.main,
            margin: '0 auto',
            marginTop: '6px',
          }}
        />
      )}
    </Link>
  )
} 