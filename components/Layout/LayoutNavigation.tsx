import { CartFab, useCartEnabled } from '@graphcommerce/magento-cart'
import { CustomerFab } from '@graphcommerce/magento-customer'
import { SearchFab } from '@graphcommerce/magento-search'
import { WishlistFab } from '@graphcommerce/magento-wishlist'
import {
  DesktopNavActions,
  LayoutDefaultProps,
  iconCustomerService,
  iconHeart,
  iconPerson,
  IconSvg,
  iconChevronDown,
  iconMenu,
  iconClose,
  MobileTopRight,
} from '@graphcommerce/next-ui'
import { i18n } from '@lingui/core'
import { Trans } from '@lingui/react'
import { 
  Divider, 
  Fab, 
  Menu, 
  MenuItem, 
  Button, 
  Box, 
  List, 
  ListItem,
  Collapse,
  ListItemButton,
  ListItemText,
  useMediaQuery,
  useTheme,
  Drawer,
  IconButton,
  Popover,
  Grid,
  Link,
} from '@mui/material'
import { useRouter } from 'next/router'
import React, { useState, useMemo, useRef } from 'react'
import { Footer } from './Footer'
import { LayoutQuery } from './Layout.gql'
import { LayoutDefault } from './LayoutDefault'
import { Logo } from './Logo'
import { productListRenderer } from '../ProductListItems/productListRenderer'
import { DesktopNavItem } from './DesktopNavItem'
import { SearchField } from '../CustomSearch/SearchField'
import { DesktopCartIcon } from './DesktopCartIcon'
import graphcommerceConfig from '../../graphcommerce.config'

export type LayoutNavigationProps = LayoutQuery &
  Omit<LayoutDefaultProps, 'footer' | 'header' | 'headerTop' | 'headerBottom' | 'cartFab' | 'menuFab'>

// Menu item interface
interface MenuItemData {
  uid: string
  name: string
  url_path: string
  include_in_menu: boolean
  position?: number // Add position field for sorting
  menu_image?: string // Add menu image field
  menu_name?: string // Add menu name field
  children?: MenuItemData[]
  level?: number // Add level marker
  childrenCount?: number // Add children count marker
}

// Recursively calculate the number of child menu items
const calculateChildrenCount = (item: MenuItemData): number => {
  if (!item.children || item.children.length === 0) {
    return 0
  }
  
  let count = item.children.length
  for (const child of item.children) {
    count += calculateChildrenCount(child)
  }
  return count
}

// Filter and process menu items - add level and children count information
const filterMenuItems = (items: any[], currentLevel: number = 1): MenuItemData[] => {
  if (!items) return []
 
  return items
    .filter(item => {
      const includeInMenu = item?.include_in_menu
      return includeInMenu === true || includeInMenu === 1 || includeInMenu === '1'
    })
    .map(item => {
      // Process child menus
      const children = item.children ? filterMenuItems(item.children, currentLevel + 1) : undefined
      // console.log("item", item)
      // Create menu item
      const menuItem: MenuItemData = {
        uid: item.uid,
        name: item.name,
        url_path: item.url_path,
        include_in_menu: item.include_in_menu,
        position: item.position,
        menu_image: item.menu_image,
        menu_name: item.menu_name || item.name || '',
        children,
        level: currentLevel,
      }
      
      // Calculate children count
      menuItem.childrenCount = calculateChildrenCount(menuItem)
   
      return menuItem
    })
}

// Process menu structure: skip first level, use second level as main menu, and sort
const processMenuStructure = (items: any[]): MenuItemData[] => {
  if (!items || items.length === 0) return []
  
  // Get menu items
  let menuItems = items.length === 1 && items[0].children 
    ? filterMenuItems(items[0].children)
    : filterMenuItems(items)
    
  // Debug output
  // console.log('Menu structure before processing:', JSON.stringify(menuItems, null, 2))
  
  // Recursive sorting function
  const sortMenuItems = (items: MenuItemData[]) => {
    // Sort current level by position field
    items.sort((a, b) => {
      const positionA = (a as any).position || 0
      const positionB = (b as any).position || 0
      
      // Primary sort: by position (ascending)
      if (positionA !== positionB) {
        return positionA - positionB
      }
      
      // Secondary sort: by name (alphabetical) if positions are equal
      return (a.name || '').localeCompare(b.name || '')
    })
    
    // Recursively sort child menus
    items.forEach(item => {
      if (item.children && item.children.length > 0) {
        sortMenuItems(item.children)
      }
    })
    
    return items
  }
  
  // Sort menus
  menuItems = sortMenuItems(menuItems)
  
  // Debug output
  // console.log('Menu structure after processing:', JSON.stringify(menuItems, null, 2))
  
  return menuItems
}

// Desktop Mega Menu Component
function DesktopMegaMenu({
  item,
  isOpen,
  onEnter,
  onLeave,
  onClose,
}: {
  item: MenuItemData
  isOpen: boolean
  onEnter: () => void
  onLeave: () => void
  onClose: () => void
}) {
  const navItemRef = useRef<HTMLDivElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)
  const hasChildren = item.children && item.children.length > 0
  const router = useRouter()
  const menuTimeout = useRef<number>()
  const isMenuTransitioning = useRef(false)

  const handleNavItemMouseEnter = () => {
    if (hasChildren) {
      onEnter()
    }
  }

  const handleNavItemMouseLeave = (e: React.MouseEvent) => {
    // Check if mouse is moving towards Popover
    const popoverElement = popoverRef.current?.querySelector('.MuiPopover-paper')
    if (popoverElement) {
      const rect = popoverElement.getBoundingClientRect()
      if (
        e.clientX >= rect.left &&
        e.clientX <= rect.right &&
        e.clientY >= rect.top - 10 && // Add some tolerance
        e.clientY <= rect.bottom
      ) {
        return // If mouse moves towards Popover, don't trigger close
      }
    }
    onLeave()
  }

  const handlePopoverMouseEnter = () => {
    if (menuTimeout.current) {
      clearTimeout(menuTimeout.current)
    }
  }

  const handlePopoverMouseLeave = (e: React.MouseEvent) => {
    // Check if mouse is moving towards navigation item
    const navItemElement = navItemRef.current
    if (navItemElement) {
      const rect = navItemElement.getBoundingClientRect()
      if (
        e.clientX >= rect.left &&
        e.clientX <= rect.right &&
        e.clientY >= rect.top &&
        e.clientY <= rect.bottom
      ) {
        return // If mouse moves towards navigation item, don't trigger close
      }
    }
    onLeave()
  }

  // Handle click event
  const handleClick = (e: React.MouseEvent) => {
    // Always allow navigation to the main menu item's category page
    // Don't prevent default behavior, let the link work normally
    onClose() // Close any open mega menu
    router.push(`/${item.url_path}`)
  }

  return (
    <>
      <Box
        ref={navItemRef}
        onMouseEnter={handleNavItemMouseEnter}
        onMouseLeave={handleNavItemMouseLeave}
        sx={{
          display: 'flex',
          // alignItems: 'center',
          height: '40px',
          // justifyContent: 'center',
          position: 'relative',
        }}
      >
        <DesktopNavItem
          href={`/${item.url_path}`}
          onClick={handleClick}
          sx={{
            cursor: 'pointer',
            display: 'flex',
            // alignItems: 'center',
            gap: 2.25,
            px: 2.5,
            transition: 'all 0.2s ease',
            '&:hover': {
              color: 'primary.main',
              backgroundColor: 'transparent',
            },
            textTransform: 'none',
            whiteSpace: 'nowrap',
            color: 'text.primary',
            letterSpacing: '0.01em',
            fontSize: '1.12rem',
            fontWeight: 450,
            lineHeight: 1.5,
            fontFamily: (theme) => theme.typography.fontFamily,
          }}
        >
          {item.menu_name}
        </DesktopNavItem>
      </Box>

      {hasChildren && (
        <Popover
          open={isOpen}
          anchorEl={navItemRef.current}
          onClose={onClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          sx={{
            pointerEvents: 'none',
            '& .MuiPopover-paper': {
              pointerEvents: 'auto',
              mt: 0.5,
              borderRadius: 1,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              border: '1px solid',
              borderColor: 'divider',
              minWidth: '100%',
              maxWidth: '100vw',
              transform: 'none !important',
              maxHeight: 'calc(100vh - 200px)',
              overflowY: 'auto'
            }
          }}
          ref={popoverRef}
          onMouseEnter={handlePopoverMouseEnter}
          onMouseLeave={handlePopoverMouseLeave}
        >
          <Box 
            sx={{ 
              p: 4,
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: 3,
              width: '100%',
              maxWidth: 1200,
              mx: 'auto'
            }}
          >
            {item.children?.map((child) => (
              <Box key={child.uid} sx={{ mb: 0 }}>
                {child.menu_image && (
                  <Link
                    href={`/${child.url_path}`}
                    underline='none'
                    onClick={(e) => {
                      e.preventDefault()
                      onClose()
                      router.push(`/${child.url_path}`)
                    }}
                    sx={{ display: 'block', mb: 1.5 }}
                  >
                    <Box
                      component="img"
                      src={`${(graphcommerceConfig as any).imagecdnurl}${child.menu_image}`}
                      alt={child.name}
                      sx={{
                        width: '80%',
                        height: 'auto',
                        display: 'block',
                        borderRadius: '4px',
                      }}
                      loading="lazy"
                    />
                  </Link>
                )}
                <Link
                  href={`/${child.url_path}`}
                  underline='none'
                  onClick={(e) => {
                    e.preventDefault()
                    onClose()
                    router.push(`/${child.url_path}`)
                  }}
                  sx={{
                    display: 'block',
                    fontWeight: 600,
                    fontSize: '0.8rem',
                    color: 'text.primary',
                    transition: 'color 0.2s ease',
                    '&:hover': {
                      color: 'primary.main',
                    },
                    cursor: 'pointer',
                  }}
                >
                  {child.name}
                </Link>
                {child.children && child.children.length > 0 && (
                  <List disablePadding>
                    {child.children.map((grandChild) => (
                      <ListItem key={grandChild.uid} disablePadding sx={{ py: 0.5 }}>
                        <Link
                          component='a'
                          href={`/${grandChild.url_path}`}
                          underline='none'
                          onClick={(e) => {
                            e.preventDefault()
                            onClose()
                            router.push(`/${grandChild.url_path}`)
                          }}
                          sx={{
                            color: 'text.secondary',
                            py: 0.5,
                            fontSize: '0.85rem',
                            transition: 'color 0.2s ease',
                            '&:hover': {
                              color: 'primary.main',
                            },
                          }}
                        >
                          {grandChild.name}
                        </Link>
                      </ListItem>
                    ))}
                  </List>
                )}
              </Box>
            ))}
          </Box>
        </Popover>
      )}
    </>
  )
}

// Mobile menu component
function MobileMenuDrawer({ 
  open, 
  onClose, 
  menuItems 
}: { 
  open: boolean; 
  onClose: () => void; 
  menuItems: MenuItemData[] 
}) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const router = useRouter()

  const toggleExpanded = (uid: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(uid)) {
      newExpanded.delete(uid)
    } else {
      newExpanded.add(uid)
    }
    setExpandedItems(newExpanded)
  }

  const renderMenuItem = (item: MenuItemData, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.has(item.uid)

    return (
      <React.Fragment key={item.uid}>
        <ListItem disablePadding>
          {hasChildren && level === 0 ? (
            // For main menu items with children, create a split button layout
            <Box sx={{ display: 'flex', width: '100%', alignItems: 'center' }}>
              <ListItemButton
                onClick={() => {
                  onClose()
                  router.push(`/${item.url_path}`)
                }}
                sx={{
                  pl: 1.5 + level * 1.5,
                  py: level === 0 ? 1.2 : level === 1 ? 1 : 0.8,
                  minHeight: level === 0 ? 48 : level === 1 ? 40 : 36,
                  flex: 1,
                }}
              >
                <ListItemText 
                  primary={item.name}
                  primaryTypographyProps={{
                    fontSize: level === 0 ? '0.875rem' : level === 1 ? '0.8rem' : '0.75rem',
                    fontWeight: level === 0 ? 500 : level === 1 ? 400 : 400,
                    lineHeight: 1.2,
                  }}
                />
              </ListItemButton>
              <IconButton
                onClick={() => toggleExpanded(item.uid)}
                size="small"
                sx={{ mr: 1 }}
              >
                <IconSvg 
                  src={iconChevronDown} 
                  size='small'
                  sx={{ 
                    transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease'
                  }}
                />
              </IconButton>
            </Box>
          ) : (
            // For items without children or sub-level items
            <ListItemButton
              onClick={() => {
                if (hasChildren) {
                  toggleExpanded(item.uid)
                } else {
                  onClose()
                  router.push(`/${item.url_path}`)
                }
              }}
              sx={{
                pl: 1.5 + level * 1.5,
                py: level === 0 ? 1.2 : level === 1 ? 1 : 0.8,
                minHeight: level === 0 ? 48 : level === 1 ? 40 : 36,
              }}
            >
              <ListItemText 
                primary={item.name}
                primaryTypographyProps={{
                  fontSize: level === 0 ? '0.875rem' : level === 1 ? '0.8rem' : '0.75rem',
                  fontWeight: level === 0 ? 500 : level === 1 ? 400 : 400,
                  lineHeight: 1.2,
                }}
              />
              {hasChildren && (
                <IconSvg 
                  src={iconChevronDown} 
                  size='small'
                  sx={{ 
                    transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease'
                  }}
                />
              )}
            </ListItemButton>
          )}
        </ListItem>
        
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    )
  }

  return (
    <Drawer
      anchor="left"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '80vw',
          maxWidth: 320,
        }
      }}
    >
      <Box sx={{ p: 1.5, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ transform: 'scale(0.8)', transformOrigin: 'left center' }}>
            <Logo />
          </Box>
          <IconButton onClick={onClose} size="small">
            <IconSvg src={iconClose} size='small' />
          </IconButton>
        </Box>
      </Box>
      
      <List>
        {/* Home link */}
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              onClose()
              router.push('/')
            }}
            sx={{
              pl: 1.5,
              py: 1.2,
              minHeight: 44,
            }}
          >
            <ListItemText 
              primary="Home"
              primaryTypographyProps={{
                fontSize: '0.8rem',
                fontWeight: 500,
                lineHeight: 1.2,
              }}
            />
          </ListItemButton>
        </ListItem>

        {/* Main menu items */}
        {menuItems.map(item => renderMenuItem(item))}

        {/* Divider */}
        <Box sx={{ my: 1, mx: 2, borderBottom: '1px solid', borderColor: 'divider' }} />

        {/* Other menu items */}
        {/* <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              onClose()
              router.push('/blog')
            }}
            sx={{
              pl: 1.5,
              py: 1.2,
              minHeight: 44,
            }}
          >
            <ListItemText 
              primary="Blog"
              primaryTypographyProps={{
                fontSize: '0.8rem',
                fontWeight: 500,
                lineHeight: 1.2,
              }}
            />
          </ListItemButton>
        </ListItem> */}

        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              onClose()
              router.push('/account/signin')
            }}
            sx={{
              pl: 1.5,
              py: 1.2,
              minHeight: 44,
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
            }}
          >
            <IconSvg src={iconPerson} size='small' />
            <ListItemText 
              primary="Account"
              primaryTypographyProps={{
                fontSize: '0.8rem',
                fontWeight: 500,
                lineHeight: 1.2,
              }}
            />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              onClose()
              router.push('/service')
            }}
            sx={{
              pl: 1.5,
              py: 1.2,
              minHeight: 44,
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
            }}
          >
            <IconSvg src={iconCustomerService} size='small' />
            <ListItemText 
              primary="Customer Service"
              primaryTypographyProps={{
                fontSize: '0.8rem',
                fontWeight: 500,
                lineHeight: 1.2,
              }}
            />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              onClose()
              router.push('/wishlist')
            }}
            sx={{
              pl: 1.5,
              py: 1.2,
              minHeight: 44,
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
            }}
          >
            <IconSvg src={iconHeart} size='small' />
            <ListItemText 
              primary="Wishlist"
              primaryTypographyProps={{
                fontSize: '0.8rem',
                fontWeight: 500,
                lineHeight: 1.2,
              }}
            />
          </ListItemButton>
        </ListItem>
      </List>
    </Drawer>
  )
}

function DesktopMenuContainer({ menuItems }: { menuItems: MenuItemData[] }) {
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const menuTimeout = useRef<number>()

  const handleMenuEnter = (uid: string) => {
    if (menuTimeout.current) {
      clearTimeout(menuTimeout.current)
    }
    setActiveMenu(uid)
  }

  const handleMenuLeave = () => {
    menuTimeout.current = window.setTimeout(() => {
      setActiveMenu(null)
    }, 100)
  }

  const handleMenuClose = () => {
    if (menuTimeout.current) {
      clearTimeout(menuTimeout.current)
    }
    setActiveMenu(null)
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        height: '100%',
      }}
    >
      {menuItems.map((item) => (
        <DesktopMegaMenu
          key={item.uid}
          item={item}
          isOpen={activeMenu === item.uid && !!item.children?.length}
          onEnter={() => handleMenuEnter(item.uid)}
          onLeave={handleMenuLeave}
          onClose={handleMenuClose}
        />
      ))}
    </Box>
  )
}

export function LayoutNavigation(props: LayoutNavigationProps) {
  const { footer, menu, children, ...uiProps } = props
  const router = useRouter()
  const cartEnabled = useCartEnabled()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // Process menu data, use three layers: skip first layer, second layer as main menu
  const filteredMenuItems = useMemo(() => {
    if (!menu?.items) return []
    
    // Debug: output original menu data
    // console.log('Original menu data:', menu.items)
    // console.log('Menu level:', menu.items.length, 'First layer children:', menu.items[0]?.children?.length)
    
    const processed = processMenuStructure(menu.items)
    
    // Debug: output processed menu data
    // console.log('Processed menu data:', processed)
    
    // If processed menu is empty, fall back to displaying all second layer menu items (for debugging)
    if (processed.length === 0 && menu.items.length > 0 && menu.items[0]?.children) {
      // console.log('Processed menu is empty, fall back to displaying second layer menu')
      return menu.items[0].children.map((item: any) => ({
        uid: item.uid || '',
        name: item.name || '',
        url_path: item.url_path || '',
        include_in_menu: Boolean(item.include_in_menu),
        position: item.position || 0,
        menu_image: item.menu_image || '',
        menu_name: item.menu_name || item.name || '',
        children: item.children ? item.children.map((child: any) => ({
          uid: child.uid || '',
          name: child.name || '',
          url_path: child.url_path || '',
          include_in_menu: Boolean(child.include_in_menu),
          position: child.position || 0,
          menu_image: child.menu_image || '',
          menu_name: child.menu_name || child.name || '',
          children: child.children ? child.children.map((grandChild: any) => ({
            uid: grandChild.uid || '',
            name: grandChild.name || '',
            url_path: grandChild.url_path || '',
            include_in_menu: Boolean(grandChild.include_in_menu),
            position: grandChild.position || 0,
            menu_image: grandChild.menu_image || '',
            menu_name: grandChild.menu_name || grandChild.name || '',
          })) : undefined
        })) : undefined
      }))
    }
    
    return processed
  }, [menu?.items])

  return (
    <>

      {/* Mobile menu */}
      <MobileMenuDrawer
        open={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        menuItems={filteredMenuItems}
      />

      <LayoutDefault
        {...uiProps}
        noSticky={false}
        headerTop={
          <>
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              width: '100%',
              justifyContent: 'space-between',
              gap: 2,
              py: 1,
            }}>
              <Box sx={{ minWidth: { xs: '120px', md: '150px' } }}>
                <Logo />
              </Box>

              {/* Desktop search box - optimized layout */}
              <Box sx={{ 
                display: { xs: 'none', md: 'flex' }, 
                alignItems: 'center', 
                // justifyContent: 'center',
                flex: 1,
                maxWidth: { md: '500px', lg: '650px', xl: '750px' }, // Responsive maximum width
                mx: { md: 2, lg: 3 }, // Responsive margin
              }}>
                <SearchField
                  visible
                  formControl={{
                    sx: {
                      width: '100%',
                      '& .MuiInputBase-root': {
                        height: '46px', // Slightly increase height
                        borderRadius: '24px', // More rounded capsule shape
                        backgroundColor: '#f8f9fa', // Add background color
                        border: '1px solid #e9ecef',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // More fluid transition animation
                        '&:hover': {
                          backgroundColor: '#ffffff',
                          borderColor: '#007bff',
                          boxShadow: '0 4px 12px rgba(0,123,255,0.12)',
                          transform: 'translateY(-1px)', // Slight floating effect
                        },
                        '&.Mui-focused': {
                          backgroundColor: '#ffffff',
                          borderColor: '#007bff',
                          boxShadow: '0 6px 20px rgba(0,123,255,0.18)',
                          transform: 'translateY(-1px)',
                        },
                      },
                      '& .MuiOutlinedInput-input': {
                        fontSize: '0.95rem',
                        padding: '12px 16px 12px 8px', // Adjust left inner padding to accommodate search icon
                        fontWeight: 400,
                        '&::placeholder': {
                          color: '#6c757d',
                          opacity: 1,
                          fontWeight: 400,
                        },
                      },
                      '& .MuiInputAdornment-root': {
                        '&.MuiInputAdornment-positionStart': {
                          marginLeft: '8px',
                        },
                        '&.MuiInputAdornment-positionEnd': {
                          marginRight: '4px',
                        },
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: 'none', // Remove default border, use custom border
                      },
                    },
                  }}
                  searchField={{ productListRenderer }}
                />
              </Box>

              {/* Right operation buttons */}
              <Box sx={{ 
                display: { xs: 'none', md: 'flex' }, 
                alignItems: 'center', 
                gap: 1,
                minWidth: { md: '150px' }, // Ensure right side has enough space
                // justifyContent: 'flex-end',
              }}>
                <DesktopNavActions sx={{ gap: 1 }}>
                  <CustomerFab guestHref='/account/signin' authHref='/account' />
                  {cartEnabled && <DesktopCartIcon />}
                </DesktopNavActions>
              </Box>

              {/* Mobile right button */}
              <MobileTopRight>
                <SearchFab 
                  size='medium'
                  sx={{ 
                    mr: 1,
                    display: { xs: 'flex', md: 'none' }
                  }} 
                />
                <IconButton
                  onClick={() => setMobileMenuOpen(true)}
                  size="medium"
                  sx={{
                    display: { xs: 'flex', md: 'none' },
                    p: 1.5,
                  }}
                >
                  <IconSvg src={iconMenu} size='medium' />
                </IconButton>
              </MobileTopRight>
            </Box>
          </>
        }
        headerBottom={
          <>
            <Box sx={{ 
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              // justifyContent: 'center',
              width: '100%',
              height: (theme) => theme.appShell.headerHeightSm,
              backgroundColor: '#fff',
            }}>
              <DesktopMenuContainer menuItems={filteredMenuItems} />
            </Box>
          </>
        }
        footer={<Footer footer={footer} />}
        cartFab={isMobile ? <CartFab /> : null}
      >
        {children}
      </LayoutDefault>
    </>
  )
}
