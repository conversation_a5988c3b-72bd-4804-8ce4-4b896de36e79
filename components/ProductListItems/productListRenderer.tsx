import type { ProductListItemRenderer } from '@graphcommerce/magento-product'
import { ProductListItem } from '@graphcommerce/magento-product'
import { ProductReviewSummary } from '@graphcommerce/magento-review'
import { ProductWishlistChip } from '@graphcommerce/magento-wishlist'
import { CompareProductToggle } from '@graphcommerce/magento-compare'
import { CustomProductListItem } from './CustomProductListItem'

export const productListRenderer: ProductListItemRenderer = {
  Skeleton: (props) => <ProductListItem {...props} aspectRatio={[1, 1]} />,
  SimpleProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              // backgroundColor: 'rgba(255, 255, 255, 0.9)',
              // backdropFilter: 'blur(8px)',
              // '&:hover': {
              //   backgroundColor: 'rgba(255, 255, 255, 0.95)',
              //   transform: 'scale(1.05)',
              // },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#f3626f !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  ConfigurableProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                transform: 'scale(1.05)',
              },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#ff4757 !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  BundleProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                transform: 'scale(1.05)',
              },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#ff4757 !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  VirtualProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                transform: 'scale(1.05)',
              },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#ff4757 !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  DownloadableProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                transform: 'scale(1.05)',
              },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#ff4757 !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  GroupedProduct: (props) => (
    <CustomProductListItem
      {...props}
      bottomLeft={<ProductReviewSummary {...props} />}
      topRight={
        <ProductWishlistChip 
          {...props} 
          sx={(theme) => ({
            '& .MuiIconButton-root': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                transform: 'scale(1.05)',
              },
            },
            '& .ProductWishlistChipBase-wishlistIcon': {
              color: '#ff4757 !important', // More prominent red for inactive state
            },
            '& .ProductWishlistChipBase-wishlistIconActive': {
              color: '#e74c3c !important', // Bright red for active state
            },
          })}
        />
      }
      topLeft={<CompareProductToggle product={props} color="inherit" />}
    />
  ),
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore GiftCardProduct is only available in Commerce
  GiftCardProduct: (props) => <ProductListItem {...props} aspectRatio={[1, 1]} />,
}
