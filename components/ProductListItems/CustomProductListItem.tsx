import { ShoppingBasket as ShoppingBasketIcon, AddShoppingCart as AddShoppingCartIcon } from '@mui/icons-material'
import type { Theme } from '@mui/material'
import { Box, Button, useTheme, IconButton } from '@mui/material'
import { extendableComponent } from '@graphcommerce/next-ui'
import type { ReactNode } from 'react'
import Link from 'next/link'

import type { ProductListItemProps } from '@graphcommerce/magento-product'
import {
  AddProductsToCartButton,
  ProductImageContainer,
  ProductListItemImage,
  ProductListItemLinkOrDiv,
  ProductListItemTitleAndPrice,
  ProductListPrice,
  useProductLink,
} from '@graphcommerce/magento-product'

const { classes } = extendableComponent('ProductListItem', [
  'root',
  'item',
  'title',
  'titleContainer',
  'subtitle',
  'price',
  'overlayItems',
  'topLeft',
  'topRight',
  'bottomLeft',
  'bottomRight',
  'imageContainer',
  'placeholder',
  'image',
  'discount',
] as const)

type CustomProductListItemProps = ProductListItemProps & {
  bottomLeft?: ReactNode
  topRight?: ReactNode
  topLeft?: ReactNode
}

function ConditionalAddToCartButton(props: CustomProductListItemProps) {
  // Skeleton has no link and can't be added to the cart.
  if (props.__typename === 'Skeleton') return null

  if (props.__typename !== 'SimpleProduct' && props.__typename !== 'VirtualProduct') {
    return (
      <IconButton
        color='primary'
        size='medium'
        sx={{
          backgroundColor: 'primary.main',
          color: 'white',
          width: 40,
          height: 40,
          '&:hover': {
            backgroundColor: 'primary.dark',
            transform: 'scale(1.05)',
          },
          // Prevent event bubbling to avoid conflicts
          pointerEvents: 'auto',
        }}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          // Navigate to product page for configurable products
          window.location.href = useProductLink(props);
        }}
      >
        <AddShoppingCartIcon fontSize="small" />
      </IconButton>
    )
  }

  return (
    <AddProductsToCartButton
      sku={props.sku}
      variant='pill'
      color='primary'
      size='medium'
      sx={{
        minWidth: 40,
        width: 40,
        height: 40,
        borderRadius: '50%',
        padding: 0,
        backgroundColor: 'primary.main',
        color: 'white',
        // Prevent event bubbling
        pointerEvents: 'auto',
        '&:hover': {
          backgroundColor: 'primary.dark',
          transform: 'scale(1.05)',
        },
        '& .MuiButton-startIcon, & .MuiLoadingButton-startIconLoadingStart': {
          margin: 0,
        },
        '& .MuiLoadingButton-loadingIndicator': {
          color: 'white',
        },
        // Keep background consistent during loading
        '&.MuiLoadingButton-loading': {
          backgroundColor: 'primary.main',
          color: 'transparent', // Hide the icon during loading
        },
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <AddShoppingCartIcon fontSize="small" />
    </AddProductsToCartButton>
  )
}

export function CustomProductListItem(props: CustomProductListItemProps) {
  const theme = useTheme()

  if (props.__typename === 'Skeleton') {
    return null
  }

  const {
    id,
    name,
    price_range,
    small_image,
    loading,
    sizes,
    aspectRatio = [1, 1],
    titleComponent = 'h2',
    onClick,
    sx = [],
    bottomLeft,
    topRight,
    topLeft,
    ...rest
  } = props

  return (
    <ProductListItemLinkOrDiv
      href={useProductLink(props)}
      onClick={(e) => onClick?.(e, props)}
      sx={[
        { display: 'flex', flexDirection: 'column', height: '100%' },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      <ProductImageContainer sx={{ position: 'relative' }}>
        <ProductListItemImage
          src={small_image?.url}
          alt={small_image?.label ?? ''}
          aspectRatio={aspectRatio}
          loading={loading}
          sizes={sizes}
        />
        {/* Add overlay items for image hover effects */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            padding: theme.spacing(1),
            pointerEvents: 'none',
            '& > *': {
              pointerEvents: 'auto',
            },
          }}
        >
          {/* Top Left - Compare Button */}
          <Box>
            {topLeft}
          </Box>
          
          {/* Top Right - Wishlist Button */}
          <Box>
            {topRight}
          </Box>
        </Box>
      </ProductImageContainer>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          flexGrow: 1,
          padding: theme.spacing(1),
        }}
      >
        {/* Product Title */}
        <Box sx={{ mb: 1 }}>
          <ProductListItemTitleAndPrice
            classes={classes}
            titleComponent={titleComponent}
            title={name}
          >
            {null}
          </ProductListItemTitleAndPrice>
        </Box>

        {/* Reviews */}
        {bottomLeft}

        {/* Price and Add to Cart Button Row */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          // mt: 1 
        }}>
          <Box sx={{ flexGrow: 1 }}>
            <Box
              sx={{
                fontSize: '1.3rem',
                fontWeight: 500,
                letterSpacing: '0.02em',
                '& .ProductListPrice-finalPrice': {
                  fontSize: '1.3rem',
                  color: 'error.main', // Red color when there's a discount
                  fontWeight: 500,
                },
                // If no discount, keep normal color
                '&:not(:has(.ProductListPrice-discountPrice)) .ProductListPrice-finalPrice': {
                  color: 'text.primary',
                },
              }}
            >
              <ProductListPrice 
                {...price_range.minimum_price} 
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  '& .ProductListPrice-discountPrice': {
                    marginRight: 0,
                    marginBottom: '2px',
                  },
                }}
              />
            </Box>
          </Box>
          <ConditionalAddToCartButton {...props} />
        </Box>
      </Box>
    </ProductListItemLinkOrDiv>
  )
} 