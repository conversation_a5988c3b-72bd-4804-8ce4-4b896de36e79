import { RichText, Asset } from '@graphcommerce/hygraph-ui'
import { breakpointVal } from '@graphcommerce/next-ui'
import { Box, Container, styled } from '@mui/material'
import { BannerFragment } from './Banner.gql'

const BannerRoot = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  minHeight: '400px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  overflow: 'hidden',
  [theme.breakpoints.up('md')]: {
    minHeight: '500px',
  },
  [theme.breakpoints.up('lg')]: {
    minHeight: '600px',
  },
}))

const BannerContent = styled(Container)(({ theme }) => ({
  position: 'relative',
  zIndex: 2,
  textAlign: 'center',
  color: theme.palette.common.white,
  padding: theme.spacing(4),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(6),
  },
}))

const BannerBackground = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  zIndex: 1,
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
})

export function Banner(props: BannerFragment) {
  const { copy, image } = props

  return (
    <BannerRoot>
      {image?.url && (
        <BannerBackground>
          <Asset 
            asset={image} 
            sizes="100vw"
            sx={{
              objectFit: 'cover',
              width: '100%',
              height: '100%',
            }}
          />
        </BannerBackground>
      )}
      
      {/* <BannerContent maxWidth="lg">
        {copy && (
          <RichText
            {...copy}
            sxRenderer={{
              paragraph: (theme) => ({
                ...breakpointVal('fontSize', 16, 20, theme.breakpoints.values),
                lineHeight: 1.6,
                margin: theme.spacing(2, 0),
              }),
              'heading-one': (theme) => ({
                ...breakpointVal('fontSize', 32, 56, theme.breakpoints.values),
                fontWeight: 700,
                lineHeight: 1.2,
                margin: theme.spacing(0, 0, 3),
                textTransform: 'uppercase',
                '& strong': {
                  color: theme.palette.primary.main,
                },
              }),
              'heading-two': (theme) => ({
                ...breakpointVal('fontSize', 24, 40, theme.breakpoints.values),
                fontWeight: 600,
                lineHeight: 1.3,
                margin: theme.spacing(0, 0, 2),
              }),
              'heading-three': (theme) => ({
                ...breakpointVal('fontSize', 20, 32, theme.breakpoints.values),
                fontWeight: 600,
                lineHeight: 1.4,
                margin: theme.spacing(0, 0, 2),
              }),
            }}
          />
        )}
      </BannerContent> */}
    </BannerRoot>
  )
}