import { PasswordElement } from '@graphcommerce/ecommerce-ui'
import { graphqlErrorByCategory } from '@graphcommerce/magento-graphql'
import { Button, FormActions, FormRow } from '@graphcommerce/next-ui'
import type { UseFormClearErrors, UseFormSetError } from '@graphcommerce/react-hook-form'
import { FormPersist, useFormGqlMutation } from '@graphcommerce/react-hook-form'
import { t } from '@lingui/macro'
import { Trans } from '@lingui/react'
import { ApolloCustomerErrorSnackbar } from '@graphcommerce/magento-customer/components/ApolloCustomerError/ApolloCustomerErrorSnackbar'
import type { SignInMutation, SignInMutationVariables } from '@graphcommerce/magento-customer/components/SignInForm/SignIn.gql'
import { SignInDocument } from '@graphcommerce/magento-customer/components/SignInForm/SignIn.gql'

type SignInFormProps = {
  email?: string
  setError: UseFormSetError<{ email?: string; requestedMode?: 'signin' | 'signup' }>
  clearErrors: UseFormClearErrors<{ email?: string; requestedMode?: 'signin' | 'signup' }>
}

export function SignInForm(props: SignInFormProps) {
  const { email, setError, clearErrors } = props

  const form = useFormGqlMutation<SignInMutation, SignInMutationVariables>(
    SignInDocument,
    {
      defaultValues: { email },
      onBeforeSubmit: (values) => {
        // 修复：使用 getValues() 获取当前表单的完整值
        const currentFormValues = form.getValues()
        
        if (!email) {
          setError('email', { message: t`Please enter a valid email address` })
          return false
        }
        clearErrors()
        
        // 修复：使用从 getValues() 获取的值，确保密码不丢失
        const finalValues = { 
          ...currentFormValues, 
          email: email ?? '',
          password: currentFormValues.password || values.password,
        }
        
        return finalValues
      },
    },
    { errorPolicy: 'all' },
  )

  const { handleSubmit, required, formState, error, control } = form
  const [remainingError, inputError] = graphqlErrorByCategory({ category: 'graphql-input', error })

  const submitHandler = handleSubmit(() => {})

  return (
    <form onSubmit={submitHandler} noValidate>
      <FormRow>
        <PasswordElement
          control={control}
          name='password'
          variant='outlined'
          error={!!formState.errors.password || !!inputError}
          label={<Trans id='Password' />}
          autoFocus={!!email}
          autoComplete='current-password'
          required={required.password}
          disabled={formState.isSubmitting}
          helperText={inputError?.message}
        />
      </FormRow>

      <ApolloCustomerErrorSnackbar error={remainingError} />

      <FormActions>
        <Button
          type='submit'
          id='sign-in'
          variant='pill'
          color='primary'
          size='large'
          loading={formState.isSubmitting}
        >
          <Trans id='Sign In' />
        </Button>
      </FormActions>
      <FormPersist form={form} name='SignIn' exclude={['password']} />
    </form>
  )
} 