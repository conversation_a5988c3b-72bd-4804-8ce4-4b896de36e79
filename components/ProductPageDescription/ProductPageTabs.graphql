fragment ProductPageTabs on ProductInterface {
  name
  sku
  url_key
  mpn
  colour
  shape
  overflow_cap
  waste_outlet_diameter
  tap_hole_diameter
  tap_hole
  excludes
  includes
  # Add more product attributes for specifications
  brand
  ean
  glass_thickness
  coating
  # Try to get additional common attributes that exist
  model
  certification
  pan_material
  Length
  short_description {
    html
  }
  description {
    html
  }
  rating_summary
  review_count
  reviews(pageSize: 5, currentPage: 1) {
    items {
      average_rating
      created_at
      nickname
      summary
      text
      ratings_breakdown {
        name
        value
      }
    }
    page_info {
      current_page
      page_size
      total_pages
    }
  }
  # Get custom attributes for dynamic data (only when needed)
  custom_attributesV2(filters: { is_visible_on_front: true }) @include(if: $useCustomAttributes) {
    items {
      code
      __typename
      ... on AttributeValue {
        value
      }
      ... on AttributeSelectedOptions {
        selected_options {
          label
          value
        }
      }
    }
  }
  ... on SimpleProduct {
    weight
  }
} 