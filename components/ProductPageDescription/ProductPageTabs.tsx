import React, { useState } from 'react'
import {
  ColumnOne,
  LazyHydrate,
  breakpointVal,
  extendableComponent,
} from '@graphcommerce/next-ui'
import type { SxProps, Theme } from '@mui/material'
import { 
  Box, 
  Tabs, 
  Tab, 
  Typography,
  Divider,
  Rating,
  Grid,
  Paper
} from '@mui/material'
import type { Variant } from '@mui/material/styles/createTypography'
import { ProductReviews } from '@graphcommerce/magento-review'
import type { ProductPageDescriptionFragment } from './ProductPageDescription.gql'
import type { ProductPageTabsFragment } from './ProductPageTabs.gql'
import styles from './ProductPageDescription.module.css'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
  className?: string
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `product-tab-${index}`,
    'aria-controls': `product-tabpanel-${index}`,
  }
}

// Helper function to get custom attribute value
function getCustomAttributeValue(customAttributes: any[], attributeCode: string): string | null {
  if (!customAttributes) return null
  
  const attribute = customAttributes.find((attr: any) => attr.code === attributeCode)
  if (!attribute) return null
  
  // Handle AttributeValue type
  if (attribute.value) {
    return attribute.value
  }
  
  // Handle AttributeSelectedOptions type
  if (attribute.selected_options && attribute.selected_options.length > 0) {
    return attribute.selected_options.map((option: any) => option.label).join(', ')
  }
  
  return null
}

export type ProductPageTabsProps = {
  sx?: SxProps<Theme>
  fontSize?: 'responsive' | Variant
  product: ProductPageTabsFragment
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

const componentName = 'ProductPageTabs'
const parts = ['root', 'tabs', 'tabPanel', 'description', 'specifications', 'reviews'] as const

const { classes } = extendableComponent(componentName, parts)

export function ProductPageTabs(props: ProductPageTabsProps) {
  const { product, fontSize = 'subtitle1', maxWidth = 'lg', sx = [] } = props
  const [value, setValue] = useState(0)

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }

  // Build specifications from real Magento data
  const specifications = [
    { name: 'Product Name', value: product.name },
    { name: 'SKU', value: product.sku },
    product.mpn && { name: 'MPN', value: product.mpn },
    product.brand && { name: 'Brand', value: product.brand },
    product.model && { name: 'Model Name', value: product.model },
    product.ean && { name: 'EAN', value: product.ean },
    product.certification && { name: 'Certification', value: product.certification },
    product.colour && { name: 'Colour', value: product.colour },
    product.shape && { name: 'Shape', value: product.shape },
    product.pan_material && { name: 'Material', value: product.pan_material },
    product.Length && { name: 'Length (mm)', value: product.Length.toString() },
    product.glass_thickness && { name: 'Glass Thickness (mm)', value: product.glass_thickness },
    product.coating && { name: 'Coating', value: product.coating },
    product.overflow_cap && { name: 'Overflow Cap', value: product.overflow_cap },
    product.waste_outlet_diameter && { name: 'Waste Outlet Diameter (mm)', value: product.waste_outlet_diameter },
    product.tap_hole_diameter && { name: 'Tap Hole Diameter (mm)', value: product.tap_hole_diameter },
    product.tap_hole && { name: 'Tap Hole', value: product.tap_hole === 429 ? 'No' : 'Yes' },
    product.includes && { name: 'Includes', value: product.includes },
    product.excludes && { name: 'Excludes', value: product.excludes },
    ('weight' in product && product.weight) && { name: 'Shipping Weight (KG)', value: product.weight.toString() },
  ].filter(Boolean) as Array<{ name: string; value: string }>

  // Add configurable product options if available
  if ('configurable_options' in product && product.configurable_options && Array.isArray(product.configurable_options)) {
    const configurableSpecs = product.configurable_options
      .map(option => {
        if (!option?.values?.length) return null
        
        const values = option.values.map(val => val?.store_label).filter(Boolean).join(', ')
        if (!values) return null
        
        return {
          name: option.label || option.attribute_code || 'Option',
          value: values
        }
      })
      .filter(Boolean) as Array<{ name: string; value: string }>
    
    specifications.push(...configurableSpecs)
  }

  // Add custom attributes to specifications if available
  if (product.custom_attributesV2?.items) {
    // Define attribute mappings for better display names
    const attributeMappings: Record<string, string> = {
      'model_name': 'Model Name',
      'type': 'Type',
      'width': 'Width (mm)',
      'width_mm': 'Width (mm)',
      'height': 'Height (mm)',
      'height_mm': 'Height (mm)',
      'material': 'Material',
      'adjustment': 'Adjustment (mm)',
      'adjustment_mm': 'Adjustment (mm)',
      'certification': 'Certification',
      'nano_coating': 'Nano Coating',
      'glass_type': 'Glass Type',
      'frame_type': 'Frame Type',
      'door_type': 'Door Type',
      'installation_type': 'Installation Type',
      'warranty_years': 'Warranty (Years)',
      'country_of_origin': 'Country of Origin',
      'finish': 'Finish',
      'style': 'Style',
      'collection': 'Collection',
      'range_name': 'Range Name',
      'din_en_12150_1': 'DIN EN 12150-1',
      'din_en_14428': 'DIN EN 14428',
      'safety_certification': 'Safety Certification',
      'glass_material': 'Glass Material',
      'shower_door_type': 'Shower Door Type',
      'door_only': 'Door Only',
      'ravenna_17': 'Ravenna 17',
      'durovin_bathrooms': 'Durovin Bathrooms'
    }

    const customSpecs = product.custom_attributesV2.items
      .map(attr => {
        const value = getCustomAttributeValue([attr], attr.code)
        if (!value) return null
        
        // Use mapping if available, otherwise convert attribute code to readable label
        const label = attributeMappings[attr.code] || attr.code
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        
        return { name: label, value }
      })
      .filter(Boolean) as Array<{ name: string; value: string }>
    
    specifications.push(...customSpecs)
  }

  return (
    <LazyHydrate height={500}>
      <ColumnOne
        maxWidth={maxWidth}
        className={classes.root}
        sx={sx}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs 
            value={value} 
            onChange={handleChange} 
            aria-label="product information tabs"
            variant="scrollable"
            scrollButtons="auto"
            className={classes.tabs}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                minWidth: 'auto',
                px: { xs: 2, md: 3 },
              },
              '& .MuiTabs-indicator': {
                height: 3,
              },
            }}
          >
            <Tab label="Details" {...a11yProps(0)} />
            <Tab label="Specifications" {...a11yProps(1)} />
            <Tab 
              label={`Reviews (${product.review_count || 0})`} 
              {...a11yProps(2)} 
            />
            <Tab label="Delivery & Returns" {...a11yProps(3)} />
          </Tabs>
        </Box>

        {/* Details Tab */}
        <TabPanel value={value} index={0} className={classes.tabPanel}>
          {product.description && (
            <Box
              className={`${classes.description} ${styles.descriptionWrapper}`}
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{ __html: product.description.html }}
              sx={[
                {
                  '& p:first-of-type': {
                    marginTop: 0,
                  },
                  '& p': {
                    marginBottom: 2,
                    lineHeight: 1.6,
                  },
                  '& img': {
                    maxWidth: '100%',
                    height: 'auto',
                  },
                },
                fontSize === 'responsive' &&
                  ((theme) => ({
                    '& p, & li': {
                      ...breakpointVal('fontSize', 16, 18, theme.breakpoints.values),
                    },
                  })),
                fontSize !== 'responsive' && {
                  '& p, & li': {
                    fontSize,
                  },
                },
              ]}
            />
          )}
          {!product.description && (
            <Typography color="text.secondary">
              No detailed description available for this product.
            </Typography>
          )}
        </TabPanel>

        {/* Specifications Tab */}
        <TabPanel value={value} index={1} className={classes.tabPanel}>
          <Box className={classes.specifications}>
            {specifications.length > 0 ? (
              <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                gap: 1,
                '& .spec-row': {
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 1,
                  px: 2,
                  borderRadius: 1,
                  '&:nth-of-type(odd)': {
                    bgcolor: 'grey.50',
                  },
                },
                '& .spec-name': {
                  fontWeight: 500,
                  color: 'text.secondary',
                  fontSize: '0.875rem',
                  minWidth: '120px',
                  flexShrink: 0,
                },
                '& .spec-value': {
                  fontWeight: 500,
                  color: 'text.primary',
                  fontSize: '0.875rem',
                  textAlign: 'right',
                },
              }}>
                {specifications.map((spec, index) => (
                  <Box key={index} className="spec-row">
                    <Typography className="spec-name">
                      {spec.name}
                    </Typography>
                    <Typography className="spec-value">
                      {spec.value}
                    </Typography>
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary">
                No specifications available for this product.
              </Typography>
            )}
          </Box>
        </TabPanel>

        {/* Reviews Tab */}
        <TabPanel value={value} index={2} className={classes.tabPanel}>
          <Box className={classes.reviews}>
            {product.rating_summary && product.review_count ? (
              <>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Rating 
                    value={product.rating_summary / 20} 
                    precision={0.1} 
                    readOnly 
                    sx={{ mr: 2 }}
                  />
                  <Typography variant="h6" sx={{ mr: 1 }}>
                    {(product.rating_summary / 20).toFixed(1)}
                  </Typography>
                  <Typography color="text.secondary">
                    ({product.review_count} reviews)
                  </Typography>
                </Box>
                
                {product.reviews && (
                  <ProductReviews
                    reviews={product.reviews}
                    url_key={product.url_key || ''}
                    sku={product.sku || ''}
                    review_count={product.review_count}
                  />
                )}
              </>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6" gutterBottom>
                  No Reviews Yet
                </Typography>
                <Typography color="text.secondary">
                  Be the first to review this product
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>

        {/* Delivery & Returns Tab - Fixed Content */}
        <TabPanel value={value} index={3} className={classes.tabPanel}>
          <Box sx={{ 
            maxWidth: '100%',
            '& .delivery-returns-content': {
                             '& .section-header': {
                 mb: 3,
                 pb: 2,
                 borderBottom: '1px solid',
                 borderColor: 'grey.300',
               },
                             '& .section-title': {
                 fontWeight: 600,
                 fontSize: '1.5rem',
                 color: 'text.primary',
               },
                             '& .delivery-card': {
                 mb: 3,
                 p: 2.5,
                 bgcolor: 'grey.50',
                 borderRadius: 1,
                 border: '1px solid',
                 borderColor: 'grey.200',
               },
              
                             '& .method-title': {
                 fontWeight: 600,
                 fontSize: '1rem',
                 mb: 1,
                 color: 'primary.main',
               },
              '& .method-description': {
                color: 'text.secondary',
                lineHeight: 1.6,
              },
              '& .zone-grid': {
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                gap: 2,
                mt: 2,
              },
              '& .zone-card': {
                p: 2,
                borderRadius: 1.5,
                border: '1px solid',
                borderColor: 'grey.300',
                bgcolor: 'grey.50',
              },
              '& .zone-title': {
                fontWeight: 600,
                fontSize: '0.95rem',
                mb: 1,
                color: 'primary.main',
              },
              '& .zone-info': {
                fontSize: '0.875rem',
                color: 'text.secondary',
                lineHeight: 1.5,
              },
              '& .pricing-table': {
                mt: 2,
                '& .price-row': {
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 1.5,
                  px: 2,
                  borderRadius: 1,
                  mb: 1,
                  '&:nth-of-type(odd)': {
                    bgcolor: 'grey.50',
                  },
                },
                '& .price-zone': {
                  fontWeight: 500,
                  fontSize: '0.9rem',
                },
                '& .price-amount': {
                  fontWeight: 600,
                  color: 'primary.main',
                },
              },
              '& .warning-box': {
                p: 2,
                borderRadius: 1.5,
                bgcolor: 'error.50',
                border: '1px solid',
                borderColor: 'error.200',
                mt: 2,
                '& .warning-text': {
                  color: 'error.dark',
                  fontWeight: 500,
                },
              },
              '& .info-box': {
                p: 2,
                borderRadius: 1.5,
                bgcolor: 'info.50',
                border: '1px solid',
                borderColor: 'info.200',
                mt: 2,
                '& .info-text': {
                  color: 'info.dark',
                },
              },
              '& .quality-section': {
                mt: 4,
                               '& .quality-item': {
                 mb: 2,
                 p: 2,
                 borderRadius: 1,
                 bgcolor: 'grey.50',
                 border: '1px solid',
                 borderColor: 'grey.200',
               },
                               '& .quality-title': {
                 fontWeight: 600,
                 fontSize: '1rem',
                 mb: 1,
                 color: 'primary.main',
               },
                '& .quality-text': {
                  color: 'text.secondary',
                  lineHeight: 1.6,
                },
              },
            }
          }}>
            <div className="delivery-returns-content">
              
                             {/* Shipping Section */}
               <Box className="section-header">
                 <Typography className="section-title">
                   Shipping Information
                 </Typography>
               </Box>
 
               {/* Important Notice */}
               <Box className="delivery-card">
                 <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                   Important Delivery Information
                 </Typography>
                <Typography paragraph sx={{ color: 'text.secondary', lineHeight: 1.6 }}>
                  Delivery times are calculated from when your order leaves our warehouse, not from when you place the order. 
                  Please wait for your order to arrive and inspect all items before arranging installation. We do not cover 
                  installation costs if products are damaged in transit or unsuitable.
                </Typography>
                <Typography sx={{ color: 'text.secondary', lineHeight: 1.6 }}>
                  We provide tracking numbers and delivery updates. Contact us anytime for delivery status, time slots, 
                  or courier contact information.
                </Typography>
              </Box>

              {/* Delivery Methods */}
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: 'text.primary' }}>
                Delivery Methods
              </Typography>

              <Box className="delivery-card">
                 <Typography className="method-title">Small Items</Typography>
                 <Typography className="method-description">
                   Letter box size or under 2kg items via Royal Mail. Carefully packaged from our warehouse. 
                   Delivery within 2-3 days (or next day with expedited shipping).
                 </Typography>
                <br/>
                 <Typography className="method-title">Medium Items</Typography>
                 <Typography className="method-description">
                   Delivered via Parcelforce and TNT. Next business day delivery unless otherwise requested. 
                   Contact us immediately if you won't be available for delivery.
                 </Typography>
              <br/>
                 <Typography className="method-title">Large Products</Typography>
                 <Typography className="method-description">
                   Specialist delivery for glassware, bath tubs, and items over 30kg. 2-7 working days for England & Wales, 
                   up to 14 days for Scotland. You'll be contacted to arrange a suitable delivery date.
                 </Typography>
               </Box>

              {/* Delivery Zones */}
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, mt: 4, color: 'text.primary' }}>
                Delivery Zones & Pricing
              </Typography>

              <Box className="zone-grid">
                <Box className="zone-card">
                  <Typography className="zone-title">Zone A - FREE</Typography>
                  <Typography className="zone-info">
                    Mainland England & Wales<br/>
                    Standard delivery included
                  </Typography>
                </Box>
                <Box className="zone-card">
                  <Typography className="zone-title">Zone B - Surcharge</Typography>
                  <Typography className="zone-info">
                    Scottish Mainlands<br/>
                    Postcodes: KA, G, FK, KY, DG, ML, EH, TD
                  </Typography>
                </Box>
                <Box className="zone-card">
                  <Typography className="zone-title">Zone C - Surcharge</Typography>
                  <Typography className="zone-info">
                    Scottish Highlands<br/>
                    Postcodes: IW, KW, PH, AB, PA, DD
                  </Typography>
                </Box>
                <Box className="zone-card" sx={{ bgcolor: 'error.50', borderColor: 'error.200' }}>
                  <Typography className="zone-title" sx={{ color: 'error.main' }}>Zone D & E - No Delivery</Typography>
                  <Typography className="zone-info" sx={{ color: 'error.dark' }}>
                    Northern Ireland & Offshore Islands<br/>
                    Customer pre-arranged shipping only
                  </Typography>
                </Box>
              </Box>

              {/* 2-Man Delivery Pricing */}
              <Box className="delivery-card" sx={{ mt: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  2-Man Delivery Surcharges
                </Typography>
                <Box className="pricing-table">
                  <Box className="price-row">
                    <Typography className="price-zone">DG1-4, EH1-EH38, G1-G80, FK1-FK16, KA, ML, TD1-TD9</Typography>
                    <Typography className="price-amount">£40</Typography>
                  </Box>
                  <Box className="price-row">
                    <Typography className="price-zone">AB1-AB30, DD1-DD11, DG5-DG9, EH39-EH42, etc.</Typography>
                    <Typography className="price-amount">£60</Typography>
                  </Box>
                  <Box className="price-row">
                    <Typography className="price-zone">AB31+, KW6-KW10, IV7-IV11, PA21-PA27, etc.</Typography>
                    <Typography className="price-amount">£95</Typography>
                  </Box>
                  <Box className="price-row">
                    <Typography className="price-zone">IV24-IV25, KW1-KW5, PA32-PA38, etc.</Typography>
                    <Typography className="price-amount">£120</Typography>
                  </Box>
                  <Box className="price-row">
                    <Typography className="price-zone">IV21-IV23, KW11-KW14, PA28-PA31, etc.</Typography>
                    <Typography className="price-amount">£140</Typography>
                  </Box>
                </Box>
              </Box>

                             {/* Missed Delivery */}
               <Box className="delivery-card">
                 <Typography className="warning-text" variant="h6" gutterBottom>
                   Missed Delivery Charges
                 </Typography>
                 <Typography className="warning-text">
                   Redelivery charges apply if you miss your delivery slot:<br/>
                   Zone A: £35 • Zone B: £50 • Zone C: £80<br/>
                   <strong>DO NOT refuse delivery - return shipping fees will be deducted from refunds.</strong>
                 </Typography>
               </Box>

                             {/* Returns Section */}
               <Box className="section-header">
                 <Typography className="section-title" sx={{ color: 'secondary.main' }}>
                   Returns Policy
                 </Typography>
               </Box>

              <Box className="delivery-card">
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  30-Day Return Policy
                </Typography>
                <Typography paragraph sx={{ lineHeight: 1.6 }}>
                  You have <strong>30 days</strong> from receiving your item to return it for a refund or exchange. 
                  You must notify us in writing within <strong>14 days</strong> to process the return within the 30-day period.
                </Typography>
                
                {/* <Box sx={{ mt: 2, p: 2, bgcolor: 'error.50', borderRadius: 1, border: '1px solid', borderColor: 'error.200' }}> */}
                <Typography paragraph sx={{ lineHeight: 1.6 }}>
                  We refuse ALL items that have been USED or INSTALLED
                </Typography>
                 {/* </Box> */}

                <Typography paragraph sx={{ lineHeight: 1.6, mt: 2 }}>
                  Return shipping costs are your responsibility. For orders with free shipping, up to 25% of sale price 
                  (minimum £5) will be deducted from your refund.
                </Typography>
              <br/>
                 <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                   Damage Reporting - 24 Hour Rule
                 </Typography>
                 <Typography>
                   Report any damage or faults within <strong>24 hours</strong> of delivery. We may request photos or videos 
                   as proof before issuing a pre-paid return label or refund. Items not reported within 24 hours cannot be processed.
                 </Typography>
               </Box>

              {/* Quality Control */}
              <Box className="quality-section">
                <Typography variant="h5" sx={{ fontWeight: 600, mb: 3, color: 'text.primary' }}>
                  Quality Control Standards
                </Typography>

                  <Box className="quality-item">
                   <Typography className="quality-title">Glass Products</Typography>
                   <Typography className="quality-text">
                     Our Nano Coated Glassware is hand-finished to ensure consistent high quality. Minor imperfections 
                     may occasionally appear as marks or bubbles due to the handcrafted process. We're happy to discuss 
                     options if issues are visible from 2m in natural lighting.
                   </Typography>
                   <br/>
                   <Typography className="quality-title">Stone Resin Products</Typography>
                   <Typography className="quality-text">
                     Hand-crafted using modern stone powder and resin with high-quality satin or gloss finish. 
                     Our Colossum range features sharp lines and modern designs with UK standard outlets, 
                     adding sophistication to any bathroom.
                   </Typography>
                    <br/>
                   <Typography className="quality-title">Ceramic Products</Typography>
                   <Typography className="quality-text">
                     Brilliant white high-gloss ceramic basins made using traditional craftsmanship techniques from 95 years ago. 
                     Sizes may vary ±2% due to the handcrafted process. We only sell hand-crafted ceramic basins for premium quality.
                   </Typography>
                 </Box>
              </Box>

            </div>
          </Box>
        </TabPanel>
      </ColumnOne>
    </LazyHydrate>
  )
} 