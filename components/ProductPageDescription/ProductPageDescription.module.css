/* Scoped CSS for Product Page Description - External webdescription.min.css styles */
.descriptionWrapper {
  /* Base container styles */
  max-width: 100%;
  overflow: hidden;
  word-wrap: break-word;
  word-break: break-word;
}

/* External styles using :global() for dynamic content classes */
.descriptionWrapper :global(.desc .w_arial13) {
  font-family: "Helvetica Neue", "Verdana", "sans-serif" !important;
  font-size: 10px !important;
  color: #fff !important;
  letter-spacing: 1px !important;
}

.descriptionWrapper :global(.desc .b_arial) {
  font-family: "Helvetica Neue", "Verdana", "sans-serif" !important;
  color: #333 !important;
}

.descriptionWrapper :global(.desc .w_arial) {
  font-family: "Helvetica Neue", "Verdana", "sans-serif" !important;
  color: #fff !important;
}

.descriptionWrapper :global(.desc .b_arialB) {
  font-family: "Helvetica Neue", "Verdana", "sans-serif" !important;
  color: #333 !important;
  font-weight: 700 !important;
}

.descriptionWrapper :global(.desc #box) {
  width: 100% !important;
  max-width: 1100px !important;
  margin: 0 auto !important;
}

.descriptionWrapper :global(.desc #top_img) {
  position: relative !important;
  width: 100% !important;
  overflow: hidden !important;
}

.descriptionWrapper :global(.desc #top_img .radio + label),
.descriptionWrapper :global(.desc .radio) {
  display: none !important;
}

.descriptionWrapper :global(.desc #top_img #scroll_img) {
  transition: all 1s !important;
  width: 100% !important;
  display: flex !important;
  flex-wrap: nowrap !important;
  position: relative !important;
}

.descriptionWrapper :global(.desc #top_img #scroll_img img) {
  display: block !important;
  width: 100% !important;
}

.descriptionWrapper :global(.desc #top_img #scroll_img .titleLeft) {
  position: absolute !important;
  top: 25% !important;
  left: 4% !important;
  width: 50% !important;
  font-size: 28px !important;
  
}

.descriptionWrapper :global(.desc #top_img #scroll_img .p2) {
  position: absolute !important;
  top: 48% !important;
  left: 4% !important;
  width: 50% !important;
  line-height: 24px !important;
  font-size: 17px !important;
  margin-bottom:0px !important;
  margin-top:0px !important;
}

.descriptionWrapper :global(.desc #top_img #scroll_img .titleRight) {
  position: absolute !important;
  top: 20% !important;
  left: 53.2% !important;
  width: 25% !important;
  font-size: 28px !important;
}

.descriptionWrapper :global(.desc #top_img #scroll_img .p4) {
  position: absolute !important;
  top: 45% !important;
  left: 53.2% !important;
  width: 20% !important;
  line-height: 24px !important;
  font-size: 17px !important;
}

.descriptionWrapper :global(.desc .mt-10) {
  margin-top: 10px !important;
}

.descriptionWrapper :global(.desc .carousel) {
  display: flex !important;
  flex-direction: row !important;
  width: 100% !important;
}

.descriptionWrapper :global(.desc .carousel-container) {
  position: relative !important;
  width: 55% !important;
  overflow: hidden !important;
}

.descriptionWrapper :global(.desc .carousel-content) {
  position: relative !important;
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
}

.descriptionWrapper :global(.desc .carousel-slide) {
  position: relative !important;
}

.descriptionWrapper :global(.desc .carousel-slide img) {
  display: block !important;
  width: 100% !important;
}

/* .descriptionWrapper :global(p) {
  font-size: 16px !important;
} */


.descriptionWrapper :global(.desc #low-mid) {
  position: relative;
  margin-top: 16px
}

.descriptionWrapper :global(.desc #low-mid img) {
  width: 100%;
  display: block
}

.descriptionWrapper :global(.desc #low-mid p) {
  position: absolute;
  left: 7%;
  font-size: 17px
}

.descriptionWrapper :global(.desc #low-mid .title) {
  top: 20%;
  width: 45%;
  line-height: 32px;
  font-size: 28px
}

.descriptionWrapper :global(.desc #low-mid .p2) {
  top: 35%;
  width: 40%;
  line-height: 28px
}

.descriptionWrapper :global(.desc #low-mid .p3) {
  top: 42%;
  width: 25%;
  line-height: 28px;
  text-align: center;
  left: 71%
}

.descriptionWrapper :global(.desc #low-mid .p4) {
  top: 40%;
  width: 30%;
  line-height: 32px
}

.descriptionWrapper :global(.desc #low-mid .p5)  {
  top: 50%;
  width: 35%;
  line-height: 28px
}

.descriptionWrapper :global(.desc #low-mid .blTitle) {
  position: relative
}

.descriptionWrapper :global(.desc .carousel-slide span) {
  position: absolute !important;
  bottom: 5% !important;
  box-sizing: border-box !important;
  border: 1px solid rgba(255, 255, 255, 1) !important;
  border-radius: 2px !important;
  padding: 4px 8px !important;
  text-align: center !important;
  color: inherit !important;
  cursor: pointer !important;
}

.descriptionWrapper :global(.desc .carousel-slide span:hover) {
  opacity: 1 !important;
}

.descriptionWrapper :global(.desc .carousel-slide span.left) {
  left: 5% !important;
}

.descriptionWrapper :global(.desc .carousel-slide span.right) {
  right: 5% !important;
}

.descriptionWrapper :global(.desc .carousel-text) {
  display: flex !important;
  flex-direction: column !important;
  width: 45% !important;
  background-color: #f4f5f6 !important;
  color: #333 !important;
  overflow: hidden !important;
}

.descriptionWrapper :global(.desc .carousel-text_selling-point) {
  width: 80% !important;
  margin: auto !important;
}

.descriptionWrapper :global(.desc .carousel-sp_title) {
  margin: 0 0 20px !important;
  line-height: 28px !important;
  font-size: 28px !important;
  font-weight: 400 !important;
}

.descriptionWrapper :global(.desc .carousel-sp_list) {
  margin: 0 !important;
  padding-left: 0 !important;
}

.descriptionWrapper :global(.desc .carousel-sp_list-item) {
  list-style-type: disc !important;
  margin-top: 10px !important;
  line-height: 24px !important;
  font-size: 16px !important;
}

.descriptionWrapper :global(.desc .thumbnail) {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
}

.descriptionWrapper :global(.desc .thumbnail-item) {
  position: relative !important;
  display: inline-block !important;
  width: calc(100% / 4 - 10px) !important;
}

.descriptionWrapper :global(.desc .thumbnail-item:after) {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  display: block !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(0, 0, 0, 0.6) !important;
  pointer-events: none !important;
  transition: background 0.2s ease !important;
}

.descriptionWrapper :global(.desc .thumbnail-item:hover:after) {
  background: rgba(0, 0, 0, 0) !important;
}

.descriptionWrapper :global(.desc .thumbnail-item img) {
  display: block !important;
  width: 100% !important;
}

.descriptionWrapper :global(.desc #spec) {
  position: relative !important;
  margin: 0 auto !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.descriptionWrapper :global(.desc #spec img) {
  display: block !important;
  margin: 0 auto !important;
  width: 100% !important;
}

.descriptionWrapper :global(.desc #spec .title) {
  font-size: 18px !important;
}

.descriptionWrapper :global(.desc #spec .spec_div) {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
  box-sizing: border-box !important;
  width: 94% !important;
  margin: 0 auto !important;
  padding: 3rem 0 !important;
}

.descriptionWrapper :global(.desc #spec .spec_div p) {
  padding: 1rem 0 !important;
}

.descriptionWrapper :global(.desc #spec .spec_div .half) {
  width: 50% !important;
}

.descriptionWrapper :global(.desc #spec .spec_div ul li) {
  line-height: 28px !important;
  list-style: none !important;
  font-size: 15px !important;
}

.descriptionWrapper :global(.desc .toiletExtra) {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  width: 100% !important;
  padding: 60px 5% !important;
  background-color: #f7f9fa !important;
  box-sizing: border-box !important;
}

.descriptionWrapper :global(.desc .toiletExtra-item) {
  box-sizing: border-box !important;
  width: calc(100% / 2 - 50px) !important;
}

.descriptionWrapper :global(.desc .toiletExtra-item img) {
  display: block !important;
  width: 100% !important;
}

/* Mobile responsive styles */
@media screen and (max-width: 450px) {
  .descriptionWrapper :global(.desc .carousel-sp_title) {
    margin: 0 0 10px !important;
    line-height: 20px !important;
    font-size: 20px !important;
  }

  .descriptionWrapper :global(.desc .carousel-sp_list-item) {
    list-style-type: disc !important;
    margin-top: 5px !important;
    line-height: 20px !important;
    font-size: 14px !important;
  }
}

@media screen and (max-width: 992px) {
  .descriptionWrapper :global(.desc .carousel) {
    flex-direction: column !important;
  }

  .descriptionWrapper :global(.desc .carousel-container),
  .descriptionWrapper :global(.desc .carousel-text) {
    width: 100% !important;
  }

  .descriptionWrapper :global(.desc .carousel-text) {
    padding: 20px 0 !important;
  }
}

@media only screen and (max-width: 768px) {
  .descriptionWrapper :global(.desc #box) {
    width: 100% !important;
    font-size: 12px !important;
  }

  .descriptionWrapper :global(.desc .toiletExtra) {
    flex-direction: column !important;
    padding: 20px 0 !important;
  }

  .descriptionWrapper :global(.desc .toiletExtra-item) {
    width: 100% !important;
    color: #333 !important;
  }
}

/* Base overflow protection */
.descriptionWrapper :global(img) {
  max-width: 100% !important;
  height: auto !important;
}

.descriptionWrapper :global(table) {
  max-width: 100% !important;
  overflow: auto !important;
}

.descriptionWrapper :global(div),
.descriptionWrapper :global(p),
.descriptionWrapper :global(span) {
  max-width: 100% !important;
  word-wrap: break-word !important;
} 

