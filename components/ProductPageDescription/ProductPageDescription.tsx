import {
  ColumnOne,
  LazyHydrate,
  breakpointVal,
  extendableComponent,
} from '@graphcommerce/next-ui'
import type { SxProps, Theme } from '@mui/material'
import { Box } from '@mui/material'
import type { Variant } from '@mui/material/styles/createTypography'
import type { ProductPageDescriptionFragment } from './ProductPageDescription.gql'
import styles from './ProductPageDescription.module.css'

export type ProductPageDescriptionProps = ProductPageDescriptionFragment & {
  sx?: SxProps<Theme>
  fontSize?: 'responsive' | Variant
  product: ProductPageDescriptionFragment
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

const componentName = 'ProductPageDescription'
const parts = ['root', 'description'] as const

const { classes } = extendableComponent(componentName, parts)

export function ProductPageDescription(props: ProductPageDescriptionProps) {
  const { product, fontSize = 'subtitle1', maxWidth = 'lg', sx = [] } = props

  return (
    <LazyHydrate height={500}>
      <ColumnOne
        maxWidth={maxWidth}
        className={classes.root}
        sx={sx}
      >
        {product.description && (
          <Box
            className={`${classes.description} ${styles.descriptionWrapper}`}
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{ __html: product.description.html }}
            sx={[
              {
                // Basic typography styles for compatibility
                '& p:first-of-type': {
                  marginTop: 0,
                },
              },
              fontSize === 'responsive' &&
                ((theme) => ({
                  '& p, & li': {
                    ...breakpointVal('fontSize', 16, 30, theme.breakpoints.values),
                  },
                })),
              fontSize !== 'responsive' && {
                '& p, & li': {
                  fontSize,
                },
              },
            ]}
          />
        )}
      </ColumnOne>
    </LazyHydrate>
  )
} 