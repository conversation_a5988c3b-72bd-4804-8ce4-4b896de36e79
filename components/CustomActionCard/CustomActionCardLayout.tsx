import type { BoxProps } from '@mui/material'
import { Box } from '@mui/material'
import React from 'react'
import { extendableComponent } from '@graphcommerce/next-ui'

export type CustomActionCardLayoutProps = {
  children?: React.ReactNode
  layout?: 'list' | 'grid' | 'stack' | 'inline'
} & Pick<BoxProps, 'sx' | 'className' | 'tabIndex'>

const parts = ['root'] as const
const name = 'CustomActionCardLayout'
const { withState } = extendableComponent<
  Pick<CustomActionCardLayoutProps, 'layout'>,
  typeof name,
  typeof parts
>(name, parts)

export const CustomActionCardLayout = React.forwardRef<HTMLDivElement, CustomActionCardLayoutProps>(
  (props, ref) => {
    const { layout = 'list', sx, className = '', ...boxProps } = props

    const classes = withState({ layout })

    return (
      <Box
        ref={ref}
        {...boxProps}
        className={`${classes.root} ${className}`}
        sx={[
          (theme) => ({
            '&.layoutStack': {
              display: 'grid',
              height: 'min-content',
              gap: theme.spacings.xxs,
            },
            '&.layoutList': {
              display: 'grid',
              height: 'min-content',
              pt: '1px',
            },
            '&.layoutGrid': {
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: '6px',
            },
            '&.layoutInline': {
              display: 'flex',
              flexWrap: 'wrap',
              gap: theme.spacings.xxs,
            },
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      />
    )
  },
) 