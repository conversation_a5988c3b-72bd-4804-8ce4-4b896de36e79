import { useEffect, useRef } from 'react'

export interface TawkToChatProps {
  /**
   * Tawk.to Property ID (the first part of the embed code)
   * Example: '686fc14861b9c5190dd25f8c'
   */
  propertyId?: string

  /**
   * Tawk.to Widget ID (the second part of the embed code)
   * Example: '1ivq8m1u7'
   */
  widgetId?: string

  /**
   * Whether to enable the chat widget
   * @default true
   */
  enabled?: boolean

  /**
   * Custom configuration for Tawk.to
   */
  customConfig?: {
    /**
     * Hide the widget on specific pages
     */
    hideOnPages?: string[]

    /**
     * Custom position for the widget
     */
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'

    /**
     * Custom styling
     */
    customStyle?: {
      visibility?: {
        desktop?: boolean
        mobile?: boolean
      }
    }
  }
}

declare global {
  interface Window {
    Tawk_API?: any
    Tawk_LoadStart?: Date
    tawkToLoaded?: boolean
  }
}

export function TawkToChat({
  propertyId = '686fc14861b9c5190dd25f8c',
  widgetId = '1ivq8m1u7',
  enabled = true,
  customConfig
}: TawkToChatProps) {
  const scriptLoadedRef = useRef(false)

  useEffect(() => {
    // Don't load if disabled or if running on server
    if (!enabled || typeof window === 'undefined') {
      return
    }

    // Check if current page should hide the widget
    if (customConfig?.hideOnPages?.some(page =>
      window.location.pathname.includes(page)
    )) {
      return
    }

    // Don't load if already loaded
    if (window.tawkToLoaded || scriptLoadedRef.current) {
      return
    }

    // Mark as loading to prevent duplicate loads
    scriptLoadedRef.current = true
    window.tawkToLoaded = true

    try {
      // Use the official Tawk.to integration method
      const loadTawkTo = () => {
        // Initialize Tawk_API and Tawk_LoadStart before script loads
        window.Tawk_API = window.Tawk_API || {}
        window.Tawk_LoadStart = new Date()

        // Create script element
        const script = document.createElement('script')
        script.async = true
        script.src = `https://embed.tawk.to/${propertyId}/${widgetId}`
        script.charset = 'UTF-8'
        script.setAttribute('crossorigin', '*')

        // Add error handling
        script.onerror = (error) => {
          console.error('Failed to load Tawk.to script:', error)
          scriptLoadedRef.current = false
          window.tawkToLoaded = false
        }

        // Add load success handler
        script.onload = () => {
          console.log('Tawk.to script loaded successfully')

          // Apply custom configuration after a short delay to ensure Tawk.to is fully initialized
          setTimeout(() => {
            applyCustomConfiguration()
          }, 1000)
        }

        // Insert script into document
        const firstScript = document.getElementsByTagName('script')[0]
        if (firstScript && firstScript.parentNode) {
          firstScript.parentNode.insertBefore(script, firstScript)
        } else {
          document.head.appendChild(script)
        }
      }

      // Apply custom configuration
      const applyCustomConfiguration = () => {
        if (!customConfig || !window.Tawk_API) return

        try {
          // Wait for Tawk_API to be fully ready
          if (window.Tawk_API.onLoad) {
            window.Tawk_API.onLoad = function() {
              applyVisibilitySettings()
            }
          } else {
            // If onLoad is not available, apply settings directly
            applyVisibilitySettings()
          }
        } catch (error) {
          console.warn('Error applying Tawk.to custom configuration:', error)
        }
      }

      // Apply visibility settings
      const applyVisibilitySettings = () => {
        if (!customConfig?.customStyle?.visibility || !window.Tawk_API) return

        try {
          const { desktop, mobile } = customConfig.customStyle.visibility

          // Check current device type
          const isMobile = window.matchMedia('(max-width: 767px)').matches

          if ((isMobile && mobile === false) || (!isMobile && desktop === false)) {
            if (typeof window.Tawk_API.hideWidget === 'function') {
              window.Tawk_API.hideWidget()
            }
          }
        } catch (error) {
          console.warn('Error applying Tawk.to visibility settings:', error)
        }
      }

      // Load the script
      loadTawkTo()

    } catch (error) {
      console.error('Error initializing Tawk.to:', error)
      scriptLoadedRef.current = false
      window.tawkToLoaded = false
    }

    // Cleanup function
    return () => {
      // Reset loading state if component unmounts
      scriptLoadedRef.current = false
    }
  }, [propertyId, widgetId, enabled, customConfig])

  // This component doesn't render anything visible
  return null
}

export default TawkToChat
