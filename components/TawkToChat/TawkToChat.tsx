import { useEffect } from 'react'

export interface TawkToChatProps {
  /**
   * Tawk.to Property ID (the first part of the embed code)
   * Example: '686fc14861b9c5190dd25f8c'
   */
  propertyId?: string
  
  /**
   * Tawk.to Widget ID (the second part of the embed code)
   * Example: '1ivq8m1u7'
   */
  widgetId?: string
  
  /**
   * Whether to enable the chat widget
   * @default true
   */
  enabled?: boolean
  
  /**
   * Custom configuration for Tawk.to
   */
  customConfig?: {
    /**
     * Hide the widget on specific pages
     */
    hideOnPages?: string[]
    
    /**
     * Custom position for the widget
     */
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
    
    /**
     * Custom styling
     */
    customStyle?: {
      visibility?: {
        desktop?: boolean
        mobile?: boolean
      }
    }
  }
}

declare global {
  interface Window {
    Tawk_API?: any
    Tawk_LoadStart?: Date
  }
}

export function TawkToChat({
  propertyId = '686fc14861b9c5190dd25f8c',
  widgetId = '1ivq8m1u7',
  enabled = true,
  customConfig
}: TawkToChatProps) {
  useEffect(() => {
    // Don't load if disabled or if running on server
    if (!enabled || typeof window === 'undefined') {
      return
    }

    // Check if current page should hide the widget
    if (customConfig?.hideOnPages?.some(page => 
      window.location.pathname.includes(page)
    )) {
      return
    }

    // Don't load if already loaded
    if (window.Tawk_API) {
      return
    }

    // Initialize Tawk_API and Tawk_LoadStart
    window.Tawk_API = window.Tawk_API || {}
    window.Tawk_LoadStart = new Date()

    // Create and append the script
    const script = document.createElement('script')
    script.async = true
    script.src = `https://embed.tawk.to/${propertyId}/${widgetId}`
    script.charset = 'UTF-8'
    script.setAttribute('crossorigin', '*')
    
    // Find the first script tag and insert before it
    const firstScript = document.getElementsByTagName('script')[0]
    if (firstScript && firstScript.parentNode) {
      firstScript.parentNode.insertBefore(script, firstScript)
    } else {
      // Fallback: append to head
      document.head.appendChild(script)
    }

    // Apply custom configuration after widget loads
    if (customConfig) {
      script.onload = () => {
        if (window.Tawk_API) {
          // Apply custom styling
          if (customConfig.customStyle?.visibility) {
            const { desktop, mobile } = customConfig.customStyle.visibility
            
            // Hide on desktop if specified
            if (desktop === false) {
              window.Tawk_API.onLoad = function() {
                const mediaQuery = window.matchMedia('(min-width: 768px)')
                if (mediaQuery.matches) {
                  window.Tawk_API.hideWidget()
                }
              }
            }
            
            // Hide on mobile if specified
            if (mobile === false) {
              window.Tawk_API.onLoad = function() {
                const mediaQuery = window.matchMedia('(max-width: 767px)')
                if (mediaQuery.matches) {
                  window.Tawk_API.hideWidget()
                }
              }
            }
          }
          
          // Apply custom position (this would require additional Tawk.to API calls)
          if (customConfig.position) {
            // Note: Position customization might require Tawk.to Pro features
            console.log(`Tawk.to position set to: ${customConfig.position}`)
          }
        }
      }
    }

    // Cleanup function
    return () => {
      // Note: Tawk.to doesn't provide a clean way to remove the widget
      // The script will remain loaded for the session
    }
  }, [propertyId, widgetId, enabled, customConfig])

  // This component doesn't render anything visible
  return null
}

export default TawkToChat
