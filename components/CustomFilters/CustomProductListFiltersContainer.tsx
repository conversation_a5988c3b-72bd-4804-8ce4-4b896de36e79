import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rovider } from '@graphcommerce/framer-scroller'
import {
  IconSvg,
  extendableComponent,
  iconChevronLeft,
  iconChevronRight,
  useScrollY,
} from '@graphcommerce/next-ui'
import type { SxProps, Theme } from '@mui/material'
import { Box, styled } from '@mui/material'
import { m, useTransform } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'

const MotionDiv = styled(m.div)({})

export type CustomProductListFiltersContainerProps = {
  children: React.ReactNode
  sx?: SxProps<Theme>
}

type OwnerState = {
  isSticky: boolean
}

const name = 'CustomProductListFiltersContainer'
const parts = [
  'wrapper',
  'container',
  'shadow',
  'containerSticky',
  'scroller',
  'scrollerSticky',
  'sliderPrev',
  'sliderNext',
] as const

const { withState } = extendableComponent<OwnerState, typeof name, typeof parts>(name, parts)

export function CustomProductListFiltersContainer(props: CustomProductListFiltersContainerProps) {
  const { children, sx = [] } = props
  const scrollY = useScrollY()

  const [isSticky, setIsSticky] = useState<boolean>(false)
  const [startPosition, setStartPosition] = useState<number>(100)
  const [spacing, setSpacing] = useState<number>(20)

  const scrollHalfway = startPosition + spacing

  const wrapperRef = useRef<HTMLDivElement>(null)
  const classes = withState({ isSticky })

  // Measure the sizing of the wrapping container
  useEffect(() => {
    const observer = new ResizeObserver(([entry]) => {
      if (window.scrollY > 100) return
      const offset = wrapperRef.current?.getBoundingClientRect()?.top ?? 0
      const elemHeigh = entry.contentRect.height
      const nextOffset =
        (
          (wrapperRef.current?.parentElement?.nextElementSibling ??
            wrapperRef.current?.parentElement?.parentElement
              ?.nextElementSibling) as HTMLElement | null
        )?.getBoundingClientRect()?.top ?? 0
      const modifier = 5

      setSpacing((nextOffset - elemHeigh - offset + 20) * modifier)
      setStartPosition(offset)
    })
    if (wrapperRef.current) observer.observe(wrapperRef.current)
    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const onCheckStickyChange = (v: number) => {
      if (isSticky && v <= scrollHalfway) {
        setIsSticky(false)
      }
      if (!isSticky && v > scrollHalfway) {
        setIsSticky(true)
      }
    }
    onCheckStickyChange(scrollY.get())
    return scrollY.on('change', onCheckStickyChange)
  }, [isSticky, scrollHalfway, scrollY])

  const opacity = useTransform(scrollY, [startPosition, startPosition + spacing], [0, 1])

  return (
    <MotionDiv
      className={classes.wrapper}
      ref={wrapperRef}
      data-custom-component="CustomProductListFiltersContainer"
      sx={[
        (theme) => ({
          display: 'flex',
          justifyContent: 'center',
          marginBottom: theme.spacings.sm,
          position: 'sticky',
          top: theme.page.vertical,
          zIndex: 9,
          margin: '0 auto',

          [theme.breakpoints.down('md')]: {
            textAlign: 'center',
            maxWidth: 'unset',
            margin: `0 calc(${theme.page.horizontal} * -1)`,
            // Debug: Add a visible indicator
            background: '#fff',
            border: '3px solid red !important',
          },
          

        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      <ScrollerProvider scrollSnapAlign='none'>
        <ScrollerButton
          direction='left'
          className={classes.sliderPrev}
          size='small'
          sx={(theme) => ({
            position: 'absolute',
            top: 4,
            left: 2,
            zIndex: 10,
            // Hide scroll buttons on mobile since we're using flexbox wrapping
            [theme.breakpoints.down('md')]: {
              display: 'none',
            },
          })}
        >
          <IconSvg src={iconChevronLeft} />
        </ScrollerButton>
        <Box
          className={classes.container}
          sx={(theme) => ({
            position: 'relative',
            maxWidth: '100%',
            padding: '0px',
            paddingLeft: 0,
            paddingRight: 0,
            [theme.breakpoints.up('md')]: {
              background: theme.palette.background.default,
              borderRadius: '99em',
            },
            display: 'grid',
            // alignItems: 'center',
          })}
        >
          <Scroller
            className={classes.scroller}
            hideScrollbar
                      sx={(theme) => ({
            px: theme.page.horizontal,
            paddingBottom: '1px',
            [theme.breakpoints.up('md')]: {
              borderRadius: '99em',
              paddingLeft: '8px',
              paddingRight: '8px',
            },
            py: '5px',
            columnGap: '6px',
            gridAutoColumns: 'min-content',
            // Mobile: Override grid layout with flexbox wrapping
            [theme.breakpoints.down('md')]: {
              display: 'flex !important',
              flexWrap: 'wrap',
              gap: '6px',
              justifyContent: 'flex-start',
              // alignItems: 'center',
              overflow: 'visible !important',
              gridAutoFlow: 'unset !important',
              gridAutoColumns: 'unset !important',
              '& > *': {
                flexShrink: 0,
                scrollSnapAlign: 'unset !important',
                scrollSnapStop: 'unset !important',
              },
            },

          })}
          >
            {children}
          </Scroller>
          <MotionDiv
            className={classes.shadow}
            style={{ opacity }}
            sx={(theme) => ({
              pointerEvents: 'none',
              zindex: '-1',
              borderRadius: '99em',
              position: 'absolute',
              height: '100%',
              width: '100%',
              top: 0,
              boxShadow: theme.shadows[6],
              [theme.breakpoints.down('md')]: {
                boxShadow: 'none !important',
              },
            })}
          />
        </Box>
        <ScrollerButton
          direction='right'
          className={classes.sliderNext}
          size='small'
          sx={(theme) => ({
            position: 'absolute',
            top: 4,
            right: 2,
            zIndex: 10,
            // Hide scroll buttons on mobile since we're using flexbox wrapping
            [theme.breakpoints.down('md')]: {
              display: 'none',
            },
          })}
        >
          <IconSvg src={iconChevronRight} />
        </ScrollerButton>
      </ScrollerProvider>
    </MotionDiv>
  )
}
