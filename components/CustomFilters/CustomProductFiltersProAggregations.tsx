import { ProductFiltersProAggregations, productFiltersProSectionRenderer } from '@graphcommerce/magento-product'
import { Box } from '@mui/material'
import { styled } from '@mui/material/styles'

// Custom styled wrapper for the filters with custom colors
const StyledFiltersWrapper = styled(Box)(({ theme }) => ({
  // Custom styles for filter titles
  '& .MuiAccordionSummary-content': {
    // color: '#1976d2', // Custom primary color for titles
    fontWeight: 600,
    fontSize: '16px' ,
  },
  
  // Custom styles for filter text
  '& .MuiTypography-root': {
    color: '#333333', // Custom text color
  },
  
  // Custom styles for Clear buttons
  '& .MuiButton-root': {
    color: '#d32f2f', // Custom color for Clear buttons
    '&:hover': {
      // backgroundColor: 'rgba(211, 47, 47, 0.04)',
    },
  },
  
  // Custom styles for filter options
  '& .ActionCard-title': {
    color: '#424242', // Custom color for filter option text
    '&.selected': {
      color: '#1976d2', // Custom color for selected options
      fontWeight: 450,
    },
  },
  
  // Apply font size to all filter options
  '& .ActionCard-root': {
    fontSize: '14px',
    '& .ActionCard-title': {
      fontSize: '14px',
    },
    '& .MuiTypography-root': {
      fontSize: '14px',
    },
  },
  
  // Custom styles for count badges
  '& .MuiTypography-caption': {
    // color: '#757575', // Custom color for count
  },
  
  // Custom styles for sliders (price range)
  '& .MuiSlider-root': {
    color: '#1976d2', // Custom slider color
    '& .MuiSlider-thumb': {
      // backgroundColor: '#1976d2',
      width: '16px',
      height: '16px',
    },
    '& .MuiSlider-track': {
      // backgroundColor: '#1976d2',
    },
    '& .MuiSlider-rail': {
      // backgroundColor: '#e0e0e0',
    },
  },
  
  // Custom styles for input fields
  '& .MuiTextField-root': {
    '& .MuiInputLabel-root': {
      color: '#666666', // Custom label color
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#e0e0e0',
      },
      '&:hover fieldset': {
        borderColor: '#1976d2',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#1976d2',
      },
    },
  },
  
  // Custom styles for chips (selected filters, Sort By, Per Page)
  '& .MuiChip-root': {
    '& .MuiChip-label': {
      fontSize: '14px !important',
    },
  },
  
  // Custom styles for range filter chips
}))

export type CustomProductFiltersProAggregationsProps = {
  renderer?: any
}

export function CustomProductFiltersProAggregations(props: CustomProductFiltersProAggregationsProps) {
  const { renderer = productFiltersProSectionRenderer } = props

  return (
    <StyledFiltersWrapper>
      <ProductFiltersProAggregations renderer={renderer} />
    </StyledFiltersWrapper>
  )
}
