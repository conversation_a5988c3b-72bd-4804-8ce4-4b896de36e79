import type { ActionCardRequireOptionSelection } from '@graphcommerce/ecommerce-ui'
import type { AddToCartItemSelector } from '@graphcommerce/magento-product'
import { useFormAddProductsToCart } from '@graphcommerce/magento-product'
import type { ActionCardListProps } from '@graphcommerce/next-ui'
import { filterNonNullableKeys, useLocale } from '@graphcommerce/next-ui'
import { i18n } from '@lingui/core'
import type { SxProps, Theme } from '@mui/material'
import { Box } from '@mui/material'
import React, { useEffect, useMemo } from 'react'
import type { CustomConfigurableOptionsFragment } from './graphql/ConfigurableOptions.gql'
import { useConfigurableOptionsSelection } from './hooks/useConfigurableOptionsSelection'
import { ConfigurableOptionValue } from './components/ConfigurableOptionValue/ConfigurableOptionValue'
import { ConfigurableProductOption } from './ConfigurableProductOption'

export type ConfigurableProductOptionsProps = AddToCartItemSelector & {
  optionStartLabels?: Record<string, React.ReactNode>
  optionEndLabels?: Record<string, React.ReactNode>
  sx?: SxProps<Theme>
  render?: typeof ConfigurableOptionValue
  product: CustomConfigurableOptionsFragment
} & Pick<ActionCardListProps, 'color' | 'variant' | 'size' | 'layout' | 'collapse'> &
  ActionCardRequireOptionSelection

export function ConfigurableProductOptions(props: ConfigurableProductOptionsProps) {
  const {
    optionStartLabels,
    optionEndLabels,
    sx,
    render = ConfigurableOptionValue,
    product,
    index = 0,
    ...other
  } = props
  const { setError, clearErrors } = useFormAddProductsToCart()

  const options = filterNonNullableKeys(product.configurable_options, [
    'attribute_code',
    'label',
    'values',
  ])

  const { configured } = useConfigurableOptionsSelection({ url_key: product.url_key, index })
  const unavailable =
    configured &&
    (configured?.configurable_product_options_selection?.options_available_for_selection ?? [])
      .length === 0

  const locale = useLocale()
  const allLabels = useMemo(() => {
    const formatter = new Intl.ListFormat(locale, {
      style: 'long',
      type: 'conjunction',
    })
    return formatter.format(options.map((o) => o.label))
  }, [locale, options])

  useEffect(() => {
    if (unavailable) {
      setError(`cartItems.${index}.sku`, {
        message: i18n._(/* i18n */ 'Product not available in {allLabels}', { allLabels }),
      })
    }
    if (!unavailable) clearErrors(`cartItems.${index}.sku`)
  }, [allLabels, clearErrors, index, setError, unavailable])

  return (
    <Box sx={(theme) => ({ 
      display: 'grid', 
      rowGap: '8px',
      // border: '1px solid', 
      // borderColor: theme.palette.divider,
      // padding: '16px',
      // borderRadius: '12px',
      // backgroundColor: theme.palette.background.paper,
      // boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
      // Global ActionCard height control
      '& .ActionCard-root': {
        minHeight: '32px !important',
        maxHeight: '32px !important',
        // padding: '5px 12px !important',
        display: 'flex !important',
        alignItems: 'center !important',
      },
      '& .ActionCard-rootInner': {
        minHeight: 'unset !important',
        display: 'flex !important',
        alignItems: 'center !important',
        justifyContent: 'center !important',
        width: '100% !important',
      },
      '& .ActionCard-title': {
        display: 'flex !important',
        alignItems: 'center !important',
        justifyContent: 'center !important',
        margin: '0 !important',
        lineHeight: '1 !important',
      },
      '& .MuiBox-root': {
        '& .SectionHeader-root': {
          marginBottom: '2px',
        }
      }
    })}>
      {options.map((option, optionIndex) => (
        <ConfigurableProductOption
          {...option}
          key={option.uid}
          render={render}
          optionStartLabels={optionStartLabels}
          optionEndLabels={optionEndLabels}
          index={index}
          optionIndex={optionIndex}
          sx={sx}
          url_key={product.url_key}
          {...other}
        />
      ))}
    </Box>
  )
} 