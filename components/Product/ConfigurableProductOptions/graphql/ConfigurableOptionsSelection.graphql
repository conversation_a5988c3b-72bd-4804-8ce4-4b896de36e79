fragment CustomConfigurableOptionsSelection on ConfigurableProduct {
  __typename
  uid
  name
  url_key
  configurable_product_options_selection(
    configurableOptionValueUids: $selectedOptions
  ) {
    variant {
      uid
      name
      sku
      price_range {
        minimum_price {
          final_price {
            ...Money
          }
          regular_price {
            ...Money
          }
        }
      }
    }
    options_available_for_selection {
      attribute_code
      option_value_uids
    }
  }
}