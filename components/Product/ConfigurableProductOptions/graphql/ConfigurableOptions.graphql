fragment CustomConfigurableOptions on ConfigurableProduct {
  __typename
  uid
  sku
  name
  url_key
  price_range {
    minimum_price {
      final_price {
        ...Money
      }
      regular_price {
        ...Money
      }
    }
  }
  configurable_options {
    attribute_code
    uid
    label
    position
    use_default
    values {
      ...CustomConfigurableOptionValue
    }
  }
  configurable_product_options_selection {
    options_available_for_selection {
      attribute_code
      option_value_uids
    }
  }
  ...ProductPageDescription
  variants {
    attributes {
      code
      uid
    }
    product {
      uid
    }
  }
} 