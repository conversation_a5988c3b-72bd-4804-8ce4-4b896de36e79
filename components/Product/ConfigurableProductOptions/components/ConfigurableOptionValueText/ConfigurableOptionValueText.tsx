import type { ActionCardItemRenderProps } from '@graphcommerce/ecommerce-ui'
import { ActionCard } from '@graphcommerce/next-ui'
import type { CustomConfigurableOptionValueTextFragment } from './ConfigurableOptionValueText.gql'

export type ConfigurableOptionValueTextProps =
  ActionCardItemRenderProps<CustomConfigurableOptionValueTextFragment>

export function ConfigurableOptionValueText(props: ConfigurableOptionValueTextProps) {
  const {
    swatch_data,
    store_label,
    uid,
    use_default_value,
    size,
    selected,
    value,
    onReset,
    onClick,
    ...actionCardProps
  } = props

  if (swatch_data && swatch_data?.__typename !== 'TextSwatchData')
    throw Error(`ConfigurableOptionValueText can not render a ${swatch_data?.__typename}`)

  const title = swatch_data?.value ?? store_label
  const details = undefined

  return (
    <ActionCard
      {...actionCardProps}
      size={size}
      title={title}
      details={details}
      selected={selected}
      value={value}
      onClick={
        onClick &&
        ((e) => {
          if (selected) {
            onReset(e)
          } else {
            onClick(e, value)
          }
        })
      }
      sx={{
        minHeight: '32px', // Reduced from 40px
        maxHeight: '32px', // Added to enforce height
        padding: '6px 12px', // Reduced from 8px 16px
        '& .ActionCard-rootInner': {
          justifyContent: 'center',
          minHeight: 'unset', // Remove any inherited minHeight
        },
        '& .ActionCard-root': {
          padding: '0px',
        },
        '& .ActionCard-title': {
          fontSize: '0.875rem',
          fontWeight: 500,
          lineHeight: 1.2,
        },
        ...actionCardProps.sx,
      }}
    />
  )
} 