{"name": "@graphcommerce/magento-graphcms", "homepage": "https://www.graphcommerce.org/", "repository": "github:graphcommerce-org/graphcommerce", "version": "9.0.4", "private": true, "sideEffects": false, "packageManager": "yarn@4.5.3", "engines": {"node": ">=18.19.0 <22.0.0"}, "scripts": {"dev": "concurrently -k -n codegen,next 'gc-gql-codegen -w' 'next dev' -c 'magenta,cyan'", "codegen": "graphcommerce codegen && gc-mesh build && gc-gql-codegen", "build": "graphcommerce codegen && gc-mesh build && gc-gql-codegen && next build", "start": "next start", "tsc:lint": "tsc --noEmit -p .", "tsc:perf": "NODE_OPTIONS=--max_old_space_size=10000 tsc --noEmit --generateTrace tsctrace --incremental false && npx @typescript/analyze-trace tsctrace", "lingui": "cross-env NODE_ENV=development lingui extract --clean", "postinstall": "is-monorepo '[pkgrun] postinstall' '[pkgrun] patch-package'", "create-patch": "patch-package --exclude 'package.json$|gql.ts$|interceptor.tsx$'"}, "dependencies": {"@apollo/client": "~3.12.3", "@emotion/cache": "^11.13.1", "@emotion/react": "11.12.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.13.0", "@graphcommerce/cli": "9.0.4", "@graphcommerce/demo-magento-graphcommerce": "9.0.4", "@graphcommerce/ecommerce-ui": "9.0.4", "@graphcommerce/framer-next-pages": "9.0.4", "@graphcommerce/framer-scroller": "9.0.4", "@graphcommerce/framer-utils": "9.0.4", "@graphcommerce/google-datalayer": "9.0.4", "@graphcommerce/google-playstore": "9.0.4", "@graphcommerce/googleanalytics": "9.0.4", "@graphcommerce/googlerecaptcha": "9.0.4", "@graphcommerce/googletagmanager": "9.0.4", "@graphcommerce/graphql": "9.0.4", "@graphcommerce/graphql-codegen-near-operation-file": "9.0.4", "@graphcommerce/graphql-codegen-relay-optimizer-plugin": "9.0.4", "@graphcommerce/graphql-mesh": "9.0.4", "@graphcommerce/hygraph-cli": "9.0.4", "@graphcommerce/hygraph-dynamic-rows": "9.0.4", "@graphcommerce/hygraph-ui": "9.0.4", "@graphcommerce/image": "9.0.4", "@graphcommerce/lingui-next": "9.0.4", "@graphcommerce/magento-cart": "9.0.4", "@graphcommerce/magento-cart-billing-address": "9.0.4", "@graphcommerce/magento-cart-checkout": "9.0.4", "@graphcommerce/magento-cart-coupon": "9.0.4", "@graphcommerce/magento-cart-email": "9.0.4", "@graphcommerce/magento-cart-items": "9.0.4", "@graphcommerce/magento-cart-payment-method": "9.0.4", "@graphcommerce/magento-cart-shipping-address": "9.0.4", "@graphcommerce/magento-cart-shipping-method": "9.0.4", "@graphcommerce/magento-category": "9.0.4", "@graphcommerce/magento-cms": "9.0.4", "@graphcommerce/magento-compare": "9.0.4", "@graphcommerce/magento-customer": "9.0.4", "@graphcommerce/magento-graphql": "9.0.4", "@graphcommerce/magento-graphql-rest": "9.0.4", "@graphcommerce/magento-newsletter": "9.0.4", "@graphcommerce/magento-payment-included": "9.0.4", "@graphcommerce/magento-product": "9.0.4", "@graphcommerce/magento-product-bundle": "9.0.4", "@graphcommerce/magento-product-configurable": "9.0.4", "@graphcommerce/magento-product-downloadable": "9.0.4", "@graphcommerce/magento-product-grouped": "9.0.4", "@graphcommerce/magento-product-simple": "9.0.4", "@graphcommerce/magento-product-virtual": "9.0.4", "@graphcommerce/magento-recently-viewed-products": "9.0.4", "@graphcommerce/magento-review": "9.0.4", "@graphcommerce/magento-search": "9.0.4", "@graphcommerce/magento-store": "9.0.4", "@graphcommerce/magento-wishlist": "9.0.4", "@graphcommerce/next-config": "9.0.4", "@graphcommerce/next-ui": "9.0.4", "@graphcommerce/react-hook-form": "9.0.4", "@graphcommerce/service-worker": "9.0.4", "@graphql-mesh/cli": "0.99.2", "@graphql-mesh/config": "0.107.2", "@graphql-mesh/cross-helpers": "0.4.10", "@graphql-mesh/graphql": "0.103.22", "@graphql-mesh/http": "0.105.23", "@graphql-mesh/json-schema": "0.108.24", "@graphql-mesh/openapi": "0.108.25", "@graphql-mesh/plugin-http-details-extensions": "0.103.22", "@graphql-mesh/runtime": "0.105.23", "@graphql-mesh/transform-encapsulate": "0.103.21", "@graphql-mesh/transform-filter-schema": "0.103.21", "@graphql-mesh/transform-hoist-field": "0.104.2", "@graphql-mesh/transform-naming-convention": "0.104.2", "@graphql-mesh/transform-prefix": "0.104.2", "@graphql-mesh/transform-prune": "0.103.21", "@graphql-mesh/transform-rename": "0.104.2", "@graphql-mesh/transform-replace-field": "0.104.2", "@graphql-mesh/transform-type-merging": "0.103.21", "@graphql-mesh/types": "0.103.21", "@graphql-mesh/utils": "0.103.21", "@lingui/conf": "4.14.1", "@lingui/core": "4.14.1", "@lingui/loader": "4.14.1", "@lingui/macro": "4.14.1", "@lingui/react": "4.14.1", "@lingui/swc-plugin": "4.1.0", "@mui/icons-material": "^7.1.1", "@mui/lab": "5.0.0-alpha.173", "@mui/material": "5.16.8", "@mui/utils": "^5.16.8", "@next/env": "15.1.0", "@parcel/watcher": "^2.5.0", "@serwist/next": "^9.0.11", "@unts/patch-package": "^8.0.0", "concurrently": "8.2.2", "cross-env": "^7.0.3", "dotenv": "16.4.7", "framer-motion": "11.15.0", "graphql": "^16.10.0", "next": "15.1.0", "next-sitemap": "4.2.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "serwist": "^9.0.11", "sharp": "0.33.5", "ts-node": "^10.9.2", "webpack": "^5.97.1"}, "devDependencies": {"@graphcommerce/eslint-config-pwa": "9.0.4", "@graphcommerce/prettier-config-pwa": "9.0.4", "@graphcommerce/typescript-config-pwa": "9.0.4", "@lingui/cli": "4.14.1", "@playwright/test": "1.49.1", "@types/node": "^18.19.68", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/react-is": "^18.3.1", "babel-plugin-macros": "^3.1.0", "eslint": "^8.57.1", "prettier": "^3", "type-fest": "^4.30.1", "typescript": "5.7.2"}, "browserslist": ["> 1% in alt-EU", "not IE 11"], "prettier": "@graphcommerce/prettier-config-pwa/classic.js", "eslintConfig": {"extends": "@graphcommerce/eslint-config-pwa", "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/consistent-type-imports": "off"}}}