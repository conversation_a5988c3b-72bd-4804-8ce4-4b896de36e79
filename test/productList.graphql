query ProductList($pageSize: Int = 24, $currentPage: Int = 1, $filters: ProductAttributeFilterInput = {}, $sort: ProductAttributeSortInput = {}, $search: String = "", $onlyItems: Boolean = false) {
  products(
    pageSize: $pageSize
    currentPage: $currentPage
    filter: $filters
    sort: $sort
    search: $search
  ) {
    items {
      __typename
      uid
      url_key
      sku
      name
      small_image {
        url
        label
        disabled
        __typename
      }
      price_range {
        minimum_price {
          regular_price {
            currency
            value
            __typename
          }
          discount {
            amount_off
            percent_off
            __typename
          }
          final_price {
            currency
            value
            __typename
          }
          __typename
        }
        __typename
      }
      id
      rating_summary
      ... on ConfigurableProduct {
        configurable_options {
          attribute_code
          uid
          label
          values {
            store_label
            uid
            swatch_data {
              __typename
              ... on TextSwatchData {
                value
                __typename
              }
              ... on ColorSwatchData {
                value
                __typename
              }
              ... on ImageSwatchData {
                value
                thumbnail
                __typename
              }
            }
            __typename
          }
          __typename
        }
        __typename
      }
    }
    suggestions @skip(if: $onlyItems) {
      search
      __typename
    }
    aggregations(filter: {category: {includeDirectChildrenOnly: true}}) @skip(if: $onlyItems) {
      __typename
      label
      attribute_code
      count
      options {
        __typename
        label
        value
        count
      }
    }
    page_info @skip(if: $onlyItems) {
      current_page
      total_pages
      __typename
    }
    total_count @skip(if: $onlyItems)
    sort_fields @skip(if: $onlyItems) {
      default
      options {
        label
        value
        __typename
      }
      __typename
    }
    __typename
  }
}


{
  "pageSize": 12,
  "currentPage": 1,
  "filters": {
    "category_uid": {
      "in": [
        "NDQ="
      ]
    }
    
  },
  "sort": {
    "position": "DESC"
  },
  "search": null,
  "onlyItems": false
}