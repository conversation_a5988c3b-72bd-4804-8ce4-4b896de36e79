---
description: 
globs: 
alwaysApply: false
---
---
description: 该规则解释了 Tailwind CSS 约定、实用工具类和现代 UI 开发的最佳实践。
globs: **/*.css
alwaysApply: false
---

# Tailwind CSS 规则

- 使用响应式前缀实现移动优先设计：

```html
<div class="w-full md:w-1/2 lg:w-1/3">
  <!-- 移动设备上全宽，中等屏幕上占一半，大屏幕上占三分之一 -->
</div>
```

- 为交互元素使用状态变体：

```html
<button class="bg-blue-500 hover:bg-blue-600 focus:ring-2">
  点击我
</button>
```

- 必要时使用 @apply 处理重复模式：

```css
@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600;
  }
}
```

- 对特定需求使用任意值：

```html
<div class="top-[117px] grid-cols-[1fr_2fr]">
  <!-- 自定义定位和网格布局 -->
</div>
```

- 使用间距工具实现一致的布局：

```html
<div class="space-y-4">
  <div>项目 1</div>
  <div>项目 2</div>
</div>
``` 