---
description: 
globs: 
alwaysApply: false
---
---
description: 该规则解释了 Python 编码、最佳实践、 整洁高效的代码模式.
globs: **/*.py
alwaysApply: false
---

# Python 规则

- 遵循 PEP 8 风格指南和命名约定
- 使用类型注解增强代码可读性和类型安全性
- 使用虚拟环境管理依赖：
  - 优先使用 `venv` 或 `poetry` 进行环境隔离
  - 使用 `requirements.txt` 或 `pyproject.toml` 记录依赖
- 使用上下文管理器处理资源（如文件操作）
- 优先使用列表推导式、生成器表达式和字典推导式
- 使用 `pytest` 进行测试，保持高测试覆盖率
- 使用文档字符串（docstrings）记录函数、类和模块
- 遵循面向对象设计原则（SOLID）
- 使用异常处理保证程序健壮性
- 使用 `dataclasses` 或 `pydantic` 模型表示数据