{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext", "WebWorker"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": false, "jsx": "preserve", "jsxImportSource": "@emotion/react", "incremental": true, "moduleSuffixes": [".interceptor", ""], "types": ["@serwist/next/typings"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}