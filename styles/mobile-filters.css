/* Mobile filter chips auto-wrap styles */
@media (max-width: 899px) {
  /* Target the filter container scroller on mobile - try multiple selectors */
   .ProductListFiltersContainer-scroller 
   /* [class*="scroller"], */
  /* .Scroller-root { */
  /* [class*="ProductListFiltersContainer-wrapper"] */
  {
    display: flex !important;
    flex-wrap: wrap !important;
    /* gap: 6px !important; */
    justify-content: flex-start !important;
    align-items: center !important;
    /* overflow: visible !important; */
    grid-auto-flow: unset !important;
    grid-auto-columns: unset !important;
    background-color: #eee;
  }

  .ProductListFiltersContainer-wrapper>.ProductListFiltersContainer-container
  {
    background-color: #eee;
  }

  .StickyBelowHeader-root {
    background-color: #eee;
    z-index: 9999;
  }
  

  /* Ensure filter chips don't shrink and have proper spacing */
  /* .ProductListFiltersContainer-scroller > *,
  [class*="ProductListFiltersContainer-scroller"] > *,
  [class*="scroller"] > *,
  .Scroller-root > * {
    flex-shrink: 0 !important;
    scroll-snap-align: unset !important;
    scroll-snap-stop: unset !important;
  } */

  /* Hide scroll buttons on mobile since we're using flexbox wrapping */
  /* .ProductListFiltersContainer-sliderPrev,
  .ProductListFiltersContainer-sliderNext,
  [class*="sliderPrev"],
  [class*="sliderNext"] {
    display: none !important;
  } */

  /* Remove shadow on mobile for cleaner look */
  /* .ProductListFiltersContainer-shadow,
  [class*="shadow"] {
    box-shadow: none !important;
  } */

  /* Additional debug styles to see if CSS is loading */
  /* body { */
    /* border-top: 3px solid red !important; */
  /* } */
}


