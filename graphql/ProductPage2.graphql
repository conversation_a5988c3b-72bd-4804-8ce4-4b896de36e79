# import CustomConfigurableOptions from '../components/Product/ConfigurableProductOptions/graphql/ConfigurableOptions.graphql'

query ProductPage2(
  $urlKey: String!
  $reviewPageSize: Int = 3
  $reviewPage: Int = 1
  $context: PrivateContext
  $useCustomAttributes: Boolean = false
) {
  products(filter: { url_key: { eq: $urlKey } }) @privateContext(context: $context) {
    ...ProductSpecs
    items {
      __typename
      uid
      ...ProductCustomizable
      ...ProductWeight
      ...ProductPageItem
      ...ProductPageTabs
      ...CustomConfigurableOptions
      ...DownloadableProductOptions
      ...BundleProductOptions
      ...GroupedProduct
    }
  }
  # Workaround for https://github.com/magento/magento2/issues/32427
  relatedUpsells: products(filter: { url_key: { eq: $urlKey } })
    @privateContext(context: $context) {
    items {
      uid
      ...UpsellProducts
      ...RelatedProducts
      ...ProductReviews
    }
  }
}
