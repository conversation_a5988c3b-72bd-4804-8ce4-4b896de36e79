# GraphCommerce Magento 电商项目开发文档

## 📋 项目概述

**GraphCommerce** 是一个为构建无头电商店面而设计的 React 和 Next.js 框架。它提供了一流的示例，包括组件和实用工具，以提供高性能、高质量的电商渐进式Web应用程序（PWA）。

本项目基于 GraphCommerce 框架构建，连接 **Magento** 电商后端和 **Hygraph CMS** 内容管理系统，为开发者提供卓越的开发体验。

### 🎯 GraphCommerce 核心特性

GraphCommerce 采用世界上最优秀的开源框架和库构建，这些工具被数百万人使用，并由世界上最大的开发者社区持续发展：

- 🔧 **TypeScript 集成**：在IDE中提供即时反馈
- ⚡ **Fast Refresh**：实时开发反馈，提升开发效率
- 📦 **零配置 Webpack**：无需花费时间配置，开箱即用
- 🛡️ **TypeScript 构建检查**：构建时代码检查，防止构建失败
- 🔍 **GraphQL Playground**：GraphQL schema 探索工具
- ☁️ **Vercel 零配置托管**：轻松部署到 Vercel 平台
- 📝 **高级表单管理**：标准化的表单管理解决方案
- 🗂️ **文件系统路由**：Next.js 提供的文件系统路由
- 🎨 **电商专用组件库**：易于定制的电商组件库
- 🎬 **60fps 动画**：Framer Motion 提供流畅动画
- 🤖 **自动类型生成**：从 GraphQL schema 查询自动生成 TypeScript 定义
- 🌐 **多后端查询**：Apollo Client 和 GraphQL Mesh 无缝查询多个后端
- 🚀 **即插即用 Magento PWA**：包含的 manifest 提供即插即用的 Magento PWA

### 🏗️ 项目架构优势

- **高性能 PWA**：基于 Next.js 15.1.0 的服务端渲染和静态生成
- **完整电商功能**：购物车、结账、用户管理、产品展示、支付集成
- **多语言支持**：支持英语、德语、法语、意大利语、西班牙语、荷兰语
- **响应式设计**：Material-UI + Emotion 样式系统
- **GraphQL 优先**：统一的数据获取和状态管理
- **内容管理**：Hygraph CMS 集成，支持动态内容和页面布局

## 🏗️ 项目架构

### 核心架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Next.js)                      │
├─────────────────────────────────────────────────────────────┤
│  Pages/        │  Components/     │  Lib/                   │
│  - index.tsx   │  - Layout/       │  - graphql/             │
│  - [...url].tsx│  - ProductView/  │  - i18n/                │
│  - cart.tsx    │  - Blog/         │  - sw.ts                │
│  - checkout/   │  - GraphCMS/     │                         │
├─────────────────────────────────────────────────────────────┤
│                    GraphQL 中间层                            │
├─────────────────────────────────────────────────────────────┤
│  Magento Backend     │           Hygraph CMS                │
│  - 产品管理           │           - 内容管理                  │
│  - 订单处理           │           - 页面布局                  │
│  - 用户管理           │           - 博客文章                  │
│  - 库存管理           │           - SEO 内容                  │
└─────────────────────────────────────────────────────────────┘
```

### 项目文件结构

GraphCommerce 项目中的几乎每个文件都是为了定制而设计的。对于每个项目，修改这些文件是标准做法：

#### 🔧 核心配置文件
- `📄 graphcommerce.config.js` - GraphCommerce 主配置文件
- `📄 next.config.ts` - Next.js 配置
- `📄 tsconfig.json` - TypeScript 配置
- `📄 codegen.yml` - GraphQL 代码生成配置
- `📄 lingui.config.js` - 国际化配置

#### 📁 主要目录结构

```
my-project/
├── 📁 components/              # 可复用组件库（重点定制目录）
│   ├── Layout/                # 主要布局组件（头部、导航、页脚）
│   ├── ProductView/           # 产品详情展示组件
│   ├── ProductListItems/      # 产品列表组件
│   ├── Blog/                  # 博客相关组件
│   ├── GraphCMS/              # Hygraph CMS 组件
│   ├── Usps/                  # 特色功能组件
│   └── theme.ts               # 全局样式主题配置
├── 📁 pages/                   # 样板页面，处理 URL 路由
│   ├── api/                   # API 路由处理
│   ├── account/               # 用户账户页面
│   ├── blog/                  # 博客页面
│   ├── c/                     # 分类页面
│   ├── checkout/              # 结账页面
│   ├── customer/              # 客户管理页面
│   ├── p/                     # 产品页面
│   ├── search/                # 搜索页面
│   ├── wishlist/              # 愿望清单页面
│   ├── _app.tsx               # App 组件
│   ├── _document.tsx          # Document 组件
│   ├── index.tsx              # 首页
│   └── [...url].tsx           # 动态路由处理
├── 📁 lib/                     # 工具库和配置
│   ├── graphql/               # GraphQL 配置和客户端
│   ├── i18n/                  # 国际化配置
│   └── sw.ts                  # Service Worker 配置
├── 📁 graphql/                 # GraphQL 查询和变更定义
├── 📁 locales/                 # 自动生成的界面翻译文件
├── 📁 plugins/                 # 自定义插件目录
├── 📁 public/                  # 静态资源文件
├── 📁 patches/                 # 依赖包补丁文件
└── 📁 .yarn/                   # Yarn 包管理器配置
```

#### 🎯 关键文件说明

**🔥 重点定制文件**：
- `components/` - 最可能需要修改的组件库
- `components/Layout/` - 主要布局组件（头部、导航、页脚）
- `components/theme.ts` - 全局样式和主题定制
- `pages/` - 处理 URL 路由的样板页面
- `graphcommerce.config.js` - 项目核心配置

**🤖 自动生成文件**：
- `locales/*.po` - 由 Lingui 自动生成的翻译文件
- `lib/graphql/generated/` - 由 GraphQL Codegen 自动生成的类型文件

## 🛠️ 技术栈详解

### 前端核心框架
- **Next.js 15.1.0** - React 全栈框架，支持 SSR/SSG
- **React 18.3.1** - 用户界面库
- **TypeScript 5.7.2** - 类型安全的 JavaScript

### 状态管理与数据获取
- **Apollo Client 3.12.3** - GraphQL 客户端和状态管理
- **GraphQL 16.10.0** - 数据查询语言
- **GraphQL Mesh** - 统一多个数据源的 GraphQL 层

### UI 组件与样式
- **Material-UI 5.16.8** - React 组件库
- **Emotion 11** - CSS-in-JS 样式解决方案
- **Framer Motion 11.15.0** - 动画库

### 国际化
- **Lingui 4.14.1** - React 国际化框架
- 支持语言：英语、德语、法语、意大利语、西班牙语、荷兰语

### 构建与开发工具
- **Yarn 4.5.3** - 包管理器
- **ESLint 8.57.1** - 代码检查
- **Prettier 3** - 代码格式化
- **GraphQL Code Generator** - TypeScript 类型生成

### PWA 与性能
- **Serwist 9.0.11** - Service Worker 管理
- **Sharp 0.33.5** - 图片优化
- **Next Sitemap 4.2.3** - 站点地图生成

## 🎨 Hygraph CMS 集成

GraphCommerce 依赖 **Hygraph**（原 GraphCMS）作为默认集成的内容管理系统。Hygraph 用于所有静态内容（视频、页面、图片），提供超越 Magento CMS 功能的高质量组件。

### ✨ Hygraph 核心特性

- **📱 多语言支持**：Hygraph 内容天然支持多语言
- **🧩 组件化内容**：页面的 `content` 字段包含要显示的组件
- **🔗 智能集成**：如果页面 URL 匹配 Magento 分类页面 URL，其 `content` 会添加到分类页面
- **🖼️ 富媒体管理**：高效管理图片、视频等多媒体资源
- **📝 灵活的内容建模**：支持复杂的内容结构和关系

### 🚀 开始使用 Hygraph

1. **克隆 Hygraph Schema**：
   - 访问 [GraphCommerce Hygraph Schema](https://hygraph.com/schemas/graphcommerce)
   - 克隆官方 schema 到你的 Hygraph 项目

2. **内容管理**：
   - 你可以安全地删除所有默认内容
   - 根据项目需求创建自定义内容

3. **页面配置**：
   - 首页的 `content` 字段定义要显示的组件
   - 通过组件化的方式构建页面布局

### 📋 Hygraph 工作流程

```
Hygraph CMS → GraphQL API → GraphCommerce → 页面渲染
     ↓              ↓              ↓           ↓
   内容管理      统一查询        组件映射     用户界面
```

## 📦 依赖版本详情

### GraphCommerce 生态 (v9.0.4)
```json
{
  "@graphcommerce/cli": "9.0.4",
  "@graphcommerce/next-config": "9.0.4",
  "@graphcommerce/magento-*": "9.0.4",
  "@graphcommerce/hygraph-*": "9.0.4",
  "@graphcommerce/ecommerce-ui": "9.0.4"
}
```

### 核心 React 生态
```json
{
  "next": "15.1.0",
  "react": "18.3.1",
  "react-dom": "18.3.1",
  "typescript": "5.7.2"
}
```

### GraphQL 生态
```json
{
  "@apollo/client": "3.12.3",
  "graphql": "16.10.0",
  "@graphql-mesh/*": "0.99.2+"
}
```

## 🚀 开发环境配置

### 系统要求
- **Node.js**: 18.19.0 - 22.0.0
- **Yarn**: 4.5.3+
- **操作系统**: macOS, Linux, Windows

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd my-project
```

2. **安装依赖**
```bash
yarn install
```

3. **配置环境变量**
```bash
# 复制配置文件
cp graphcommerce.config.js.example graphcommerce.config.js

# 编辑配置文件，设置以下关键配置：
# - magentoEndpoint: Magento GraphQL 端点
# - hygraphEndpoint: Hygraph CMS 端点
# - canonicalBaseUrl: 站点基础 URL
```

4. **生成代码**
```bash
yarn codegen
```

5. **启动开发服务器**
```bash
yarn dev
```

访问 http://localhost:3000 查看应用
访问 http://localhost:3000/api/graphql 查看 GraphQL Playground

### 开发脚本

```bash
# 开发模式（自动重新生成 GraphQL 类型）
yarn dev

# 代码生成
yarn codegen

# 构建生产版本
yarn build

# 启动生产服务器
yarn start

# TypeScript 类型检查
yarn tsc:lint

# 国际化文本提取
yarn lingui

# 创建补丁文件
yarn create-patch
```

## 🔧 配置文件说明

### 1. `graphcommerce.config.js`
GraphCommerce 主配置文件：

```javascript
const config = {
  // Magento 配置
  magentoEndpoint: 'https://your-magento-site.com/graphql',
  magentoVersion: 247,
  
  // Hygraph CMS 配置
  hygraphEndpoint: 'https://your-hygraph-endpoint.com',
  
  // 多店铺配置
  storefront: [
    {
      locale: 'en',
      magentoStoreCode: 'en_GB',
      defaultLocale: true,
    }
  ],
  
  // 功能开关
  recentlyViewedProducts: { enabled: true },
  productFiltersPro: true,
  compare: true,
  
  // SEO 配置
  canonicalBaseUrl: 'https://your-domain.com',
  robotsAllow: false,
}
```

### 2. `next.config.ts`
Next.js 配置：

```typescript
const nextConfig = {
  // 图片优化配置
  images: {
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  
  // 开发配置
  onDemandEntries: {
    maxInactiveAge: 1000 * 60 * 10,
    pagesBufferLength: 10,
  },
  
  // 构建优化
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
}
```

### 3. `codegen.yml`
GraphQL 代码生成配置：

```yaml
schema:
  - ./lib/graphql/mesh/sources/Magento/schema.graphql
  - ./lib/graphql/mesh/sources/Hygraph/schema.graphql

generates:
  ./lib/graphql/generated/
```

## 🎨 组件开发指南

### 组件结构

每个组件目录通常包含：
```
ComponentName/
├── ComponentName.tsx      # 主组件
├── index.ts              # 导出文件
├── ComponentName.graphql # GraphQL 查询
└── ComponentName.gql.ts  # 生成的类型
```

### 创建新组件示例

```tsx
// components/MyComponent/MyComponent.tsx
import { Box, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'

const StyledContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
}))

interface MyComponentProps {
  title: string
  content: string
}

export function MyComponent({ title, content }: MyComponentProps) {
  return (
    <StyledContainer>
      <Typography variant="h4" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body1">
        {content}
      </Typography>
    </StyledContainer>
  )
}
```

### GraphQL 查询组件

```tsx
// components/ProductList/ProductList.tsx
import { useQuery } from '@apollo/client'
import { ProductListDocument, ProductListQuery } from './ProductList.gql'

export function ProductList() {
  const { data, loading, error } = useQuery<ProductListQuery>(ProductListDocument)

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      {data?.products?.items?.map((product) => (
        <div key={product?.id}>
          {product?.name}
        </div>
      ))}
    </div>
  )
}
```

## 🌐 国际化开发

### 添加新文本

1. **在组件中使用 Lingui**：
```tsx
import { Trans, t } from '@lingui/macro'

function MyComponent() {
  return (
    <div>
      <Trans>Welcome to our store</Trans>
      <input placeholder={t`Search products...`} />
    </div>
  )
}
```

2. **提取文本**：
```bash
yarn lingui
```

3. **翻译文本**：
编辑 `locales/*.po` 文件添加翻译

### 添加新语言

1. 在 `graphcommerce.config.js` 中添加新的 storefront 配置
2. 在 `lingui.config.js` 中添加新的 locale
3. 创建对应的 `.po` 文件

## 📱 PWA 功能

### Service Worker
项目使用 Serwist 管理 Service Worker：

```typescript
// lib/sw.ts
import { defaultCache } from '@serwist/next/worker'
import type { PrecacheEntry } from '@serwist/precaching'
import { installSerwist } from '@serwist/sw'

declare const self: ServiceWorkerGlobalScope & {
  __SW_MANIFEST: (PrecacheEntry | string)[]
}

installSerwist({
  precacheEntries: self.__SW_MANIFEST,
  skipWaiting: true,
  clientsClaim: true,
  runtimeCaching: defaultCache,
})
```

### 离线支持
- 自动缓存静态资源
- 产品页面离线浏览
- 购物车本地存储

## 🚀 部署指南

### 构建生产版本

```bash
# 1. 安装依赖
yarn install

# 2. 生成代码
yarn codegen

# 3. 构建应用
yarn build

# 4. 启动生产服务器
yarn start
```

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
# Magento 配置
MAGENTO_ENDPOINT=https://your-magento.com/graphql
MAGENTO_STORE_CODE=default

# Hygraph 配置
HYGRAPH_ENDPOINT=https://your-hygraph.com

# 其他配置
CANONICAL_BASE_URL=https://your-domain.com
GOOGLE_ANALYTICS_ID=GA-XXXXXXX
GOOGLE_RECAPTCHA_KEY=your-recaptcha-key
```

### ☁️ Vercel 部署（推荐）

**Vercel 是 GraphCommerce 官方推荐的部署平台**，提供零配置的托管体验：

#### 🚀 快速部署步骤

1. **连接 GitHub 仓库**：
   - 访问 [Vercel 仪表板](https://vercel.com/dashboard)
   - 点击 "New Project" 导入你的 GitHub 仓库

2. **自动构建配置**：
   ```json
   {
     "buildCommand": "yarn build",
     "outputDirectory": ".next",
     "installCommand": "yarn install"
   }
   ```

3. **环境变量配置**：
   在 Vercel 项目设置中添加以下环境变量：
   ```bash
   MAGENTO_ENDPOINT=https://your-magento.com/graphql
   HYGRAPH_ENDPOINT=https://your-hygraph.com
   CANONICAL_BASE_URL=https://your-domain.com
   ```

4. **自动部署**：
   - 每次推送到主分支自动触发部署
   - 预览部署支持 Pull Request

#### ⚡ Vercel 优势

- **🔧 零配置**：无需复杂的服务器配置
- **⚡ 边缘计算**：全球 CDN 加速
- **🔄 自动部署**：Git 集成，自动 CI/CD
- **📊 性能监控**：内置性能分析工具
- **🌐 自定义域名**：免费 SSL 证书

#### 📱 Vercel CLI 部署

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署到生产环境
vercel --prod

# 查看部署状态
vercel list
```

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN yarn install --frozen-lockfile

COPY . .
RUN yarn build

EXPOSE 3000
CMD ["yarn", "start"]
```

## 🔍 调试与故障排除

### 常见问题

1. **GraphQL 查询失败**
   - 检查 Magento/Hygraph 端点配置
   - 验证 API 密钥和权限
   - 查看网络请求日志

2. **构建失败**
   - 运行 `yarn codegen` 重新生成类型
   - 检查 TypeScript 错误
   - 清理 `.next` 缓存目录

3. **样式问题**
   - 检查 Material-UI 主题配置
   - 验证 Emotion 样式语法
   - 查看浏览器开发者工具

### 调试工具

```bash
# TypeScript 性能分析
yarn tsc:perf

# GraphQL Playground
http://localhost:3000/api/graphql

# Apollo Client DevTools
# 安装浏览器扩展：Apollo Client Devtools
```

## 🔌 GraphCommerce 高级功能

### GraphCommerce 插件系统

通过插件系统，你可以扩展 GraphCommerce 的功能，从简单到高度复杂的功能，同时确保与未来升级的兼容性：

```typescript
// plugins/my-custom-plugin/index.ts
import { PluginConfig } from '@graphcommerce/next-config'

const config: PluginConfig = {
  type: 'component',
  module: './MyCustomComponent',
}

export default config
```

### 🎯 SEO 优化

GraphCommerce 内置了强大的 SEO 功能：

- **🔍 自动站点地图生成**：基于产品和页面自动生成
- **📱 结构化数据**：自动添加 JSON-LD 结构化数据
- **⚡ Core Web Vitals 优化**：针对 Google 页面体验指标优化
- **🌐 多语言 SEO**：hreflang 标签自动处理

### 🧡 Magento 扩展集成

GraphCommerce 支持任何与 GraphQL 兼容的 Magento 扩展：

1. **检查 GraphQL 兼容性**：确保 Magento 扩展提供 GraphQL API
2. **更新 GraphQL Schema**：运行 `yarn codegen` 更新类型定义
3. **创建对应组件**：为新功能创建前端组件
4. **测试集成**：确保前后端数据流通畅

### 🔮 自定义 Hygraph 组件

构建自定义 Hygraph 组件以展示特定内容：

```tsx
// components/CustomHygraphComponent/index.tsx
import { HygraphComponent } from '@graphcommerce/hygraph-ui'

interface CustomComponentProps {
  title: string
  description: string
  image: { url: string }
}

export function CustomHygraphComponent(props: CustomComponentProps) {
  return (
    <HygraphComponent>
      <img src={props.image.url} alt={props.title} />
      <h2>{props.title}</h2>
      <p>{props.description}</p>
    </HygraphComponent>
  )
}
```

## 📚 学习资源

### 🎓 官方文档路径

按照以下顺序学习 GraphCommerce：

1. **🎉 快速开始**：[Getting Started](https://graphcommerce.org/docs/getting-started)
2. **⚙️ 配置**：[Configuration](https://graphcommerce.org/docs/framework/config)
3. **🌎 国际化**：[Internationalization](https://graphcommerce.org/docs/framework/internationalization)
4. **✅ 定制化**：[Customization](https://graphcommerce.org/docs/getting-started/start-building)
5. **⭐️ SEO 优化**：[SEO Guide](https://graphcommerce.org/docs/framework/seo)

### 📖 核心文档资源
- [GraphCommerce 官方文档](https://graphcommerce.org/docs)
- [Next.js 文档](https://nextjs.org/docs)
- [Material-UI 文档](https://mui.com/)
- [Apollo Client 文档](https://www.apollographql.com/docs/react/)
- [Hygraph 文档](https://hygraph.com/docs)

### 🌟 社区资源
- [GraphCommerce Slack 社区](https://join.slack.com/t/graphcommerce)
- [GitHub 仓库](https://github.com/graphcommerce-org/graphcommerce)
- [示例项目画廊](https://graphcommerce.org/gallery)

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交变更：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 TypeScript 类型安全原则
- 编写单元测试（使用 Jest + React Testing Library）
- 添加 JSDoc 注释

## 📄 许可证

本项目使用 ELv2 许可证。详见 [LICENSE.md](./LICENSE.md) 文件。

---

## 📖 升级指南

### 🔄 升级到最新版本

GraphCommerce 定期发布更新，包含新功能、性能改进和安全修复：

#### 检查当前版本
```bash
# 查看当前 GraphCommerce 版本
yarn list @graphcommerce/cli

# 查看可用更新
yarn outdated @graphcommerce/*
```

#### 升级步骤

1. **备份项目**：
   ```bash
   git add .
   git commit -m "Backup before upgrade"
   ```

2. **升级 GraphCommerce 包**：
   ```bash
   # 升级所有 GraphCommerce 相关包
   yarn upgrade @graphcommerce/cli @graphcommerce/next-config
   
   # 或升级到特定版本
   yarn add @graphcommerce/cli@latest
   ```

3. **更新代码生成**：
   ```bash
   yarn codegen
   ```

4. **测试升级**：
   ```bash
   yarn build
   yarn start
   ```

#### 🔍 升级注意事项

- **📋 查看 CHANGELOG**：阅读 [发布说明](https://github.com/graphcommerce-org/graphcommerce/releases)
- **🧪 测试功能**：在升级后全面测试核心功能
- **📦 依赖兼容性**：检查第三方依赖的兼容性

## 🚦 故障排除指南

### 常见问题快速解决

#### 1. **GraphQL 查询失败** ❌
```bash
# 检查 GraphQL 端点连接
curl -X POST https://your-magento.com/graphql \
  -H "Content-Type: application/json" \
  -d '{"query":"{ __typename }"}'

# 重新生成 GraphQL 类型
yarn codegen
```

**解决方案**：
- 验证 `graphcommerce.config.js` 中的端点配置
- 检查 Magento/Hygraph API 密钥和权限
- 查看浏览器网络请求日志

#### 2. **构建失败** 🔨
```bash
# 清理缓存
rm -rf .next
rm -rf node_modules/.cache

# 重新安装依赖
yarn install

# 重新生成代码
yarn codegen

# 尝试构建
yarn build
```

#### 3. **TypeScript 错误** 📝
```bash
# 检查 TypeScript 配置
yarn tsc:lint

# 更新类型定义
yarn codegen

# 检查依赖版本兼容性
yarn audit
```

#### 4. **样式问题** 🎨
- 检查 `components/theme.ts` 配置
- 验证 Material-UI 主题设置
- 使用浏览器开发者工具调试样式

#### 5. **国际化问题** 🌐
```bash
# 重新提取翻译文本
yarn lingui

# 检查 locale 文件
ls locales/

# 验证 lingui 配置
cat lingui.config.js
```

### 🔧 调试工具集

```bash
# GraphQL Playground
http://localhost:3000/api/graphql

# TypeScript 性能分析
yarn tsc:perf

# 开发模式日志
DEBUG=* yarn dev

# 构建分析
ANALYZE=true yarn build
```

### 📱 浏览器调试

推荐安装以下浏览器扩展：
- **Apollo Client DevTools** - GraphQL 查询调试
- **React Developer Tools** - React 组件调试
- **Lighthouse** - 性能和 SEO 分析

## 🆘 获取帮助

### 📞 技术支持渠道

1. **🚦 故障排除文档**：[官方故障排除指南](https://graphcommerce.org/docs/framework/troubleshooting)
2. **🔍 GitHub Issues**：[搜索已知问题](https://github.com/graphcommerce-org/graphcommerce/issues)
3. **💬 Slack 社区**：[加入 GraphCommerce Slack](https://join.slack.com/t/graphcommerce)
4. **📖 官方文档**：[完整文档库](https://graphcommerce.org/docs)

### 💡 提问技巧

在寻求帮助时，请提供：
- **环境信息**：Node.js、Yarn、GraphCommerce 版本
- **错误日志**：完整的错误信息和堆栈跟踪
- **复现步骤**：详细的操作步骤
- **配置文件**：相关的配置文件内容

---

**Happy Coding! 🎉**

*GraphCommerce - 为开发者打造的卓越电商框架* 