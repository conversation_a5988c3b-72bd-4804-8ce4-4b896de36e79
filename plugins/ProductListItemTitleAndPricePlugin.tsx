import type { PluginConfig } from '@graphcommerce/next-config'
import type { ProductListItemTitleAndPriceProps } from '@graphcommerce/magento-product'
import { productListPrice } from '@graphcommerce/magento-product'
import { Box, Typography } from '@mui/material'

export const config: PluginConfig = {
  type: 'replace',
  module: '@graphcommerce/magento-product',
}

// Replace the original ProductListItemTitleAndPrice component with vertical layout
// Keep all original functionality, only change the layout from grid to vertical flex
// Enhanced price colors to highlight discounts
export function ProductListItemTitleAndPrice(props: ProductListItemTitleAndPriceProps) {
  const { titleComponent = 'h2', classes, children, subTitle, title, sx } = props

  return (
    <Box
      className={classes.titleContainer}
      sx={[
        (theme) => ({
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          marginTop: theme.spacings.xs,
          gap: theme.spacing(0.25), // Smaller gap for tighter layout
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      {/* Product Title - Now on top */}
      <Typography
        component={titleComponent}
        variant='subtitle2'
        sx={{
          color: 'text.primary',
          overflowWrap: 'break-word',
          maxWidth: '100%',
          lineHeight: 1.3,
          letterSpacing: '0.02em',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          height: `calc(1.3em * 2)`,
        }}
        className={classes.title}
      >
        {title}
      </Typography>

      {/* Subtitle - In the middle if exists */}
      {subTitle && (
        <Box className={classes.subtitle}>
          {subTitle}
        </Box>
      )}

      {/* Product Price - Enhanced colors only to highlight discounts */}
      <Box
        className={productListPrice.classes.root}
        sx={{
          // Keep original styling, only enhance colors
          textAlign: 'left', // Changed to left align for better vertical layout
          width: '100%',
          fontSize: '1.3rem', // Restored font size
          paddingTop: '5px',
          letterSpacing: '0.02em',
          fontWeight: 500,
          '& .ProductListPrice-finalPrice': {
            // Current price - red color when there's a discount
            fontSize: '1.3rem', // Match the outer container font size
            color: 'error.main', // Red color to highlight discount
            fontWeight: 500,
          },
          // If no discount, keep normal color
          '&:not(:has(.ProductListPrice-discountPrice)) .ProductListPrice-finalPrice': {
            color: 'text.primary',
          },
        }}
      >
        {children}
      </Box>
    </Box>
  )
} 