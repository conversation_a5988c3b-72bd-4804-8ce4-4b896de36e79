// @ts-check

/**
 * Docs: https://graphcommerce.org/docs/framework/config
 *
 * @type {import('@graphcommerce/next-config/src/generated/config').GraphCommerceConfig}
 */
const config = {
  robotsAllow: false,
  limitSsg: true,
  debug: { pluginStatus: true },

  hygraphEndpoint: 'https://eu-west-2.cdn.hygraph.com/content/cmbyj6btm03te07wa7a7owwo4/master',
  magentoEndpoint: 'https://newweb.durovinbathroom.co.uk/graphql',
  imagecdnurl: 'https://newweb.durovinbathroom.co.uk',
  // magentoEndpoint: 'http://magelocal.com/graphql',
  // imagecdnurl: 'http://magelocal.com',
  magentoVersion: 247,
  canonicalBaseUrl: 'https://graphcommerce.vercel.app',
  storefront: [
    {
      locale: 'en',
      magentoStoreCode: 'en_GB',
      defaultLocale: true,
      googleAnalyticsId: undefined,
      googleRecaptchaKey: undefined,
    },
  ],
  recentlyViewedProducts: { enabled: true },
  productFiltersPro: true,
  productFiltersLayout: 'SIDEBAR',
  containerSizingContent: 'BREAKPOINT',
  containerSizingShell: 'FULL_WIDTH',
  breadcrumbs: true,
  // previewSecret: '123',

  compare: true,
  compareVariant: 'ICON',
  // customerDeleteEnabled: false,

  permissions: { cart: 'ENABLED', checkout: 'ENABLED', customerAccount: 'ENABLED' },
  // customerCompanyFieldsEnable: false,
  // customerAddressNoteEnable: false,
  enableGuestCheckoutLogin: true,
  // dataLayer: { coreWebVitals: false },
  wishlistHideForGuests: true,

  // googleAnalyticsId: undefined,
  // googlePlaystore: undefined,
  // googleRecaptchaKey: undefined,
  // googleTagmanagerId: undefined,

  configurableVariantForSimple: true,
  configurableVariantValues: { content: false, gallery: true, url: true },

  // containerSizingContent: 'FULL_WIDTH',
  // containerSizingShell: 'FULL_WIDTH',
  // demoMode: true,
  // breadcrumbs: false,
}

module.exports = config
