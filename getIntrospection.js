const fs = require('fs');
const { getIntrospectionQuery, buildClientSchema, printSchema } = require('graphql');

const endpoint = 'https://newweb.durovinbathroom.co.uk/graphql';

async function main() {
  // console.log(`Fetching schema from ${endpoint}...`);
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: getIntrospectionQuery() }),
    });

    const responseJson = await response.json();

    if (responseJson.errors) {
      console.error('Error fetching introspection query:', JSON.stringify(responseJson.errors, null, 2));
      return;
    }

    if (!responseJson.data) {
      console.error('No data in response:', JSON.stringify(responseJson, null, 2));
      return;
    }

    const schema = buildClientSchema(responseJson.data);
    const sdl = printSchema(schema);

    fs.writeFileSync('magento-schema.graphql', sdl);
    // console.log('Magento schema has been successfully saved to magento-schema.graphql');
  } catch (error) {
    console.error('An error occurred while fetching the schema:', error);
  }
}

main(); 